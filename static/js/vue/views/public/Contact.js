// Contact.js - Vue.js component converted from .vue file
import { ref } from 'vue'
import PublicNavigation from '../../components/public/PublicNavigation.js'
import PublicFooter from '../../components/public/PublicFooter.js'

export default {
  name: 'Contact',
  components: {
    PublicNavigation,
    PublicFooter
  },
  template: `
    <div class="min-h-screen bg-white dark:bg-gray-900">
      <PublicNavigation />

      <!-- Hero Section -->
      <section class="bg-brand-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 class="text-4xl font-bold mb-4">
            Contattaci
          </h1>
          <p class="text-xl text-brand-primary-100">
            Siamo qui per aiutarti
          </p>
        </div>
      </section>

      <!-- Contact Form & Info -->
      <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div>
              <h2 class="text-2xl font-bold text-brand-text-primary mb-6">
                Invia un messaggio
              </h2>
              <form @submit.prevent="submitForm" class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-brand-text-secondary mb-2">
                    Nome
                  </label>
                  <input
                    v-model="form.name"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-brand-text-secondary mb-2">
                    Email
                  </label>
                  <input
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-brand-text-secondary mb-2">
                    Messaggio
                  </label>
                  <textarea
                    v-model="form.message"
                    rows="4"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500"
                  ></textarea>
                </div>
                <button
                  type="submit"
                  :disabled="isSubmitting"
                  class="w-full bg-brand-primary-600 text-white py-2 px-4 rounded-md hover:bg-brand-primary-700 transition-colors disabled:opacity-50"
                >
                  {{ isSubmitting ? 'Invio...' : 'Invia messaggio' }}
                </button>
              </form>
            </div>

            <!-- Contact Info -->
            <div>
              <h2 class="text-2xl font-bold text-brand-text-primary mb-6">
                Informazioni di contatto
              </h2>
              <div class="space-y-4">
                <div class="flex items-center">
                  <i class="fas fa-map-marker-alt text-brand-primary-500 mr-3"></i>
                  <span>Via Roma 123, 00100 Roma</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-phone text-brand-primary-500 mr-3"></i>
                  <span>+39 06 1234567</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-envelope text-brand-primary-500 mr-3"></i>
                  <span><EMAIL></span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-clock text-brand-primary-500 mr-3"></i>
                  <span>Lun-Ven 9:00-18:00</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <PublicFooter />
    </div>
  `,
  setup() {
    const form = ref({
      name: '',
      email: '',
      message: ''
    })

    const isSubmitting = ref(false)

    async function submitForm() {
      isSubmitting.value = true
      try {
        // Simulate form submission
        await new Promise(resolve => setTimeout(resolve, 1000))
        alert('Messaggio inviato!')
        form.value = { name: '', email: '', message: '' }
      } catch (error) {
        alert('Errore nell\'invio')
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      form,
      isSubmitting,
      submitForm
    }
  }
}
