// About.js - Vue.js component converted from .vue file
import PublicNavigation from '../../components/public/PublicNavigation.js'
import PublicFooter from '../../components/public/PublicFooter.js'

export default {
  name: 'About',
  components: {
    PublicNavigation,
    PublicFooter
  },
  template: `
    <div class="min-h-screen bg-white dark:bg-gray-900">
      <PublicNavigation />

      <!-- Hero Section -->
      <section class="bg-brand-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 class="text-4xl font-bold mb-4">
            Chi Siamo
          </h1>
          <p class="text-xl text-brand-primary-100">
            La nostra storia e i nostri valori
          </p>
        </div>
      </section>

      <!-- Content -->
      <section class="py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="prose prose-lg max-w-none">
            <h2>La nostra storia</h2>
            <p>DatPortal nasce dalla passione per l'innovazione e la tecnologia. Siamo un team di esperti dedicati a fornire soluzioni all'avanguardia per le aziende moderne.</p>

            <h2>La nostra missione</h2>
            <p>La nostra missione è supportare le aziende nel loro percorso di trasformazione digitale, fornendo strumenti e servizi che migliorano l'efficienza e la produttività.</p>

            <h2>La nostra visione</h2>
            <p>Vogliamo essere il partner di riferimento per le aziende che desiderano innovare e crescere nel mercato digitale, offrendo soluzioni personalizzate e di alta qualità.</p>
          </div>
        </div>
      </section>

      <PublicFooter />
    </div>
  `
}
