/**
 * Vue.js 3 Main Application Entry Point
 * DatPortal SPA - Test Version
 */

console.log('🚀 Loading Vue.js main.js...')

// Test if Vue.js is available
try {
    const { createApp } = await import('vue')
    console.log('✅ Vue.js imported successfully')

    const { createRouter, createWebHistory } = await import('vue-router')
    console.log('✅ Vue Router imported successfully')

    const { createPinia } = await import('pinia')
    console.log('✅ Pinia imported successfully')

    // Simple test component
    const TestComponent = {
        template: `
            <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">🎉 Vue.js Funziona!</h1>
                    <p class="text-lg text-gray-600 mb-8">DatPortal SPA è operativo</p>
                    <div class="space-x-4">
                        <router-link to="/test" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                            Test Route
                        </router-link>
                        <a href="/auth/login" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600">
                            Login
                        </a>
                    </div>
                    <div class="mt-8 text-sm text-gray-500">
                        <p>Utente: {{ userInfo }}</p>
                        <p>Autenticato: {{ isAuthenticated }}</p>
                    </div>
                </div>
            </div>
        `,
        data() {
            return {
                userInfo: window.APP_CONFIG?.user?.username || 'Non disponibile',
                isAuthenticated: window.APP_CONFIG?.isAuthenticated || false
            }
        }
    }

    const TestPage = {
        template: `
            <div class="min-h-screen bg-blue-50 flex items-center justify-center">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-blue-900 mb-4">Test Page</h1>
                    <p class="text-lg text-blue-600 mb-8">Routing funziona correttamente!</p>
                    <router-link to="/" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                        Torna alla Home
                    </router-link>
                </div>
            </div>
        `
    }

    // Simple routes for testing
    const routes = [
        { path: '/', component: TestComponent },
        { path: '/test', component: TestPage },
        { path: '/:pathMatch(.*)*', redirect: '/' }
    ]

    const router = createRouter({
        history: createWebHistory(),
        routes
    })

    // Create Pinia store
    const pinia = createPinia()

    // App Vue.js con router e Pinia
    const vueApp = createApp({
        template: '<router-view />'
    })

    vueApp.use(router)
    vueApp.use(pinia)

    // Mount the app
    vueApp.mount('#app')

    // Make it available globally for debugging
    window.vueApp = vueApp

    console.log('🎉 Vue.js SPA con router e Pinia caricato con successo!')

} catch (error) {
    console.error('❌ Errore durante il caricamento di Vue.js:', error)

    // Fallback: show error message
    document.getElementById('app').innerHTML = `
        <div class="min-h-screen bg-red-50 flex items-center justify-center">
            <div class="text-center">
                <h1 class="text-2xl font-bold text-red-900 mb-4">Errore Vue.js</h1>
                <p class="text-red-600 mb-4">Impossibile caricare l'applicazione Vue.js</p>
                <p class="text-sm text-red-500">${error.message}</p>
                <a href="/auth/login" class="mt-4 inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                    Vai al Login
                </a>
            </div>
        </div>
    `
}
