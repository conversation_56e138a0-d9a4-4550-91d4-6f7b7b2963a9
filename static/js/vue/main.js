// MAIN.JS SEMPLICE - USA ROUTER ESISTENTE
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from './router/index.js'

console.log('🚀 Caricando Vue.js con router esistente...')

// Crea router con le route esistenti
const router = createRouter({
    history: createWebHistory(),
    routes
})

// Crea app semplice
const app = createApp({
    template: '<router-view />'
})

app.use(router)
app.mount('#app')

console.log('✅ Vue.js con router completo caricato!')
window.vueApp = app
