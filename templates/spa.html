<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DatPortal</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- CSS -->
    <link href="{{ url_for('static', filename='css/brand-variables.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/tailwind.css') }}" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js for charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Import Maps for ES6 modules -->
    <script type="importmap">
    {
        "imports": {
            "vue": "https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.prod.js",
            "vue-router": "https://unpkg.com/vue-router@4.2.0/dist/vue-router.esm-browser.js",
            "pinia": "https://unpkg.com/pinia@2.1.0/dist/pinia.esm-browser.js",
            "axios": "https://unpkg.com/axios@1.6.0/dist/esm/axios.min.js"
        }
    }
    </script>

    <!-- Meta tags for SEO -->
    <meta name="description" content="DatPortal - Sistema di gestione progetti, task e risorse">
    <meta name="keywords" content="progetti, task, gestione, risorse, KPI, dashboard">
    <meta name="author" content="DatVinci">

    <!-- Open Graph meta tags -->
    <meta property="og:title" content="DatPortal">
    <meta property="og:description" content="Sistema di gestione progetti, task e risorse">
    <meta property="og:type" content="website">

    <!-- CSRF Token for API requests -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <!-- Vue.js App Container -->
    <div id="app">
        <!-- PAGINA CHE FUNZIONA SEMPRE -->
        <div class="min-h-screen bg-blue-50 flex items-center justify-center">
            <div class="text-center">
                <h1 class="text-6xl font-bold text-blue-900 mb-6">🎉 FUNZIONA!</h1>
                <p class="text-2xl text-blue-600 mb-8">DatPortal è OPERATIVO</p>
                <div class="space-x-4">
                    <a href="/auth/login" class="bg-blue-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-blue-600">
                        VAI AL LOGIN
                    </a>
                    <button onclick="alert('Vue.js sarà qui presto!')" class="bg-green-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-green-600">
                        TEST BUTTON
                    </button>
                </div>
                <div class="mt-8 text-lg text-blue-500">
                    <p>✅ Flask: OK</p>
                    <p>✅ Template: OK</p>
                    <p>✅ CSS: OK</p>
                    <p>⏳ Vue.js: In arrivo...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Global Configuration for Vue.js -->
    <script>
        // Global app configuration
        window.APP_CONFIG = {
            apiUrl: '/api',
            baseUrl: '{{ request.url_root }}',
            csrfToken: '{{ csrf_token() }}',
            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},
            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},
            version: '1.0.0',
            environment: '{{ config.ENV }}',
            debug: {{ 'true' if config.DEBUG else 'false' }}
        };

        // Configure Axios defaults
        if (typeof axios !== 'undefined') {
            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;
            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;
            axios.defaults.headers.common['Content-Type'] = 'application/json';

            // Add request interceptor for authentication
            axios.interceptors.request.use(function (config) {
                // Add timestamp to prevent caching
                config.params = config.params || {};
                config.params._t = Date.now();
                return config;
            });

            // Add response interceptor for error handling
            axios.interceptors.response.use(
                function (response) {
                    return response;
                },
                function (error) {
                    if (error.response && error.response.status === 401) {
                        // Redirect to login if unauthorized
                        window.location.href = '/auth/login';
                    }
                    return Promise.reject(error);
                }
            );
        }

        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            // You can send errors to a logging service here
        });

        // Global unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            // You can send errors to a logging service here
        });
    </script>

    <!-- Test Script First -->
    <script>
        console.log('🔥 TEST: Script caricato!');

        // Test immediato - sostituisce il loading spinner
        setTimeout(() => {
            console.log('🔥 TEST: Sostituendo loading spinner...');
            document.getElementById('app').innerHTML = `
                <div class="min-h-screen bg-green-50 flex items-center justify-center">
                    <div class="text-center">
                        <h1 class="text-4xl font-bold text-green-900 mb-4">✅ JAVASCRIPT FUNZIONA!</h1>
                        <p class="text-lg text-green-600 mb-8">Il problema non è JavaScript</p>
                        <button onclick="loadVue()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                            Carica Vue.js
                        </button>
                    </div>
                </div>
            `;
        }, 1000);

        // Funzione per caricare Vue.js
        window.loadVue = function() {
            console.log('🚀 Caricando Vue.js...');
            const script = document.createElement('script');
            script.type = 'module';
            script.src = '{{ url_for("static", filename="js/vue/main.js") }}';
            script.onload = () => console.log('✅ Vue.js script caricato');
            script.onerror = (e) => console.error('❌ Errore caricamento Vue.js:', e);
            document.head.appendChild(script);
        }
    </script>

    <!-- Vue.js Application Entry Point -->
    <!-- <script type="module" src="{{ url_for('static', filename='js/vue/main.js') }}"></script> -->



    <!-- Fallback for browsers that don't support import maps -->
    <script nomodule>
        document.getElementById('app').innerHTML = `
            <div class="min-h-screen flex items-center justify-center bg-gray-50">
                <div class="text-center">
                    <h1 class="text-2xl font-bold text-gray-900 mb-4">Browser non supportato</h1>
                    <p class="text-gray-600 mb-4">Il tuo browser non supporta le Import Maps.</p>
                    <p class="text-gray-600">Aggiorna il tuo browser per utilizzare DatPortal.</p>
                </div>
            </div>
        `;
    </script>
</body>
</html>
