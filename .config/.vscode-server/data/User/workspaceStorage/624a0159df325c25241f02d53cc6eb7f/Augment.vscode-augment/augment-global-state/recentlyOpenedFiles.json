[["/home/<USER>/workspace/templates/projects/index.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/index.html"}}], ["/home/<USER>/workspace/seed_data.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "seed_data.py"}}], ["/home/<USER>/workspace/templates/projects/view.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/view.html"}}], ["/home/<USER>/workspace/templates/dashboard/index.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/dashboard/index.html"}}], ["/home/<USER>/workspace/templates/components/toast.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/components/toast.html"}}], ["/home/<USER>/workspace/db_update_cost_management.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "db_update_cost_management.py"}}], ["/home/<USER>/workspace/add_task_fields.sql", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "add_task_fields.sql"}}], ["/home/<USER>/workspace/specs/tasks.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/tasks.md"}}], ["/home/<USER>/workspace/db_update_cv.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "db_update_cv.py"}}], ["/home/<USER>/workspace/migrate_skills.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "migrate_skills.py"}}], ["/home/<USER>/workspace/create_complete_profiles.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "create_complete_profiles.py"}}], ["/home/<USER>/workspace/specs/todo_7_26_maggio.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/todo_7_26_maggio.txt"}}], ["/home/<USER>/workspace/specs/todo_7.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/todo_7.txt"}}], ["/home/<USER>/workspace/specs/prd.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/prd.md"}}], ["/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.2.2/out/activation/setup-toolbar.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.2.2/out/activation/setup-toolbar.js"}}], ["/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/624a0159df325c25241f02d53cc6eb7f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/b78c4e9e-00fa-421d-9c6d-2d78753d686b/document-_home_runner_workspace_templates_personnel_departments_dashboard.html-1748291618066-5aa87630-4056-43bf-9c3e-1564dbbad9b7.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/624a0159df325c25241f02d53cc6eb7f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/b78c4e9e-00fa-421d-9c6d-2d78753d686b/document-_home_runner_workspace_templates_personnel_departments_dashboard.html-1748291618066-5aa87630-4056-43bf-9c3e-1564dbbad9b7.json"}}], ["/home/<USER>/workspace/db_update.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "db_update.py"}}], ["/home/<USER>/workspace/templates/components/sidebar.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/components/sidebar.html"}}], ["/home/<USER>/workspace/templates/personnel/profile.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/personnel/profile.html"}}], ["/home/<USER>/workspace/templates/components/navbar.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/components/navbar.html"}}], ["/home/<USER>/workspace/templates/projects/create.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/create.html"}}], ["/home/<USER>/workspace/templates/landing/services.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/services.html"}}], ["/home/<USER>/workspace/templates/landing/home.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/landing/home.html"}}], ["/home/<USER>/workspace/static/js/utils.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/utils.js"}}], ["/home/<USER>/workspace/static/js/theme.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/theme.js"}}], ["/home/<USER>/workspace/static/js/alpine-init.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/alpine-init.js"}}], ["/home/<USER>/workspace/static/js/charts.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/charts.js"}}], ["/home/<USER>/workspace/static/js/components.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/components.js"}}], ["/home/<USER>/workspace/static/js/spa-navigation.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/spa-navigation.js"}}], ["/home/<USER>/workspace/fix_spa_buttons.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "fix_spa_buttons.py"}}], ["/home/<USER>/workspace/scripts/seed_kpi_templates.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/seed_kpi_templates.py"}}], ["/home/<USER>/workspace/.config/.vscode-server/data/User/History/-16c31df8/GVyQ.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-16c31df8/GVyQ.js"}}], ["/home/<USER>/workspace/specs/task_7_status.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/task_7_status.txt"}}], ["/home/<USER>/workspace/blueprints/reporting.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/reporting.py"}}], ["/home/<USER>/workspace/blueprints/projects.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/projects.py"}}], ["/home/<USER>/workspace/blueprints/products.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/products.py"}}], ["/home/<USER>/workspace/blueprints/funding.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/funding.py"}}], ["/home/<USER>/workspace/blueprints/communications.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/communications.py"}}], ["/home/<USER>/workspace/blueprints/crm.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/crm.py"}}], ["/home/<USER>/workspace/blueprints/dashboard.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/dashboard.py"}}], ["/home/<USER>/workspace/blueprints/performance.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/performance.py"}}], ["/home/<USER>/workspace/scripts/task-complexity-report.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/task-complexity-report.json"}}], ["/home/<USER>/workspace/static/swagger/swagger.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/swagger/swagger.json"}}], ["/home/<USER>/workspace/static/js/vue/stores/brand.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/brand.js"}}], ["/home/<USER>/workspace/static/js/vue/stores/auth.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/auth.js"}}], ["/home/<USER>/workspace/static/js/vue/stores/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/app.js"}}], ["/home/<USER>/workspace/docs/css-architecture.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/css-architecture.md"}}], ["/home/<USER>/workspace/static/js/vue/components/ui/ModalContainer.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/ModalContainer.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/ui/LoadingOverlay.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/LoadingOverlay.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/public/PublicNavigation.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicNavigation.vue"}}], ["/home/<USER>/workspace/config/tenant_config.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "config/tenant_config.json"}}], ["/home/<USER>/workspace/static/js/vue/views/Dashboard.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/Dashboard.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Services.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Services.vue"}}], ["/home/<USER>/workspace/blueprints/landing.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/landing.py"}}], ["/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/task_16_vue_refactoring.txt"}}], ["/home/<USER>/workspace/templates/base.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}}], ["/home/<USER>/workspace/main.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "main.py"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Home.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Home.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/App.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/App.vue"}}], ["/home/<USER>/workspace/static/js/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/app.js"}}], ["/home/<USER>/workspace/app.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "app.py"}}], ["/home/<USER>/workspace/static/js/vue/main.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}}], ["/home/<USER>/workspace/templates/spa.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/spa.html"}}], ["/home/<USER>/workspace/static/js/vue/router/index.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/router/index.js"}}]]