{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Privacy.vue"}, "originalCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <!-- Hero Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h1 class=\"text-4xl font-bold mb-4\">\n          {{ tenantStore.getPageContent('privacy', 'hero', 'title') || 'Privacy Policy' }}\n        </h1>\n        <p class=\"text-xl text-brand-primary-100\">\n          {{ tenantStore.getPageContent('privacy', 'hero', 'subtitle') || 'Informativa sulla privacy' }}\n        </p>\n      </div>\n    </section>\n\n    <!-- Privacy Content -->\n    <section class=\"py-16\">\n      <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"prose prose-lg max-w-none\">\n          <p class=\"text-sm text-gray-500 mb-8\">\n            {{ tenantStore.getPageContent('privacy', 'last_updated') || 'Ultimo aggiornamento: 1 novembre 2023' }}\n          </p>\n          \n          <div class=\"space-y-8\">\n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.title || 'Introduzione' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.content || 'Contenuto introduzione privacy...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.title || 'Raccolta dei dati' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.content || 'Contenuto raccolta dati...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.title || 'Utilizzo dei dati' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.content || 'Contenuto utilizzo dati...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.title || 'Contatti' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.content || 'Contenuto contatti privacy...' }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst tenantStore = useTenantStore()\n</script>\n", "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <!-- Hero Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h1 class=\"text-4xl font-bold mb-4\">\n          {{ tenantStore.getPageContent('privacy', 'hero', 'title') || 'Privacy Policy' }}\n        </h1>\n        <p class=\"text-xl text-brand-primary-100\">\n          {{ tenantStore.getPageContent('privacy', 'hero', 'subtitle') || 'Informativa sulla privacy' }}\n        </p>\n      </div>\n    </section>\n\n    <!-- Privacy Content -->\n    <section class=\"py-16\">\n      <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"prose prose-lg max-w-none\">\n          <p class=\"text-sm text-gray-500 mb-8\">\n            {{ tenantStore.getPageContent('privacy', 'last_updated') || 'Ultimo aggiornamento: 1 novembre 2023' }}\n          </p>\n          \n          <div class=\"space-y-8\">\n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.title || 'Introduzione' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'intro')?.content || 'Contenuto introduzione privacy...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.title || 'Raccolta dei dati' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_collection')?.content || 'Contenuto raccolta dati...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.title || 'Utilizzo dei dati' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'data_usage')?.content || 'Contenuto utilizzo dati...' }}</p>\n            </div>\n            \n            <div>\n              <h2>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.title || 'Contatti' }}</h2>\n              <p>{{ tenantStore.getPageContent('privacy', 'sections', 'contact')?.content || 'Contenuto contatti privacy...' }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst tenantStore = useTenantStore()\n</script>\n"}