{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/AppFooter.vue"}, "originalCode": "<template>\n  <footer class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto\">\n    <div class=\"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\">\n      <div class=\"flex flex-col md:flex-row md:items-center md:justify-between\">\n        <!-- Left Section -->\n        <div class=\"flex items-center space-x-4\">\n          <p class=\"text-sm text-brand-text-secondary\">\n            © {{ currentYear }} {{ brandStore.brandConfig.name }}. Tutti i diritti riservati.\n          </p>\n          <span class=\"hidden md:inline text-brand-text-tertiary\">|</span>\n          <p class=\"text-xs text-brand-text-tertiary\">\n            Versione {{ appVersion }}\n          </p>\n        </div>\n\n        <!-- Right Section -->\n        <div class=\"mt-2 md:mt-0 flex items-center space-x-4\">\n          <!-- Status Indicator -->\n          <div class=\"flex items-center space-x-2\">\n            <div \n              class=\"h-2 w-2 rounded-full\"\n              :class=\"appStore.isOnline ? 'bg-brand-success-500' : 'bg-brand-error-500'\"\n            ></div>\n            <span class=\"text-xs text-brand-text-tertiary\">\n              {{ appStore.isOnline ? 'Online' : 'Offline' }}\n            </span>\n          </div>\n\n          <!-- Links -->\n          <div class=\"flex items-center space-x-3 text-xs\">\n            <a \n              href=\"/privacy\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Privacy\n            </a>\n            <a \n              href=\"/terms\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Termini\n            </a>\n            <a \n              href=\"/support\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Supporto\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </footer>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useAppStore } from '../../stores/app.js'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Stores\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\n\n// Computed\nconst currentYear = computed(() => new Date().getFullYear())\nconst appVersion = computed(() => window.APP_CONFIG?.version || '1.0.0')\n</script>\n\n<style scoped>\n/* Footer styling */\nfooter {\n  flex-shrink: 0;\n}\n\n/* Link hover effects */\na:hover {\n  text-decoration: none;\n}\n\n/* Focus styles */\na:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .flex-col.md\\\\:flex-row {\n    text-align: center;\n  }\n  \n  .space-x-4 > * + * {\n    margin-left: 0.5rem;\n  }\n}\n\n/* Print styles */\n@media print {\n  footer {\n    display: none !important;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <footer class=\"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto\">\n    <div class=\"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\">\n      <div class=\"flex flex-col md:flex-row md:items-center md:justify-between\">\n        <!-- Left Section -->\n        <div class=\"flex items-center space-x-4\">\n          <p class=\"text-sm text-brand-text-secondary\">\n            © {{ currentYear }} {{ brandStore.brandConfig.name }}. Tutti i diritti riservati.\n          </p>\n          <span class=\"hidden md:inline text-brand-text-tertiary\">|</span>\n          <p class=\"text-xs text-brand-text-tertiary\">\n            Versione {{ appVersion }}\n          </p>\n        </div>\n\n        <!-- Right Section -->\n        <div class=\"mt-2 md:mt-0 flex items-center space-x-4\">\n          <!-- Status Indicator -->\n          <div class=\"flex items-center space-x-2\">\n            <div \n              class=\"h-2 w-2 rounded-full\"\n              :class=\"appStore.isOnline ? 'bg-brand-success-500' : 'bg-brand-error-500'\"\n            ></div>\n            <span class=\"text-xs text-brand-text-tertiary\">\n              {{ appStore.isOnline ? 'Online' : 'Offline' }}\n            </span>\n          </div>\n\n          <!-- Links -->\n          <div class=\"flex items-center space-x-3 text-xs\">\n            <a \n              href=\"/privacy\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Privacy\n            </a>\n            <a \n              href=\"/terms\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Termini\n            </a>\n            <a \n              href=\"/support\" \n              class=\"text-brand-text-tertiary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n            >\n              Supporto\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </footer>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useAppStore } from '../../stores/app.js'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Stores\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\n\n// Computed\nconst currentYear = computed(() => new Date().getFullYear())\nconst appVersion = computed(() => window.APP_CONFIG?.version || '1.0.0')\n</script>\n\n<style scoped>\n/* Footer styling */\nfooter {\n  flex-shrink: 0;\n}\n\n/* Link hover effects */\na:hover {\n  text-decoration: none;\n}\n\n/* Focus styles */\na:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .flex-col.md\\\\:flex-row {\n    text-align: center;\n  }\n  \n  .space-x-4 > * + * {\n    margin-left: 0.5rem;\n  }\n}\n\n/* Print styles */\n@media print {\n  footer {\n    display: none !important;\n  }\n}\n</style>\n"}