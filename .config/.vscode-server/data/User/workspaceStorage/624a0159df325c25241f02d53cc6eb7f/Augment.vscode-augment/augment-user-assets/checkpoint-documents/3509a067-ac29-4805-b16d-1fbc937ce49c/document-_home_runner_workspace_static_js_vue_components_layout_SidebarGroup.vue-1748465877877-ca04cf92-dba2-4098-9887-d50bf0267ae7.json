{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/SidebarGroup.vue"}, "originalCode": "<template>\n  <div class=\"space-y-1\">\n    <!-- Group Header -->\n    <button\n      @click=\"toggleGroup\"\n      class=\"group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150\"\n      :class=\"[\n        isActive \n          ? 'text-white bg-brand-primary-800 dark:bg-gray-700' \n          : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700',\n        collapsed ? 'justify-center' : ''\n      ]\"\n    >\n      <!-- Icon -->\n      <i \n        :class=\"[\n          icon,\n          collapsed ? 'mr-0' : 'mr-3',\n          'h-6 w-6 flex-shrink-0 transition-colors duration-150'\n        ]\"\n      ></i>\n      \n      <!-- Label -->\n      <span \n        class=\"truncate flex-1 text-left transition-all duration-150\"\n        :class=\"{ 'hidden': collapsed }\"\n      >\n        {{ label }}\n      </span>\n      \n      <!-- Expand/Collapse Icon -->\n      <i \n        v-if=\"!collapsed\"\n        class=\"fas fa-chevron-right ml-2 h-4 w-4 transition-transform duration-150\"\n        :class=\"{ 'rotate-90': isOpen }\"\n      ></i>\n      \n      <!-- Tooltip for collapsed state -->\n      <div \n        v-if=\"collapsed\"\n        class=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap\"\n      >\n        {{ label }}\n        <div class=\"absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full\">\n          <div class=\"border-4 border-transparent border-r-gray-900\"></div>\n        </div>\n      </div>\n    </button>\n\n    <!-- Submenu Items -->\n    <transition\n      name=\"submenu\"\n      @enter=\"onEnter\"\n      @leave=\"onLeave\"\n    >\n      <div \n        v-show=\"isOpen && !collapsed\" \n        class=\"ml-6 space-y-1 overflow-hidden\"\n      >\n        <router-link\n          v-for=\"item in items\"\n          :key=\"item.to\"\n          :to=\"item.to\"\n          class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n          :class=\"[\n            isItemActive(item.to)\n              ? 'text-white bg-brand-primary-700 dark:bg-gray-600'\n              : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700'\n          ]\"\n        >\n          <!-- Item Icon/Emoji -->\n          <span class=\"mr-2 text-sm\">{{ item.icon }}</span>\n          \n          <!-- Item Label -->\n          <span class=\"truncate\">{{ item.label }}</span>\n          \n          <!-- Badge (optional) -->\n          <span \n            v-if=\"item.badge\"\n            class=\"ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-secondary-500 text-white\"\n          >\n            {{ item.badge }}\n          </span>\n        </router-link>\n      </div>\n    </transition>\n\n    <!-- Mobile Submenu (when collapsed) -->\n    <div \n      v-if=\"collapsed && isOpen\"\n      class=\"fixed left-20 top-0 bg-white dark:bg-gray-800 shadow-lg rounded-md py-2 z-50 min-w-48\"\n      :style=\"{ top: mobileMenuTop + 'px' }\"\n    >\n      <router-link\n        v-for=\"item in items\"\n        :key=\"item.to\"\n        :to=\"item.to\"\n        class=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n        @click=\"closeMobileMenu\"\n      >\n        <span class=\"mr-2\">{{ item.icon }}</span>\n        {{ item.label }}\n      </router-link>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick } from 'vue'\nimport { useRoute } from 'vue-router'\n\n// Props\nconst props = defineProps({\n  icon: {\n    type: String,\n    required: true\n  },\n  label: {\n    type: String,\n    required: true\n  },\n  items: {\n    type: Array,\n    required: true\n  },\n  collapsed: {\n    type: Boolean,\n    default: false\n  },\n  defaultOpen: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// State\nconst isOpen = ref(props.defaultOpen)\nconst mobileMenuTop = ref(0)\n\n// Route\nconst route = useRoute()\n\n// Computed\nconst isActive = computed(() => {\n  return props.items.some(item => route.path.startsWith(item.to))\n})\n\n// Auto-open if current route matches any item\nif (isActive.value) {\n  isOpen.value = true\n}\n\n// Methods\nfunction toggleGroup() {\n  if (props.collapsed) {\n    // For collapsed sidebar, show mobile menu\n    isOpen.value = !isOpen.value\n    if (isOpen.value) {\n      nextTick(() => {\n        // Calculate position for mobile menu\n        const button = event.currentTarget\n        const rect = button.getBoundingClientRect()\n        mobileMenuTop.value = rect.top\n      })\n    }\n  } else {\n    // Normal toggle\n    isOpen.value = !isOpen.value\n  }\n}\n\nfunction isItemActive(itemTo) {\n  if (itemTo === '/') {\n    return route.path === '/'\n  }\n  return route.path.startsWith(itemTo)\n}\n\nfunction closeMobileMenu() {\n  if (props.collapsed) {\n    isOpen.value = false\n  }\n}\n\n// Animation hooks\nfunction onEnter(el) {\n  el.style.height = '0'\n  el.offsetHeight // force reflow\n  el.style.height = el.scrollHeight + 'px'\n}\n\nfunction onLeave(el) {\n  el.style.height = el.scrollHeight + 'px'\n  el.offsetHeight // force reflow\n  el.style.height = '0'\n}\n\n// Close mobile menu when clicking outside\nif (typeof window !== 'undefined') {\n  document.addEventListener('click', (event) => {\n    if (props.collapsed && isOpen.value) {\n      const mobileMenu = event.target.closest('.fixed')\n      const button = event.target.closest('button')\n      \n      if (!mobileMenu && !button) {\n        isOpen.value = false\n      }\n    }\n  })\n}\n</script>\n\n<style scoped>\n/* Submenu animations */\n.submenu-enter-active,\n.submenu-leave-active {\n  transition: height 0.3s ease;\n  overflow: hidden;\n}\n\n.submenu-enter-from,\n.submenu-leave-to {\n  height: 0;\n}\n\n/* Chevron rotation */\n.rotate-90 {\n  transform: rotate(90deg);\n}\n\n/* Mobile menu positioning */\n.fixed {\n  animation: slideIn 0.2s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Group hover effects */\n.group {\n  position: relative;\n}\n\n/* Active state indicator */\n.router-link-active {\n  position: relative;\n}\n\n.router-link-active::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 16px;\n  background-color: var(--brand-secondary-400);\n  border-radius: 0 2px 2px 0;\n}\n\n/* Focus styles */\nbutton:focus,\n.router-link-active:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-all,\n  .transition-colors,\n  .transition-transform,\n  .submenu-enter-active,\n  .submenu-leave-active {\n    transition: none;\n  }\n  \n  .fixed {\n    animation: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"space-y-1\">\n    <!-- Group Header -->\n    <button\n      @click=\"toggleGroup\"\n      class=\"group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-150\"\n      :class=\"[\n        isActive \n          ? 'text-white bg-brand-primary-800 dark:bg-gray-700' \n          : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700',\n        collapsed ? 'justify-center' : ''\n      ]\"\n    >\n      <!-- Icon -->\n      <i \n        :class=\"[\n          icon,\n          collapsed ? 'mr-0' : 'mr-3',\n          'h-6 w-6 flex-shrink-0 transition-colors duration-150'\n        ]\"\n      ></i>\n      \n      <!-- Label -->\n      <span \n        class=\"truncate flex-1 text-left transition-all duration-150\"\n        :class=\"{ 'hidden': collapsed }\"\n      >\n        {{ label }}\n      </span>\n      \n      <!-- Expand/Collapse Icon -->\n      <i \n        v-if=\"!collapsed\"\n        class=\"fas fa-chevron-right ml-2 h-4 w-4 transition-transform duration-150\"\n        :class=\"{ 'rotate-90': isOpen }\"\n      ></i>\n      \n      <!-- Tooltip for collapsed state -->\n      <div \n        v-if=\"collapsed\"\n        class=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap\"\n      >\n        {{ label }}\n        <div class=\"absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full\">\n          <div class=\"border-4 border-transparent border-r-gray-900\"></div>\n        </div>\n      </div>\n    </button>\n\n    <!-- Submenu Items -->\n    <transition\n      name=\"submenu\"\n      @enter=\"onEnter\"\n      @leave=\"onLeave\"\n    >\n      <div \n        v-show=\"isOpen && !collapsed\" \n        class=\"ml-6 space-y-1 overflow-hidden\"\n      >\n        <router-link\n          v-for=\"item in items\"\n          :key=\"item.to\"\n          :to=\"item.to\"\n          class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n          :class=\"[\n            isItemActive(item.to)\n              ? 'text-white bg-brand-primary-700 dark:bg-gray-600'\n              : 'text-brand-primary-100 hover:bg-brand-primary-600 dark:text-gray-300 dark:hover:bg-gray-700'\n          ]\"\n        >\n          <!-- Item Icon/Emoji -->\n          <span class=\"mr-2 text-sm\">{{ item.icon }}</span>\n          \n          <!-- Item Label -->\n          <span class=\"truncate\">{{ item.label }}</span>\n          \n          <!-- Badge (optional) -->\n          <span \n            v-if=\"item.badge\"\n            class=\"ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-secondary-500 text-white\"\n          >\n            {{ item.badge }}\n          </span>\n        </router-link>\n      </div>\n    </transition>\n\n    <!-- Mobile Submenu (when collapsed) -->\n    <div \n      v-if=\"collapsed && isOpen\"\n      class=\"fixed left-20 top-0 bg-white dark:bg-gray-800 shadow-lg rounded-md py-2 z-50 min-w-48\"\n      :style=\"{ top: mobileMenuTop + 'px' }\"\n    >\n      <router-link\n        v-for=\"item in items\"\n        :key=\"item.to\"\n        :to=\"item.to\"\n        class=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n        @click=\"closeMobileMenu\"\n      >\n        <span class=\"mr-2\">{{ item.icon }}</span>\n        {{ item.label }}\n      </router-link>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, nextTick } from 'vue'\nimport { useRoute } from 'vue-router'\n\n// Props\nconst props = defineProps({\n  icon: {\n    type: String,\n    required: true\n  },\n  label: {\n    type: String,\n    required: true\n  },\n  items: {\n    type: Array,\n    required: true\n  },\n  collapsed: {\n    type: Boolean,\n    default: false\n  },\n  defaultOpen: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// State\nconst isOpen = ref(props.defaultOpen)\nconst mobileMenuTop = ref(0)\n\n// Route\nconst route = useRoute()\n\n// Computed\nconst isActive = computed(() => {\n  return props.items.some(item => route.path.startsWith(item.to))\n})\n\n// Auto-open if current route matches any item\nif (isActive.value) {\n  isOpen.value = true\n}\n\n// Methods\nfunction toggleGroup() {\n  if (props.collapsed) {\n    // For collapsed sidebar, show mobile menu\n    isOpen.value = !isOpen.value\n    if (isOpen.value) {\n      nextTick(() => {\n        // Calculate position for mobile menu\n        const button = event.currentTarget\n        const rect = button.getBoundingClientRect()\n        mobileMenuTop.value = rect.top\n      })\n    }\n  } else {\n    // Normal toggle\n    isOpen.value = !isOpen.value\n  }\n}\n\nfunction isItemActive(itemTo) {\n  if (itemTo === '/') {\n    return route.path === '/'\n  }\n  return route.path.startsWith(itemTo)\n}\n\nfunction closeMobileMenu() {\n  if (props.collapsed) {\n    isOpen.value = false\n  }\n}\n\n// Animation hooks\nfunction onEnter(el) {\n  el.style.height = '0'\n  el.offsetHeight // force reflow\n  el.style.height = el.scrollHeight + 'px'\n}\n\nfunction onLeave(el) {\n  el.style.height = el.scrollHeight + 'px'\n  el.offsetHeight // force reflow\n  el.style.height = '0'\n}\n\n// Close mobile menu when clicking outside\nif (typeof window !== 'undefined') {\n  document.addEventListener('click', (event) => {\n    if (props.collapsed && isOpen.value) {\n      const mobileMenu = event.target.closest('.fixed')\n      const button = event.target.closest('button')\n      \n      if (!mobileMenu && !button) {\n        isOpen.value = false\n      }\n    }\n  })\n}\n</script>\n\n<style scoped>\n/* Submenu animations */\n.submenu-enter-active,\n.submenu-leave-active {\n  transition: height 0.3s ease;\n  overflow: hidden;\n}\n\n.submenu-enter-from,\n.submenu-leave-to {\n  height: 0;\n}\n\n/* Chevron rotation */\n.rotate-90 {\n  transform: rotate(90deg);\n}\n\n/* Mobile menu positioning */\n.fixed {\n  animation: slideIn 0.2s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* Group hover effects */\n.group {\n  position: relative;\n}\n\n/* Active state indicator */\n.router-link-active {\n  position: relative;\n}\n\n.router-link-active::before {\n  content: '';\n  position: absolute;\n  left: -8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 16px;\n  background-color: var(--brand-secondary-400);\n  border-radius: 0 2px 2px 0;\n}\n\n/* Focus styles */\nbutton:focus,\n.router-link-active:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-all,\n  .transition-colors,\n  .transition-transform,\n  .submenu-enter-active,\n  .submenu-leave-active {\n    transition: none;\n  }\n  \n  .fixed {\n    animation: none;\n  }\n}\n</style>\n"}