{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/About.vue"}, "originalCode": "// About.js - Vue.js component converted from .vue file\nimport PublicNavigation from '../../components/public/PublicNavigation.js'\nimport PublicFooter from '../../components/public/PublicFooter.js'\n\nexport default {\n  name: 'About',\n  components: {\n    PublicNavigation,\n    PublicFooter\n  },\n  template: `\n    <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n      <PublicNavigation />\n\n      <!-- Hero Section -->\n      <section class=\"bg-brand-primary-600 text-white py-16\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 class=\"text-4xl font-bold mb-4\">\n            Chi Siamo\n          </h1>\n          <p class=\"text-xl text-brand-primary-100\">\n            La nostra storia e i nostri valori\n          </p>\n        </div>\n      </section>\n\n      <!-- Content -->\n      <section class=\"py-16\">\n        <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div class=\"prose prose-lg max-w-none\">\n            <h2>La nostra storia</h2>\n            <p>DatPortal nasce dalla passione per l'innovazione e la tecnologia. Siamo un team di esperti dedicati a fornire soluzioni all'avanguardia per le aziende moderne.</p>\n\n            <h2>La nostra missione</h2>\n            <p>La nostra missione è supportare le aziende nel loro percorso di trasformazione digitale, fornendo strumenti e servizi che migliorano l'efficienza e la produttività.</p>\n\n            <h2>La nostra visione</h2>\n            <p>Vogliamo essere il partner di riferimento per le aziende che desiderano innovare e crescere nel mercato digitale, offrendo soluzioni personalizzate e di alta qualità.</p>\n          </div>\n        </div>\n      </section>\n\n      <PublicFooter />\n    </div>\n  `\n}\n"}