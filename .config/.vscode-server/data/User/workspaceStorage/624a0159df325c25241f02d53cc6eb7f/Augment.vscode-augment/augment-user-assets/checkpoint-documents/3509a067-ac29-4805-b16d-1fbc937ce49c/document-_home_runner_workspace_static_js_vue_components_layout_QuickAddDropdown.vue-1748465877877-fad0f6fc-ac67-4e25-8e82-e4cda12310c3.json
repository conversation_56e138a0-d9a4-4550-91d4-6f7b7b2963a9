{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/QuickAddDropdown.vue"}, "originalCode": "<template>\n  <div class=\"relative\" v-click-outside=\"closeDropdown\">\n    <!-- Quick Add Button -->\n    <button\n      @click=\"toggleDropdown\"\n      class=\"p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors\"\n      title=\"Aggiungi nuovo\"\n    >\n      <i class=\"fas fa-plus h-5 w-5\"></i>\n    </button>\n\n    <!-- Dropdown -->\n    <transition name=\"dropdown\">\n      <div \n        v-if=\"isOpen\"\n        class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\"\n      >\n        <div class=\"py-1\">\n          <!-- Project -->\n          <button\n            @click=\"quickAdd('project')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-project-diagram mr-3 h-4 w-4 text-brand-primary-500\"></i>\n            Nuovo Progetto\n          </button>\n\n          <!-- Task -->\n          <button\n            @click=\"quickAdd('task')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-tasks mr-3 h-4 w-4 text-brand-secondary-500\"></i>\n            Nuova Attività\n          </button>\n\n          <!-- Client -->\n          <button\n            @click=\"quickAdd('client')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-handshake mr-3 h-4 w-4 text-brand-accent-500\"></i>\n            Nuovo Cliente\n          </button>\n\n          <div class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- User (Admin only) -->\n          <button\n            v-if=\"authStore.isAdmin\"\n            @click=\"quickAdd('user')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-user-plus mr-3 h-4 w-4 text-brand-success-500\"></i>\n            Nuovo Utente\n          </button>\n\n          <!-- Department (Manager only) -->\n          <button\n            v-if=\"authStore.isManager\"\n            @click=\"quickAdd('department')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-building mr-3 h-4 w-4 text-brand-warning-500\"></i>\n            Nuovo Dipartimento\n          </button>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst router = useRouter()\n\n// State\nconst isOpen = ref(false)\n\n// Methods\nfunction toggleDropdown() {\n  isOpen.value = !isOpen.value\n}\n\nfunction closeDropdown() {\n  isOpen.value = false\n}\n\nfunction quickAdd(type) {\n  closeDropdown()\n  \n  switch (type) {\n    case 'project':\n      // Show project creation modal\n      appStore.showModal('ProjectCreateModal', {}, {\n        title: 'Nuovo Progetto',\n        size: 'lg'\n      })\n      break\n      \n    case 'task':\n      // Show task creation modal\n      appStore.showModal('TaskCreateModal', {}, {\n        title: 'Nuova Attività',\n        size: 'md'\n      })\n      break\n      \n    case 'client':\n      // Navigate to CRM with create mode\n      router.push('/crm/clients?action=create')\n      break\n      \n    case 'user':\n      // Show user creation modal\n      appStore.showModal('UserCreateModal', {}, {\n        title: 'Nuovo Utente',\n        size: 'lg'\n      })\n      break\n      \n    case 'department':\n      // Navigate to departments with create mode\n      router.push('/personnel/departments?action=create')\n      break\n      \n    default:\n      console.warn('Unknown quick add type:', type)\n  }\n}\n</script>\n\n<style scoped>\n/* Dropdown animations */\n.dropdown-enter-active,\n.dropdown-leave-active {\n  transition: all 0.2s ease;\n}\n\n.dropdown-enter-from,\n.dropdown-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Button hover effects */\nbutton:hover i {\n  transform: scale(1.1);\n  transition: transform 0.2s ease;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"relative\" v-click-outside=\"closeDropdown\">\n    <!-- Quick Add Button -->\n    <button\n      @click=\"toggleDropdown\"\n      class=\"p-2 rounded-full text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-brand-primary-500 transition-colors\"\n      title=\"Aggiungi nuovo\"\n    >\n      <i class=\"fas fa-plus h-5 w-5\"></i>\n    </button>\n\n    <!-- Dropdown -->\n    <transition name=\"dropdown\">\n      <div \n        v-if=\"isOpen\"\n        class=\"absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\"\n      >\n        <div class=\"py-1\">\n          <!-- Project -->\n          <button\n            @click=\"quickAdd('project')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-project-diagram mr-3 h-4 w-4 text-brand-primary-500\"></i>\n            Nuovo Progetto\n          </button>\n\n          <!-- Task -->\n          <button\n            @click=\"quickAdd('task')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-tasks mr-3 h-4 w-4 text-brand-secondary-500\"></i>\n            Nuova Attività\n          </button>\n\n          <!-- Client -->\n          <button\n            @click=\"quickAdd('client')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-handshake mr-3 h-4 w-4 text-brand-accent-500\"></i>\n            Nuovo Cliente\n          </button>\n\n          <div class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- User (Admin only) -->\n          <button\n            v-if=\"authStore.isAdmin\"\n            @click=\"quickAdd('user')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-user-plus mr-3 h-4 w-4 text-brand-success-500\"></i>\n            Nuovo Utente\n          </button>\n\n          <!-- Department (Manager only) -->\n          <button\n            v-if=\"authStore.isManager\"\n            @click=\"quickAdd('department')\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-building mr-3 h-4 w-4 text-brand-warning-500\"></i>\n            Nuovo Dipartimento\n          </button>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst router = useRouter()\n\n// State\nconst isOpen = ref(false)\n\n// Methods\nfunction toggleDropdown() {\n  isOpen.value = !isOpen.value\n}\n\nfunction closeDropdown() {\n  isOpen.value = false\n}\n\nfunction quickAdd(type) {\n  closeDropdown()\n  \n  switch (type) {\n    case 'project':\n      // Show project creation modal\n      appStore.showModal('ProjectCreateModal', {}, {\n        title: 'Nuovo Progetto',\n        size: 'lg'\n      })\n      break\n      \n    case 'task':\n      // Show task creation modal\n      appStore.showModal('TaskCreateModal', {}, {\n        title: 'Nuova Attività',\n        size: 'md'\n      })\n      break\n      \n    case 'client':\n      // Navigate to CRM with create mode\n      router.push('/crm/clients?action=create')\n      break\n      \n    case 'user':\n      // Show user creation modal\n      appStore.showModal('UserCreateModal', {}, {\n        title: 'Nuovo Utente',\n        size: 'lg'\n      })\n      break\n      \n    case 'department':\n      // Navigate to departments with create mode\n      router.push('/personnel/departments?action=create')\n      break\n      \n    default:\n      console.warn('Unknown quick add type:', type)\n  }\n}\n</script>\n\n<style scoped>\n/* Dropdown animations */\n.dropdown-enter-active,\n.dropdown-leave-active {\n  transition: all 0.2s ease;\n}\n\n.dropdown-enter-from,\n.dropdown-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Button hover effects */\nbutton:hover i {\n  transform: scale(1.1);\n  transition: transform 0.2s ease;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n</style>\n"}