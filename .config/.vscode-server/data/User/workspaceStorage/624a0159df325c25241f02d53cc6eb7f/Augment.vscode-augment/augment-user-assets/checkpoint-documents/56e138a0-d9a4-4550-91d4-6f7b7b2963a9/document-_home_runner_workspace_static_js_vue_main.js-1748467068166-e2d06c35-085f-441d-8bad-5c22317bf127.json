{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}, "originalCode": "// VERSIONE CDN GLOBALE - FUNZIONA SEMPRE!\nconsole.log('🚀 Caricamento Vue.js GLOBALE...')\n\n// Usa Vue.js globale dal CDN\nconst { createApp } = Vue\nconst { createRouter, createWebHistory } = VueRouter\n\n// Componente HOME semplice\nconst Home = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-blue-600 font-medium\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-gray-700 hover:text-blue-600\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"flex items-center justify-center py-20\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-5xl font-bold text-gray-900 mb-6\">🎉 Vue.js FUNZIONA!</h1>\n                    <p class=\"text-xl text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/dashboard\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Vai alla Dashboard\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated ? 'SÌ' : 'NO' }}</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `,\n    data() {\n        return {\n            userInfo: window.APP_CONFIG?.user?.username || 'Anonimo',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n        }\n    }\n}\n\n// Dashboard semplice\nconst Dashboard = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-blue-600 font-medium\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"max-w-7xl mx-auto px-4 py-8\">\n                <h1 class=\"text-3xl font-bold text-gray-900 mb-8\">Dashboard</h1>\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Progetti</h3>\n                        <p class=\"text-3xl font-bold text-blue-600\">12</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Task</h3>\n                        <p class=\"text-3xl font-bold text-green-600\">45</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Team</h3>\n                        <p class=\"text-3xl font-bold text-purple-600\">8</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `\n}\n\n// Routes semplici\nconst routes = [\n    { path: '/', component: Home },\n    { path: '/dashboard', component: Dashboard },\n    { path: '/:pathMatch(.*)*', redirect: '/' }\n]\n\nconst router = createRouter({\n    history: createWebHistory(),\n    routes\n})\n\n// Crea app\nconst app = createApp({\n    template: '<router-view />'\n})\n\napp.use(router)\napp.mount('#app')\n\nconsole.log('✅ Vue.js caricato con successo!')\nwindow.vueApp = app\n", "modifiedCode": "// VERSIONE CDN GLOBALE - FUNZIONA SEMPRE!\nconsole.log('🚀 Caricamento Vue.js GLOBALE...')\n\n// Usa Vue.js globale dal CDN\nconst { createApp } = Vue\nconst { createRouter, createWebHistory } = VueRouter\n\n// Componente HOME semplice\nconst Home = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-blue-600 font-medium\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-gray-700 hover:text-blue-600\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"flex items-center justify-center py-20\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-5xl font-bold text-gray-900 mb-6\">🎉 Vue.js FUNZIONA!</h1>\n                    <p class=\"text-xl text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/dashboard\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Vai alla Dashboard\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated ? 'SÌ' : 'NO' }}</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `,\n    data() {\n        return {\n            userInfo: window.APP_CONFIG?.user?.username || 'Anonimo',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n        }\n    }\n}\n\n// Dashboard semplice\nconst Dashboard = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-blue-600 font-medium\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"max-w-7xl mx-auto px-4 py-8\">\n                <h1 class=\"text-3xl font-bold text-gray-900 mb-8\">Dashboard</h1>\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Progetti</h3>\n                        <p class=\"text-3xl font-bold text-blue-600\">12</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Task</h3>\n                        <p class=\"text-3xl font-bold text-green-600\">45</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Team</h3>\n                        <p class=\"text-3xl font-bold text-purple-600\">8</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `\n}\n\n// Routes complete per SPA\nconst routes = [\n    { path: '/', component: Home },\n    { path: '/dashboard', component: Dashboard },\n    { path: '/projects', component: Dashboard }, // Placeholder\n    { path: '/personnel', component: Dashboard }, // Placeholder\n    { path: '/crm', component: Dashboard }, // Placeholder\n    { path: '/products', component: Dashboard }, // Placeholder\n    { path: '/performance', component: Dashboard }, // Placeholder\n    { path: '/communications', component: Dashboard }, // Placeholder\n    { path: '/funding', component: Dashboard }, // Placeholder\n    { path: '/reporting', component: Dashboard }, // Placeholder\n    { path: '/startup', component: Dashboard }, // Placeholder\n    { path: '/admin', component: Dashboard }, // Placeholder\n    { path: '/:pathMatch(.*)*', redirect: '/' }\n]\n\nconst router = createRouter({\n    history: createWebHistory(),\n    routes\n})\n\n// Crea app\nconst app = createApp({\n    template: '<router-view />'\n})\n\napp.use(router)\napp.mount('#app')\n\nconsole.log('✅ Vue.js caricato con successo!')\nwindow.vueApp = app\n"}