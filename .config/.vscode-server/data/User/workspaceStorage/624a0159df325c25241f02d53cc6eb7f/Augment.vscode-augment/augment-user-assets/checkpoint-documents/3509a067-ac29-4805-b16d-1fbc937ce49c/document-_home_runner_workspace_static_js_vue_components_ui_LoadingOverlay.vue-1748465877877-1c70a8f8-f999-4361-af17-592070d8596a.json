{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/LoadingOverlay.vue"}, "originalCode": "<template>\n  <div \n    class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300\"\n    :class=\"{ 'opacity-0 pointer-events-none': !visible }\"\n  >\n    <div class=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl max-w-sm w-full mx-4 animate-brand-scale-in\">\n      <!-- Loading Spinner -->\n      <div class=\"flex items-center justify-center mb-4\">\n        <div class=\"relative\">\n          <!-- Outer ring -->\n          <div class=\"w-16 h-16 border-4 border-brand-primary-200 rounded-full animate-spin\"></div>\n          <!-- Inner ring -->\n          <div class=\"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-brand-primary-500 rounded-full animate-spin\"></div>\n          <!-- Center dot -->\n          <div class=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-brand-primary-500 rounded-full animate-pulse\"></div>\n        </div>\n      </div>\n      \n      <!-- Loading Message -->\n      <div class=\"text-center\">\n        <h3 class=\"text-lg font-medium text-brand-text-primary mb-2\">\n          {{ title }}\n        </h3>\n        <p class=\"text-sm text-brand-text-secondary\">\n          {{ message }}\n        </p>\n        \n        <!-- Progress Bar (optional) -->\n        <div v-if=\"showProgress\" class=\"mt-4\">\n          <div class=\"bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <div \n              class=\"bg-brand-primary-500 h-2 rounded-full transition-all duration-300\"\n              :style=\"{ width: `${progress}%` }\"\n            ></div>\n          </div>\n          <p class=\"text-xs text-brand-text-tertiary mt-1\">\n            {{ progress }}% completato\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue'\n\n// Props\nconst props = defineProps({\n  message: {\n    type: String,\n    default: 'Caricamento in corso...'\n  },\n  title: {\n    type: String,\n    default: 'Attendere'\n  },\n  showProgress: {\n    type: Boolean,\n    default: false\n  },\n  progress: {\n    type: Number,\n    default: 0,\n    validator: (value) => value >= 0 && value <= 100\n  }\n})\n\n// State\nconst visible = ref(false)\n\n// Show overlay with animation\nonMounted(() => {\n  // Small delay to ensure smooth animation\n  setTimeout(() => {\n    visible.value = true\n  }, 10)\n})\n\n// Prevent body scroll when overlay is visible\nonMounted(() => {\n  document.body.style.overflow = 'hidden'\n})\n\nonUnmounted(() => {\n  document.body.style.overflow = ''\n})\n</script>\n\n<style scoped>\n/* Custom spinner animation */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n\n/* Scale in animation */\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.animate-brand-scale-in {\n  animation: scaleIn 0.3s ease-out;\n}\n\n/* Pulse animation for center dot */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .animate-spin,\n  .animate-brand-scale-in,\n  .animate-pulse {\n    animation: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div \n    class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300\"\n    :class=\"{ 'opacity-0 pointer-events-none': !visible }\"\n  >\n    <div class=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl max-w-sm w-full mx-4 animate-brand-scale-in\">\n      <!-- Loading Spinner -->\n      <div class=\"flex items-center justify-center mb-4\">\n        <div class=\"relative\">\n          <!-- Outer ring -->\n          <div class=\"w-16 h-16 border-4 border-brand-primary-200 rounded-full animate-spin\"></div>\n          <!-- Inner ring -->\n          <div class=\"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-brand-primary-500 rounded-full animate-spin\"></div>\n          <!-- Center dot -->\n          <div class=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-brand-primary-500 rounded-full animate-pulse\"></div>\n        </div>\n      </div>\n      \n      <!-- Loading Message -->\n      <div class=\"text-center\">\n        <h3 class=\"text-lg font-medium text-brand-text-primary mb-2\">\n          {{ title }}\n        </h3>\n        <p class=\"text-sm text-brand-text-secondary\">\n          {{ message }}\n        </p>\n        \n        <!-- Progress Bar (optional) -->\n        <div v-if=\"showProgress\" class=\"mt-4\">\n          <div class=\"bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <div \n              class=\"bg-brand-primary-500 h-2 rounded-full transition-all duration-300\"\n              :style=\"{ width: `${progress}%` }\"\n            ></div>\n          </div>\n          <p class=\"text-xs text-brand-text-tertiary mt-1\">\n            {{ progress }}% completato\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted } from 'vue'\n\n// Props\nconst props = defineProps({\n  message: {\n    type: String,\n    default: 'Caricamento in corso...'\n  },\n  title: {\n    type: String,\n    default: 'Attendere'\n  },\n  showProgress: {\n    type: Boolean,\n    default: false\n  },\n  progress: {\n    type: Number,\n    default: 0,\n    validator: (value) => value >= 0 && value <= 100\n  }\n})\n\n// State\nconst visible = ref(false)\n\n// Show overlay with animation\nonMounted(() => {\n  // Small delay to ensure smooth animation\n  setTimeout(() => {\n    visible.value = true\n  }, 10)\n})\n\n// Prevent body scroll when overlay is visible\nonMounted(() => {\n  document.body.style.overflow = 'hidden'\n})\n\nonUnmounted(() => {\n  document.body.style.overflow = ''\n})\n</script>\n\n<style scoped>\n/* Custom spinner animation */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n\n/* Scale in animation */\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.animate-brand-scale-in {\n  animation: scaleIn 0.3s ease-out;\n}\n\n/* Pulse animation for center dot */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .animate-spin,\n  .animate-brand-scale-in,\n  .animate-pulse {\n    animation: none;\n  }\n}\n</style>\n"}