{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/spa.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DatPortal</title>\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- CSS -->\n    <link href=\"{{ url_for('static', filename='css/brand-variables.css') }}\" rel=\"stylesheet\">\n    <link href=\"{{ url_for('static', filename='css/tailwind.css') }}\" rel=\"stylesheet\">\n\n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n\n    <!-- Chart.js for charts -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n    <!-- Import Maps for ES6 modules -->\n    <script type=\"importmap\">\n    {\n        \"imports\": {\n            \"vue\": \"https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.prod.js\",\n            \"vue-router\": \"https://unpkg.com/vue-router@4.2.0/dist/vue-router.esm-browser.js\",\n            \"pinia\": \"https://unpkg.com/pinia@2.1.0/dist/pinia.esm-browser.js\",\n            \"axios\": \"https://unpkg.com/axios@1.6.0/dist/esm/axios.min.js\"\n        }\n    }\n    </script>\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n</head>\n<body class=\"bg-gray-50 dark:bg-gray-900\">\n    <!-- Vue.js App Container -->\n    <div id=\"app\">\n        <!-- PAGINA CHE FUNZIONA SEMPRE -->\n        <div class=\"min-h-screen bg-blue-50 flex items-center justify-center\">\n            <div class=\"text-center\">\n                <h1 class=\"text-6xl font-bold text-blue-900 mb-6\">🎉 FUNZIONA!</h1>\n                <p class=\"text-2xl text-blue-600 mb-8\">DatPortal è OPERATIVO</p>\n                <div class=\"space-x-4\">\n                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-blue-600\">\n                        VAI AL LOGIN\n                    </a>\n                    <button onclick=\"alert('Vue.js sarà qui presto!')\" class=\"bg-green-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-green-600\">\n                        TEST BUTTON\n                    </button>\n                </div>\n                <div class=\"mt-8 text-lg text-blue-500\">\n                    <p>✅ Flask: OK</p>\n                    <p>✅ Template: OK</p>\n                    <p>✅ CSS: OK</p>\n                    <p>⏳ Vue.js: In arrivo...</p>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n        // Global app configuration\n        window.APP_CONFIG = {\n            apiUrl: '/api',\n            baseUrl: '{{ request.url_root }}',\n            csrfToken: '{{ csrf_token() }}',\n            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n            version: '1.0.0',\n            environment: '{{ config.ENV }}',\n            debug: {{ 'true' if config.DEBUG else 'false' }}\n        };\n\n        // Configure Axios defaults\n        if (typeof axios !== 'undefined') {\n            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;\n            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;\n            axios.defaults.headers.common['Content-Type'] = 'application/json';\n\n            // Add request interceptor for authentication\n            axios.interceptors.request.use(function (config) {\n                // Add timestamp to prevent caching\n                config.params = config.params || {};\n                config.params._t = Date.now();\n                return config;\n            });\n\n            // Add response interceptor for error handling\n            axios.interceptors.response.use(\n                function (response) {\n                    return response;\n                },\n                function (error) {\n                    if (error.response && error.response.status === 401) {\n                        // Redirect to login if unauthorized\n                        window.location.href = '/auth/login';\n                    }\n                    return Promise.reject(error);\n                }\n            );\n        }\n\n        // Global error handler\n        window.addEventListener('error', function(event) {\n            console.error('Global error:', event.error);\n            // You can send errors to a logging service here\n        });\n\n        // Global unhandled promise rejection handler\n        window.addEventListener('unhandledrejection', function(event) {\n            console.error('Unhandled promise rejection:', event.reason);\n            // You can send errors to a logging service here\n        });\n    </script>\n\n    <!-- Test Script First -->\n    <script>\n        console.log('🔥 TEST: Script caricato!');\n\n        // Test immediato - sostituisce il loading spinner\n        setTimeout(() => {\n            console.log('🔥 TEST: Sostituendo loading spinner...');\n            document.getElementById('app').innerHTML = `\n                <div class=\"min-h-screen bg-green-50 flex items-center justify-center\">\n                    <div class=\"text-center\">\n                        <h1 class=\"text-4xl font-bold text-green-900 mb-4\">✅ JAVASCRIPT FUNZIONA!</h1>\n                        <p class=\"text-lg text-green-600 mb-8\">Il problema non è JavaScript</p>\n                        <button onclick=\"loadVue()\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Carica Vue.js\n                        </button>\n                    </div>\n                </div>\n            `;\n        }, 1000);\n\n        // Funzione per caricare Vue.js\n        window.loadVue = function() {\n            console.log('🚀 Caricando Vue.js...');\n            const script = document.createElement('script');\n            script.type = 'module';\n            script.src = '{{ url_for(\"static\", filename=\"js/vue/main.js\") }}';\n            script.onload = () => console.log('✅ Vue.js script caricato');\n            script.onerror = (e) => console.error('❌ Errore caricamento Vue.js:', e);\n            document.head.appendChild(script);\n        }\n    </script>\n\n    <!-- Vue.js Application Entry Point -->\n    <!-- <script type=\"module\" src=\"{{ url_for('static', filename='js/vue/main.js') }}\"></script> -->\n\n\n\n    <!-- Fallback for browsers that don't support import maps -->\n    <script nomodule>\n        document.getElementById('app').innerHTML = `\n            <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Browser non supportato</h1>\n                    <p class=\"text-gray-600 mb-4\">Il tuo browser non supporta le Import Maps.</p>\n                    <p class=\"text-gray-600\">Aggiorna il tuo browser per utilizzare DatPortal.</p>\n                </div>\n            </div>\n        `;\n    </script>\n</body>\n</html>\n", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DatPortal</title>\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- CSS -->\n    <link href=\"{{ url_for('static', filename='css/brand-variables.css') }}\" rel=\"stylesheet\">\n    <link href=\"{{ url_for('static', filename='css/tailwind.css') }}\" rel=\"stylesheet\">\n\n    <!-- Font Awesome for icons -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n\n    <!-- Chart.js for charts -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n    <!-- Vue.js 3 CDN DIRETTO -->\n    <script src=\"https://unpkg.com/vue@3.4.0/dist/vue.global.prod.js\"></script>\n    <script src=\"https://unpkg.com/vue-router@4.2.0/dist/vue-router.global.js\"></script>\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n</head>\n<body class=\"bg-gray-50 dark:bg-gray-900\">\n    <!-- Vue.js App Container -->\n    <div id=\"app\">\n        <!-- PAGINA CHE FUNZIONA SEMPRE -->\n        <div class=\"min-h-screen bg-blue-50 flex items-center justify-center\">\n            <div class=\"text-center\">\n                <h1 class=\"text-6xl font-bold text-blue-900 mb-6\">🎉 FUNZIONA!</h1>\n                <p class=\"text-2xl text-blue-600 mb-8\">DatPortal è OPERATIVO</p>\n                <div class=\"space-x-4\">\n                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-blue-600\">\n                        VAI AL LOGIN\n                    </a>\n                    <button onclick=\"alert('Vue.js sarà qui presto!')\" class=\"bg-green-500 text-white px-8 py-4 rounded-lg text-xl hover:bg-green-600\">\n                        TEST BUTTON\n                    </button>\n                </div>\n                <div class=\"mt-8 text-lg text-blue-500\">\n                    <p>✅ Flask: OK</p>\n                    <p>✅ Template: OK</p>\n                    <p>✅ CSS: OK</p>\n                    <p>⏳ Vue.js: In arrivo...</p>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n        // Global app configuration\n        window.APP_CONFIG = {\n            apiUrl: '/api',\n            baseUrl: '{{ request.url_root }}',\n            csrfToken: '{{ csrf_token() }}',\n            user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n            isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n            version: '1.0.0',\n            environment: '{{ config.ENV }}',\n            debug: {{ 'true' if config.DEBUG else 'false' }}\n        };\n\n        // Configure Axios defaults\n        if (typeof axios !== 'undefined') {\n            axios.defaults.baseURL = window.APP_CONFIG.apiUrl;\n            axios.defaults.headers.common['X-CSRFToken'] = window.APP_CONFIG.csrfToken;\n            axios.defaults.headers.common['Content-Type'] = 'application/json';\n\n            // Add request interceptor for authentication\n            axios.interceptors.request.use(function (config) {\n                // Add timestamp to prevent caching\n                config.params = config.params || {};\n                config.params._t = Date.now();\n                return config;\n            });\n\n            // Add response interceptor for error handling\n            axios.interceptors.response.use(\n                function (response) {\n                    return response;\n                },\n                function (error) {\n                    if (error.response && error.response.status === 401) {\n                        // Redirect to login if unauthorized\n                        window.location.href = '/auth/login';\n                    }\n                    return Promise.reject(error);\n                }\n            );\n        }\n\n        // Global error handler\n        window.addEventListener('error', function(event) {\n            console.error('Global error:', event.error);\n            // You can send errors to a logging service here\n        });\n\n        // Global unhandled promise rejection handler\n        window.addEventListener('unhandledrejection', function(event) {\n            console.error('Unhandled promise rejection:', event.reason);\n            // You can send errors to a logging service here\n        });\n    </script>\n\n    <!-- Test Script First -->\n    <script>\n        console.log('🔥 TEST: Script caricato!');\n\n        // Test immediato - sostituisce il loading spinner\n        setTimeout(() => {\n            console.log('🔥 TEST: Sostituendo loading spinner...');\n            document.getElementById('app').innerHTML = `\n                <div class=\"min-h-screen bg-green-50 flex items-center justify-center\">\n                    <div class=\"text-center\">\n                        <h1 class=\"text-4xl font-bold text-green-900 mb-4\">✅ JAVASCRIPT FUNZIONA!</h1>\n                        <p class=\"text-lg text-green-600 mb-8\">Il problema non è JavaScript</p>\n                        <button onclick=\"loadVue()\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Carica Vue.js\n                        </button>\n                    </div>\n                </div>\n            `;\n        }, 1000);\n\n        // Funzione per caricare Vue.js\n        window.loadVue = function() {\n            console.log('🚀 Caricando Vue.js...');\n            const script = document.createElement('script');\n            script.type = 'module';\n            script.src = '{{ url_for(\"static\", filename=\"js/vue/main.js\") }}';\n            script.onload = () => console.log('✅ Vue.js script caricato');\n            script.onerror = (e) => console.error('❌ Errore caricamento Vue.js:', e);\n            document.head.appendChild(script);\n        }\n    </script>\n\n    <!-- Vue.js Application Entry Point -->\n    <!-- <script type=\"module\" src=\"{{ url_for('static', filename='js/vue/main.js') }}\"></script> -->\n\n\n\n    <!-- Fallback for browsers that don't support import maps -->\n    <script nomodule>\n        document.getElementById('app').innerHTML = `\n            <div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Browser non supportato</h1>\n                    <p class=\"text-gray-600 mb-4\">Il tuo browser non supporta le Import Maps.</p>\n                    <p class=\"text-gray-600\">Aggiorna il tuo browser per utilizzare DatPortal.</p>\n                </div>\n            </div>\n        `;\n    </script>\n</body>\n</html>\n"}