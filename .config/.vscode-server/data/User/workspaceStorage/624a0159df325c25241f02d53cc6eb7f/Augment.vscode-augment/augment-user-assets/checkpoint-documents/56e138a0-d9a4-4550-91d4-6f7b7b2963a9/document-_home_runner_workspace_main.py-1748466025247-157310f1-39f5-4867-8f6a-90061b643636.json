{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "main.py"}, "originalCode": "from app import create_app, db\n\napp = create_app() # Chiamata alla factory\n\nif __name__ == \"__main__\":\n    # Considera di usare app.config['DEBUG'] qui se vuoi che sia controllato dalla configurazione\n    app.run(host=\"0.0.0.0\", port=5000, debug=app.config.get('DEBUG', True))\n", "modifiedCode": "from app import create_app, db\n\napp = create_app() # Chiamata alla factory\n\nif __name__ == \"__main__\":\n    # Considera di usare app.config['DEBUG'] qui se vuoi che sia controllato dalla configurazione\n    app.run(host=\"0.0.0.0\", port=5001, debug=app.config.get('DEBUG', True))\n"}