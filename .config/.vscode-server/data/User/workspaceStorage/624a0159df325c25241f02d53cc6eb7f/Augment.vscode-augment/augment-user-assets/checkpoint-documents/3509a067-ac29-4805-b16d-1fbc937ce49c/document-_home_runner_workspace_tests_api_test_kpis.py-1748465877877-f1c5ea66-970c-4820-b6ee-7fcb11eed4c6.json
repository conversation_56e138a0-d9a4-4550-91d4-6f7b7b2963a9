{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/api/test_kpis.py"}, "originalCode": "\"\"\"\nTest per le API dei KPI.\n\"\"\"\nimport pytest\nimport json\nfrom models import KPI\nfrom extensions import db\n\ndef test_get_kpis_unauthorized(client):\n    \"\"\"Test per l'endpoint GET /api/kpis/ senza autenticazione.\"\"\"\n    response = client.get('/api/kpis/')\n\n    # Deve richiedere autenticazione\n    assert response.status_code in [302, 401]\n\ndef test_get_kpis_empty_list(client, auth):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con lista vuota.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get('/api/kpis/')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpis' in data['data']\n    assert data['data']['kpis'] == []\n    assert 'pagination' in data['data']\n\ndef test_get_kpis_with_data(client, auth, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con dati.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get('/api/kpis/')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpis' in data['data']\n    assert len(data['data']['kpis']) == 1\n\n    # Verifica dati KPI\n    kpi = data['data']['kpis'][0]\n    assert kpi['id'] == test_kpi  # test_kpi ora è un ID\n    assert kpi['name'] == 'Test KPI'\n    assert kpi['description'] == 'A test KPI for API testing'\n    assert kpi['category'] == 'Test'\n    assert kpi['target_value'] == 100.0\n    assert kpi['current_value'] == 50.0\n    assert kpi['unit'] == '%'\n    assert kpi['frequency'] == 'monthly'\n    assert kpi['progress'] == 50.0  # 50/100 * 100\n\ndef test_get_kpis_with_filters(client, auth, app):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con filtri.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea KPI di test con categorie diverse (senza usare fixture test_kpi)\n    with app.app_context():\n        # Prima pulisci eventuali KPI esistenti per questo test\n        KPI.query.delete()\n        db.session.commit()\n\n        kpi1 = KPI(name='KPI Finanziario', category='Finanziario', frequency='monthly')\n        kpi2 = KPI(name='KPI Operativo', category='Operativo', frequency='weekly')\n        kpi3 = KPI(name='Fatturato Mensile', category='Finanziario', frequency='monthly')\n\n        db.session.add_all([kpi1, kpi2, kpi3])\n        db.session.commit()\n\n    # Test filtro per categoria\n    response = client.get('/api/kpis/?category=Finanziario')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 2\n\n    # Test filtro per frequenza\n    response = client.get('/api/kpis/?frequency=monthly')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 2\n\n    # Test ricerca\n    response = client.get('/api/kpis/?search=fatturato')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 1\n    assert data['data']['kpis'][0]['name'] == 'Fatturato Mensile'\n\ndef test_get_kpi_detail_unauthorized(client, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> senza autenticazione.\"\"\"\n    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID\n\n    # L'endpoint GET potrebbe essere accessibile senza autenticazione o richiedere login\n    assert response.status_code in [200, 302, 401]\n\ndef test_get_kpi_detail_success(client, auth, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> con successo.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n\n    # Verifica dati KPI\n    kpi = data['data']['kpi']\n    assert kpi['id'] == test_kpi  # test_kpi è ora un ID\n    assert kpi['name'] == 'Test KPI'\n    assert kpi['description'] == 'A test KPI for API testing'\n\ndef test_get_kpi_not_found(client, auth):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API con ID non esistente\n    response = client.get('/api/kpis/99999')\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_create_kpi_unauthorized(client):\n    \"\"\"Test per l'endpoint POST /api/kpis/ senza autenticazione.\"\"\"\n    kpi_data = {\n        'name': 'Test KPI',\n        'description': 'KPI di test',\n        'category': 'Test'\n    }\n\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Deve richiedere autenticazione o permessi, ma può anche restituire 400 per validazione\n    assert response.status_code in [302, 400, 401, 403]\n\ndef test_create_kpi_success(client, admin_auth, app):\n    \"\"\"Test per l'endpoint POST /api/kpis/ con successo.\"\"\"\n    # Login come admin (serve permesso EDIT_PROJECT)\n    admin_auth.login()\n\n    # Dati per il nuovo KPI\n    kpi_data = {\n        'name': 'Nuovo KPI Test',\n        'description': 'KPI di test per i test automatici',\n        'category': 'Test',\n        'target_value': 100,\n        'current_value': 50,\n        'unit': '%',\n        'frequency': 'monthly'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 201\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n    assert 'message' in data\n\n    # Verifica dati KPI\n    kpi = data['data']['kpi']\n    assert kpi['name'] == kpi_data['name']\n    assert kpi['description'] == kpi_data['description']\n    assert kpi['category'] == kpi_data['category']\n    assert kpi['target_value'] == kpi_data['target_value']\n    assert kpi['current_value'] == kpi_data['current_value']\n    assert kpi['unit'] == kpi_data['unit']\n    assert kpi['frequency'] == kpi_data['frequency']\n\n    # Verifica che sia stato salvato nel database\n    with app.app_context():\n        saved_kpi = KPI.query.filter_by(name=kpi_data['name']).first()\n        assert saved_kpi is not None\n        assert saved_kpi.name == kpi_data['name']\n\ndef test_create_kpi_missing_name(client, admin_auth):\n    \"\"\"Test per l'endpoint POST /api/kpis/ senza nome.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati senza nome\n    kpi_data = {\n        'description': 'KPI senza nome',\n        'category': 'Test'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n    assert data['success'] is False\n    assert 'nome' in data['message'].lower()\n\ndef test_create_kpi_duplicate_name(client, admin_auth, test_kpi):\n    \"\"\"Test per l'endpoint POST /api/kpis/ con nome duplicato.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati con nome già esistente\n    kpi_data = {\n        'name': 'Test KPI',  # Nome già esistente (dalla fixture test_kpi)\n        'description': 'KPI duplicato',\n        'category': 'Test'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n    assert data['success'] is False\n    assert 'esiste già' in data['message'].lower()\n\ndef test_update_kpi_success(client, admin_auth, test_kpi, app):\n    \"\"\"Test per l'endpoint PUT /api/kpis/<id> con successo.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati per l'aggiornamento\n    kpi_data = {\n        'name': 'KPI Aggiornato',\n        'description': 'Descrizione aggiornata',\n        'current_value': 75,\n        'category': 'Aggiornato'\n    }\n\n    # Richiesta all'API\n    response = client.put(\n        f'/api/kpis/{test_kpi}',  # test_kpi è ora un ID\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n    assert 'message' in data\n\n    # Verifica dati KPI aggiornati\n    kpi = data['data']['kpi']\n    assert kpi['name'] == kpi_data['name']\n    assert kpi['description'] == kpi_data['description']\n    assert kpi['current_value'] == kpi_data['current_value']\n    assert kpi['category'] == kpi_data['category']\n\n    # Verifica che sia stato aggiornato nel database\n    with app.app_context():\n        updated_kpi = KPI.query.get(test_kpi)  # test_kpi è ora un ID\n        assert updated_kpi.name == kpi_data['name']\n        assert updated_kpi.description == kpi_data['description']\n\ndef test_update_kpi_not_found(client, admin_auth):\n    \"\"\"Test per l'endpoint PUT /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati per l'aggiornamento\n    kpi_data = {\n        'name': 'KPI Inesistente',\n        'description': 'Descrizione'\n    }\n\n    # Richiesta all'API con ID non esistente\n    response = client.put(\n        '/api/kpis/99999',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_delete_kpi_success(client, admin_auth, app):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con successo.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Crea un KPI che può essere eliminato\n    with app.app_context():\n        kpi_to_delete = KPI(\n            name='KPI da Eliminare',\n            description='Questo KPI sarà eliminato',\n            category='Test'\n        )\n        db.session.add(kpi_to_delete)\n        db.session.commit()\n        kpi_id = kpi_to_delete.id\n\n    # Richiesta all'API\n    response = client.delete(f'/api/kpis/{kpi_id}')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'message' in data\n    assert 'eliminato' in data['message'].lower()\n\n    # Verifica che sia stato eliminato dal database\n    with app.app_context():\n        deleted_kpi = KPI.query.get(kpi_id)\n        assert deleted_kpi is None\n\ndef test_delete_kpi_in_use(client, admin_auth, test_project_kpi, test_kpi):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con KPI in uso.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Richiesta all'API per eliminare un KPI che è in uso in un progetto\n    response = client.delete(f'/api/kpis/{test_kpi}')  # test_kpi è l'ID del KPI usato nel ProjectKPI\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta di errore\n    assert data['success'] is False\n    assert 'message' in data\n    assert 'utilizzato' in data['message'].lower()\n\ndef test_delete_kpi_not_found(client, admin_auth):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Richiesta all'API con ID non esistente\n    response = client.delete('/api/kpis/99999')\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_kpi_pagination(client, auth, app):\n    \"\"\"Test per la paginazione degli endpoint KPI.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea più KPI per testare la paginazione\n    with app.app_context():\n        kpis = []\n        for i in range(15):\n            kpi = KPI(\n                name=f'KPI Paginazione {i}',\n                description=f'KPI numero {i}',\n                category='Test'\n            )\n            kpis.append(kpi)\n\n        db.session.add_all(kpis)\n        db.session.commit()\n\n    # Test con parametri di paginazione\n    response = client.get('/api/kpis/?page=1&per_page=5')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica presenza paginazione\n    assert 'pagination' in data['data']\n    pagination = data['data']['pagination']\n    assert 'page' in pagination\n    assert 'per_page' in pagination\n    assert 'total' in pagination\n    assert 'pages' in pagination\n\n    # Verifica che ci siano esattamente 5 KPI nella prima pagina\n    assert len(data['data']['kpis']) == 5\n    assert pagination['page'] == 1\n    assert pagination['per_page'] == 5\n\ndef test_kpi_frequency_validation(client, admin_auth):\n    \"\"\"Test per la validazione dell'enum frequency.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Test con frequenze valide\n    valid_frequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'annually']\n\n    for i, frequency in enumerate(valid_frequencies):\n        kpi_data = {\n            'name': f'KPI {frequency} {i}',  # Nome unico\n            'frequency': frequency\n        }\n\n        response = client.post(\n            '/api/kpis/',\n            data=json.dumps(kpi_data),\n            content_type='application/json'\n        )\n\n        # Verifica che la risposta sia 201 (Created)\n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['data']['kpi']['frequency'] == frequency\n\ndef test_kpi_progress_calculation(client, auth, app):\n    \"\"\"Test per il calcolo del progress dei KPI.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea un KPI con valori specifici per testare il calcolo del progress\n    with app.app_context():\n        kpi = KPI(\n            name='KPI Progress Test',\n            target_value=200.0,\n            current_value=100.0\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        kpi_id = kpi.id\n\n    # Richiesta all'API\n    response = client.get(f'/api/kpis/{kpi_id}')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica calcolo progress (100/200 * 100 = 50%)\n    kpi_data = data['data']['kpi']\n    assert kpi_data['progress'] == 50.0\n", "modifiedCode": "\"\"\"\nTest per le API dei KPI.\n\"\"\"\nimport pytest\nimport json\nfrom models import KPI\nfrom extensions import db\n\ndef test_get_kpis_unauthorized(client):\n    \"\"\"Test per l'endpoint GET /api/kpis/ senza autenticazione.\"\"\"\n    response = client.get('/api/kpis/')\n\n    # Deve richiedere autenticazione\n    assert response.status_code in [302, 401]\n\ndef test_get_kpis_empty_list(client, auth):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con lista vuota.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get('/api/kpis/')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpis' in data['data']\n    assert data['data']['kpis'] == []\n    assert 'pagination' in data['data']\n\ndef test_get_kpis_with_data(client, auth, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con dati.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get('/api/kpis/')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpis' in data['data']\n    assert len(data['data']['kpis']) == 1\n\n    # Verifica dati KPI\n    kpi = data['data']['kpis'][0]\n    assert kpi['id'] == test_kpi  # test_kpi ora è un ID\n    assert kpi['name'] == 'Test KPI'\n    assert kpi['description'] == 'A test KPI for API testing'\n    assert kpi['category'] == 'Test'\n    assert kpi['target_value'] == 100.0\n    assert kpi['current_value'] == 50.0\n    assert kpi['unit'] == '%'\n    assert kpi['frequency'] == 'monthly'\n    assert kpi['progress'] == 50.0  # 50/100 * 100\n\ndef test_get_kpis_with_filters(client, auth, app):\n    \"\"\"Test per l'endpoint GET /api/kpis/ con filtri.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea KPI di test con categorie diverse (senza usare fixture test_kpi)\n    with app.app_context():\n        # Prima pulisci eventuali KPI esistenti per questo test\n        KPI.query.delete()\n        db.session.commit()\n\n        kpi1 = KPI(name='KPI Finanziario', category='Finanziario', frequency='monthly')\n        kpi2 = KPI(name='KPI Operativo', category='Operativo', frequency='weekly')\n        kpi3 = KPI(name='Fatturato Mensile', category='Finanziario', frequency='monthly')\n\n        db.session.add_all([kpi1, kpi2, kpi3])\n        db.session.commit()\n\n    # Test filtro per categoria\n    response = client.get('/api/kpis/?category=Finanziario')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 2\n\n    # Test filtro per frequenza\n    response = client.get('/api/kpis/?frequency=monthly')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 2\n\n    # Test ricerca\n    response = client.get('/api/kpis/?search=fatturato')\n    assert response.status_code == 200\n    data = json.loads(response.data)\n    assert len(data['data']['kpis']) == 1\n    assert data['data']['kpis'][0]['name'] == 'Fatturato Mensile'\n\ndef test_get_kpi_detail_unauthorized(client, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> senza autenticazione.\"\"\"\n    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID\n\n    # L'endpoint GET potrebbe essere accessibile senza autenticazione o richiedere login\n    assert response.status_code in [200, 302, 401]\n\ndef test_get_kpi_detail_success(client, auth, test_kpi):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> con successo.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API\n    response = client.get(f'/api/kpis/{test_kpi}')  # test_kpi è ora un ID\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n\n    # Verifica dati KPI\n    kpi = data['data']['kpi']\n    assert kpi['id'] == test_kpi  # test_kpi è ora un ID\n    assert kpi['name'] == 'Test KPI'\n    assert kpi['description'] == 'A test KPI for API testing'\n\ndef test_get_kpi_not_found(client, auth):\n    \"\"\"Test per l'endpoint GET /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login\n    auth.login()\n\n    # Richiesta all'API con ID non esistente\n    response = client.get('/api/kpis/99999')\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_create_kpi_unauthorized(client):\n    \"\"\"Test per l'endpoint POST /api/kpis/ senza autenticazione.\"\"\"\n    kpi_data = {\n        'name': 'Test KPI',\n        'description': 'KPI di test',\n        'category': 'Test'\n    }\n\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Deve richiedere autenticazione o permessi, ma può anche restituire 400 per validazione\n    assert response.status_code in [302, 400, 401, 403]\n\ndef test_create_kpi_success(client, admin_auth, app):\n    \"\"\"Test per l'endpoint POST /api/kpis/ con successo.\"\"\"\n    # Login come admin (serve permesso EDIT_PROJECT)\n    admin_auth.login()\n\n    # Dati per il nuovo KPI\n    kpi_data = {\n        'name': 'Nuovo KPI Test',\n        'description': 'KPI di test per i test automatici',\n        'category': 'Test',\n        'target_value': 100,\n        'current_value': 50,\n        'unit': '%',\n        'frequency': 'monthly'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 201\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n    assert 'message' in data\n\n    # Verifica dati KPI\n    kpi = data['data']['kpi']\n    assert kpi['name'] == kpi_data['name']\n    assert kpi['description'] == kpi_data['description']\n    assert kpi['category'] == kpi_data['category']\n    assert kpi['target_value'] == kpi_data['target_value']\n    assert kpi['current_value'] == kpi_data['current_value']\n    assert kpi['unit'] == kpi_data['unit']\n    assert kpi['frequency'] == kpi_data['frequency']\n\n    # Verifica che sia stato salvato nel database\n    with app.app_context():\n        saved_kpi = KPI.query.filter_by(name=kpi_data['name']).first()\n        assert saved_kpi is not None\n        assert saved_kpi.name == kpi_data['name']\n\ndef test_create_kpi_missing_name(client, admin_auth):\n    \"\"\"Test per l'endpoint POST /api/kpis/ senza nome.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati senza nome\n    kpi_data = {\n        'description': 'KPI senza nome',\n        'category': 'Test'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n    assert data['success'] is False\n    assert 'nome' in data['message'].lower()\n\ndef test_create_kpi_duplicate_name(client, admin_auth, test_kpi):\n    \"\"\"Test per l'endpoint POST /api/kpis/ con nome duplicato.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati con nome già esistente\n    kpi_data = {\n        'name': 'Test KPI',  # Nome già esistente (dalla fixture test_kpi)\n        'description': 'KPI duplicato',\n        'category': 'Test'\n    }\n\n    # Richiesta all'API\n    response = client.post(\n        '/api/kpis/',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n    assert data['success'] is False\n    assert 'esiste già' in data['message'].lower()\n\ndef test_update_kpi_success(client, admin_auth, test_kpi, app):\n    \"\"\"Test per l'endpoint PUT /api/kpis/<id> con successo.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati per l'aggiornamento\n    kpi_data = {\n        'name': 'KPI Aggiornato',\n        'description': 'Descrizione aggiornata',\n        'current_value': 75,\n        'category': 'Aggiornato'\n    }\n\n    # Richiesta all'API\n    response = client.put(\n        f'/api/kpis/{test_kpi}',  # test_kpi è ora un ID\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'data' in data\n    assert 'kpi' in data['data']\n    assert 'message' in data\n\n    # Verifica dati KPI aggiornati\n    kpi = data['data']['kpi']\n    assert kpi['name'] == kpi_data['name']\n    assert kpi['description'] == kpi_data['description']\n    assert kpi['current_value'] == kpi_data['current_value']\n    assert kpi['category'] == kpi_data['category']\n\n    # Verifica che sia stato aggiornato nel database\n    with app.app_context():\n        updated_kpi = KPI.query.get(test_kpi)  # test_kpi è ora un ID\n        assert updated_kpi.name == kpi_data['name']\n        assert updated_kpi.description == kpi_data['description']\n\ndef test_update_kpi_not_found(client, admin_auth):\n    \"\"\"Test per l'endpoint PUT /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Dati per l'aggiornamento\n    kpi_data = {\n        'name': 'KPI Inesistente',\n        'description': 'Descrizione'\n    }\n\n    # Richiesta all'API con ID non esistente\n    response = client.put(\n        '/api/kpis/99999',\n        data=json.dumps(kpi_data),\n        content_type='application/json'\n    )\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_delete_kpi_success(client, admin_auth, app):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con successo.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Crea un KPI che può essere eliminato\n    with app.app_context():\n        kpi_to_delete = KPI(\n            name='KPI da Eliminare',\n            description='Questo KPI sarà eliminato',\n            category='Test'\n        )\n        db.session.add(kpi_to_delete)\n        db.session.commit()\n        kpi_id = kpi_to_delete.id\n\n    # Richiesta all'API\n    response = client.delete(f'/api/kpis/{kpi_id}')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta\n    assert data['success'] is True\n    assert 'message' in data\n    assert 'eliminato' in data['message'].lower()\n\n    # Verifica che sia stato eliminato dal database\n    with app.app_context():\n        deleted_kpi = KPI.query.get(kpi_id)\n        assert deleted_kpi is None\n\ndef test_delete_kpi_in_use(client, admin_auth, test_project_kpi, test_kpi):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con KPI in uso.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Richiesta all'API per eliminare un KPI che è in uso in un progetto\n    response = client.delete(f'/api/kpis/{test_kpi}')  # test_kpi è l'ID del KPI usato nel ProjectKPI\n\n    # Verifica che la risposta sia 400 (Bad Request)\n    assert response.status_code == 400\n    data = json.loads(response.data)\n\n    # Verifica struttura risposta di errore\n    assert data['success'] is False\n    assert 'message' in data\n    assert 'utilizzato' in data['message'].lower()\n\ndef test_delete_kpi_not_found(client, admin_auth):\n    \"\"\"Test per l'endpoint DELETE /api/kpis/<id> con ID non esistente.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Richiesta all'API con ID non esistente\n    response = client.delete('/api/kpis/99999')\n\n    # Verifica risposta\n    assert response.status_code == 404\n\ndef test_kpi_pagination(client, auth, app):\n    \"\"\"Test per la paginazione degli endpoint KPI.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea più KPI per testare la paginazione\n    with app.app_context():\n        kpis = []\n        for i in range(15):\n            kpi = KPI(\n                name=f'KPI Paginazione {i}',\n                description=f'KPI numero {i}',\n                category='Test'\n            )\n            kpis.append(kpi)\n\n        db.session.add_all(kpis)\n        db.session.commit()\n\n    # Test con parametri di paginazione\n    response = client.get('/api/kpis/?page=1&per_page=5')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica presenza paginazione\n    assert 'pagination' in data['data']\n    pagination = data['data']['pagination']\n    assert 'page' in pagination\n    assert 'per_page' in pagination\n    assert 'total' in pagination\n    assert 'pages' in pagination\n\n    # Verifica che ci siano esattamente 5 KPI nella prima pagina\n    assert len(data['data']['kpis']) == 5\n    assert pagination['page'] == 1\n    assert pagination['per_page'] == 5\n\ndef test_kpi_frequency_validation(client, admin_auth):\n    \"\"\"Test per la validazione dell'enum frequency.\"\"\"\n    # Login come admin\n    admin_auth.login()\n\n    # Test con frequenze valide\n    valid_frequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'annually']\n\n    for i, frequency in enumerate(valid_frequencies):\n        kpi_data = {\n            'name': f'KPI {frequency} {i}',  # Nome unico\n            'frequency': frequency\n        }\n\n        response = client.post(\n            '/api/kpis/',\n            data=json.dumps(kpi_data),\n            content_type='application/json'\n        )\n\n        # Verifica che la risposta sia 201 (Created)\n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['data']['kpi']['frequency'] == frequency\n\ndef test_kpi_progress_calculation(client, auth, app):\n    \"\"\"Test per il calcolo del progress dei KPI.\"\"\"\n    # Login\n    auth.login()\n\n    # Crea un KPI con valori specifici per testare il calcolo del progress\n    with app.app_context():\n        kpi = KPI(\n            name='KPI Progress Test',\n            target_value=200.0,\n            current_value=100.0\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        kpi_id = kpi.id\n\n    # Richiesta all'API\n    response = client.get(f'/api/kpis/{kpi_id}')\n\n    # Verifica risposta\n    assert response.status_code == 200\n    data = json.loads(response.data)\n\n    # Verifica calcolo progress (100/200 * 100 = 50%)\n    kpi_data = data['data']['kpi']\n    assert kpi_data['progress'] == 50.0\n"}