{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/Sidebar.vue"}, "originalCode": "<template>\n  <!-- Desktop Sidebar -->\n  <div class=\"hidden md:flex md:flex-shrink-0\">\n    <div \n      class=\"flex flex-col transition-all duration-300\"\n      :class=\"[\n        appStore.sidebarOpen ? 'w-sidebar' : 'w-sidebar-collapsed'\n      ]\"\n    >\n      <div class=\"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-brand-primary-700 dark:bg-gray-800 border-r border-brand-primary-600 dark:border-gray-700\">\n        <!-- Logo Section -->\n        <div class=\"flex items-center flex-shrink-0 px-4\">\n          <div class=\"flex items-center\" :class=\"{ 'justify-center': !appStore.sidebarOpen }\">\n            <!-- Logo when sidebar is open -->\n            <router-link \n              to=\"/\" \n              class=\"flex items-center\" \n              :class=\"{ 'hidden': !appStore.sidebarOpen }\"\n            >\n              <img \n                :src=\"brandStore.currentLogo\" \n                alt=\"Logo\" \n                class=\"h-12 w-auto rounded-lg shadow-xl\"\n              >\n              <h3 class=\"ml-3 text-2xl font-light leading-tight text-white\">\n                {{ brandStore.brandConfig.name }}\n              </h3>\n            </router-link>\n            \n            <!-- Compact logo for collapsed sidebar -->\n            <router-link \n              to=\"/\" \n              class=\"flex items-center justify-center\" \n              :class=\"{ 'hidden': appStore.sidebarOpen }\"\n            >\n              <img \n                :src=\"brandStore.compactLogo\" \n                alt=\"Logo\" \n                class=\"h-8 w-auto rounded-lg shadow-xl\"\n              >\n            </router-link>\n          </div>\n          \n          <!-- Toggle Button -->\n          <button \n            @click=\"appStore.toggleSidebar()\"\n            class=\"ml-auto text-white focus:outline-none hover:text-brand-primary-200 transition-colors\"\n          >\n            <i class=\"fas fa-bars\" v-if=\"!appStore.sidebarOpen\"></i>\n            <i class=\"fas fa-times\" v-else></i>\n          </button>\n        </div>\n\n        <!-- Navigation Menu -->\n        <nav class=\"mt-5 flex-1 px-2 space-y-1\">\n          <!-- Dashboard -->\n          <SidebarItem\n            to=\"/\"\n            icon=\"fas fa-home\"\n            label=\"Dashboard\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Personnel with Submenu -->\n          <SidebarGroup\n            icon=\"fas fa-users\"\n            label=\"Personale\"\n            :collapsed=\"!appStore.sidebarOpen\"\n            :items=\"personnelItems\"\n          />\n\n          <!-- Projects -->\n          <SidebarItem\n            to=\"/projects\"\n            icon=\"fas fa-project-diagram\"\n            label=\"Progetti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- CRM -->\n          <SidebarItem\n            to=\"/crm\"\n            icon=\"fas fa-handshake\"\n            label=\"CRM\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Products -->\n          <SidebarItem\n            to=\"/products\"\n            icon=\"fas fa-box\"\n            label=\"Prodotti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Performance -->\n          <SidebarItem\n            to=\"/performance\"\n            icon=\"fas fa-chart-line\"\n            label=\"Performance\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Communications -->\n          <SidebarItem\n            to=\"/communications\"\n            icon=\"fas fa-bullhorn\"\n            label=\"Comunicazione\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Funding -->\n          <SidebarItem\n            to=\"/funding\"\n            icon=\"fas fa-coins\"\n            label=\"Finanziamenti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Reporting -->\n          <SidebarItem\n            to=\"/reporting\"\n            icon=\"fas fa-file-alt\"\n            label=\"Rendicontazione\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Startup -->\n          <SidebarItem\n            to=\"/startup\"\n            icon=\"fas fa-rocket\"\n            label=\"Startup\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Admin (only for admins) -->\n          <SidebarItem\n            v-if=\"authStore.isAdmin\"\n            to=\"/admin\"\n            icon=\"fas fa-cog\"\n            label=\"Admin\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n        </nav>\n\n        <!-- User Section -->\n        <div class=\"flex-shrink-0 flex border-t border-brand-primary-800 dark:border-gray-700 p-2\">\n          <UserProfile :collapsed=\"!appStore.sidebarOpen\" />\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <MobileSidebar />\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Components\nimport SidebarItem from './SidebarItem.vue'\nimport SidebarGroup from './SidebarGroup.vue'\nimport UserProfile from './UserProfile.vue'\nimport MobileSidebar from './MobileSidebar.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\n\n// Personnel submenu items\nconst personnelItems = computed(() => {\n  const items = [\n    { to: '/personnel', label: 'Team', icon: '👥' },\n    { to: '/personnel/directory', label: 'Directory', icon: '📖' },\n    { to: '/personnel/orgchart', label: 'Organigramma', icon: '🏢' },\n    { to: '/personnel/skills', label: 'Competenze', icon: '🎯' }\n  ]\n\n  // Add admin-only items\n  if (authStore.isManager) {\n    items.push(\n      { to: '/personnel/departments', label: 'Dipartimenti', icon: '🏢' },\n      { to: '/personnel/admin', label: 'Amministrazione', icon: '⚙️' }\n    )\n  }\n\n  return items\n})\n</script>\n\n<style scoped>\n/* Sidebar animations */\n.sidebar-enter-active,\n.sidebar-leave-active {\n  transition: transform var(--brand-transition-normal);\n}\n\n.sidebar-enter-from,\n.sidebar-leave-to {\n  transform: translateX(-100%);\n}\n\n/* Custom scrollbar for sidebar */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 4px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: var(--brand-radius-full);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .hidden.md\\\\:flex {\n    display: none !important;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <!-- Desktop Sidebar -->\n  <div class=\"hidden md:flex md:flex-shrink-0\">\n    <div \n      class=\"flex flex-col transition-all duration-300\"\n      :class=\"[\n        appStore.sidebarOpen ? 'w-sidebar' : 'w-sidebar-collapsed'\n      ]\"\n    >\n      <div class=\"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-brand-primary-700 dark:bg-gray-800 border-r border-brand-primary-600 dark:border-gray-700\">\n        <!-- Logo Section -->\n        <div class=\"flex items-center flex-shrink-0 px-4\">\n          <div class=\"flex items-center\" :class=\"{ 'justify-center': !appStore.sidebarOpen }\">\n            <!-- Logo when sidebar is open -->\n            <router-link \n              to=\"/\" \n              class=\"flex items-center\" \n              :class=\"{ 'hidden': !appStore.sidebarOpen }\"\n            >\n              <img \n                :src=\"brandStore.currentLogo\" \n                alt=\"Logo\" \n                class=\"h-12 w-auto rounded-lg shadow-xl\"\n              >\n              <h3 class=\"ml-3 text-2xl font-light leading-tight text-white\">\n                {{ brandStore.brandConfig.name }}\n              </h3>\n            </router-link>\n            \n            <!-- Compact logo for collapsed sidebar -->\n            <router-link \n              to=\"/\" \n              class=\"flex items-center justify-center\" \n              :class=\"{ 'hidden': appStore.sidebarOpen }\"\n            >\n              <img \n                :src=\"brandStore.compactLogo\" \n                alt=\"Logo\" \n                class=\"h-8 w-auto rounded-lg shadow-xl\"\n              >\n            </router-link>\n          </div>\n          \n          <!-- Toggle Button -->\n          <button \n            @click=\"appStore.toggleSidebar()\"\n            class=\"ml-auto text-white focus:outline-none hover:text-brand-primary-200 transition-colors\"\n          >\n            <i class=\"fas fa-bars\" v-if=\"!appStore.sidebarOpen\"></i>\n            <i class=\"fas fa-times\" v-else></i>\n          </button>\n        </div>\n\n        <!-- Navigation Menu -->\n        <nav class=\"mt-5 flex-1 px-2 space-y-1\">\n          <!-- Dashboard -->\n          <SidebarItem\n            to=\"/\"\n            icon=\"fas fa-home\"\n            label=\"Dashboard\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Personnel with Submenu -->\n          <SidebarGroup\n            icon=\"fas fa-users\"\n            label=\"Personale\"\n            :collapsed=\"!appStore.sidebarOpen\"\n            :items=\"personnelItems\"\n          />\n\n          <!-- Projects -->\n          <SidebarItem\n            to=\"/projects\"\n            icon=\"fas fa-project-diagram\"\n            label=\"Progetti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- CRM -->\n          <SidebarItem\n            to=\"/crm\"\n            icon=\"fas fa-handshake\"\n            label=\"CRM\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Products -->\n          <SidebarItem\n            to=\"/products\"\n            icon=\"fas fa-box\"\n            label=\"Prodotti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Performance -->\n          <SidebarItem\n            to=\"/performance\"\n            icon=\"fas fa-chart-line\"\n            label=\"Performance\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Communications -->\n          <SidebarItem\n            to=\"/communications\"\n            icon=\"fas fa-bullhorn\"\n            label=\"Comunicazione\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Funding -->\n          <SidebarItem\n            to=\"/funding\"\n            icon=\"fas fa-coins\"\n            label=\"Finanziamenti\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Reporting -->\n          <SidebarItem\n            to=\"/reporting\"\n            icon=\"fas fa-file-alt\"\n            label=\"Rendicontazione\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Startup -->\n          <SidebarItem\n            to=\"/startup\"\n            icon=\"fas fa-rocket\"\n            label=\"Startup\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n\n          <!-- Admin (only for admins) -->\n          <SidebarItem\n            v-if=\"authStore.isAdmin\"\n            to=\"/admin\"\n            icon=\"fas fa-cog\"\n            label=\"Admin\"\n            :collapsed=\"!appStore.sidebarOpen\"\n          />\n        </nav>\n\n        <!-- User Section -->\n        <div class=\"flex-shrink-0 flex border-t border-brand-primary-800 dark:border-gray-700 p-2\">\n          <UserProfile :collapsed=\"!appStore.sidebarOpen\" />\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Mobile Sidebar -->\n  <MobileSidebar />\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Components\nimport SidebarItem from './SidebarItem.vue'\nimport SidebarGroup from './SidebarGroup.vue'\nimport UserProfile from './UserProfile.vue'\nimport MobileSidebar from './MobileSidebar.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\nconst brandStore = useBrandStore()\n\n// Personnel submenu items\nconst personnelItems = computed(() => {\n  const items = [\n    { to: '/personnel', label: 'Team', icon: '👥' },\n    { to: '/personnel/directory', label: 'Directory', icon: '📖' },\n    { to: '/personnel/orgchart', label: 'Organigramma', icon: '🏢' },\n    { to: '/personnel/skills', label: 'Competenze', icon: '🎯' }\n  ]\n\n  // Add admin-only items\n  if (authStore.isManager) {\n    items.push(\n      { to: '/personnel/departments', label: 'Dipartimenti', icon: '🏢' },\n      { to: '/personnel/admin', label: 'Amministrazione', icon: '⚙️' }\n    )\n  }\n\n  return items\n})\n</script>\n\n<style scoped>\n/* Sidebar animations */\n.sidebar-enter-active,\n.sidebar-leave-active {\n  transition: transform var(--brand-transition-normal);\n}\n\n.sidebar-enter-from,\n.sidebar-leave-to {\n  transform: translateX(-100%);\n}\n\n/* Custom scrollbar for sidebar */\n.overflow-y-auto::-webkit-scrollbar {\n  width: 4px;\n}\n\n.overflow-y-auto::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: var(--brand-radius-full);\n}\n\n.overflow-y-auto::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .hidden.md\\\\:flex {\n    display: none !important;\n  }\n}\n</style>\n"}