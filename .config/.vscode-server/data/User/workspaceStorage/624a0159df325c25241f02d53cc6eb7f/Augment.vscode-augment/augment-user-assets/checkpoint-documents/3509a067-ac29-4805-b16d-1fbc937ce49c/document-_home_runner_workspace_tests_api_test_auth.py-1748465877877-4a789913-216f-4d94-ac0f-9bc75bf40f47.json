{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/api/test_auth.py"}, "originalCode": "\"\"\"\nTest suite for Auth API endpoints.\n\"\"\"\nimport json\nimport pytest\nfrom datetime import datetime\n\n\nclass TestAuthAPI:\n    \"\"\"Test authentication API endpoints.\"\"\"\n\n    def test_get_current_user_success(self, client, auth, sample_users):\n        \"\"\"Test successful current user retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'user' in data['data']\n        \n        user = data['data']['user']\n        assert 'id' in user\n        assert 'username' in user\n        assert 'email' in user\n        assert 'full_name' in user\n        assert 'role' in user\n        assert 'permissions' in user\n        assert 'profile_completion' in user\n        assert 'preferences' in user\n        \n        # Check permissions structure\n        assert isinstance(user['permissions'], list)\n        \n        # Check preferences structure\n        preferences = user['preferences']\n        assert 'dark_mode' in preferences\n        assert 'language' in preferences\n        assert 'timezone' in preferences\n\n    def test_get_current_user_unauthorized(self, client, auth):\n        \"\"\"Test current user access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 401\n\n    def test_check_session_success(self, client, auth, sample_users):\n        \"\"\"Test successful session check.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/check-session')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        \n        session_data = data['data']\n        assert session_data['valid'] is True\n        assert 'user_id' in session_data\n        assert 'username' in session_data\n        assert 'role' in session_data\n        assert 'last_activity' in session_data\n\n    def test_check_session_unauthorized(self, client, auth):\n        \"\"\"Test session check without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/auth/check-session')\n        assert response.status_code == 401\n\n    def test_get_preferences_success(self, client, auth, sample_users):\n        \"\"\"Test successful preferences retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/preferences')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'preferences' in data['data']\n        \n        preferences = data['data']['preferences']\n        assert 'dark_mode' in preferences\n        assert 'language' in preferences\n        assert 'timezone' in preferences\n        assert 'notifications' in preferences\n\n    def test_update_preferences_success(self, client, auth, sample_users):\n        \"\"\"Test successful preferences update.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'dark_mode': True\n        }\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'preferences' in data['data']\n        \n        preferences = data['data']['preferences']\n        assert preferences['dark_mode'] is True\n\n    def test_update_preferences_invalid_data(self, client, auth, sample_users):\n        \"\"\"Test preferences update with invalid data.\"\"\"\n        auth.login()\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data='',\n            content_type='application/json'\n        )\n        assert response.status_code == 400\n        \n        data = json.loads(response.data)\n        assert data['success'] is False\n\n    def test_update_preferences_unauthorized(self, client, auth):\n        \"\"\"Test preferences update without authentication.\"\"\"\n        auth.logout()\n        \n        update_data = {\n            'dark_mode': True\n        }\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 401\n\n    def test_update_profile_success(self, client, auth, sample_users):\n        \"\"\"Test successful profile update.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'first_name': 'Mario',\n            'last_name': 'Rossi',\n            'phone': '+39 ************',\n            'bio': 'Software Developer',\n            'address': 'Via Roma 123, Milano',\n            'emergency_contact_name': 'Giulia Rossi',\n            'emergency_contact_phone': '+39 ************'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'user' in data['data']\n        \n        user = data['data']['user']\n        assert user['first_name'] == 'Mario'\n        assert user['last_name'] == 'Rossi'\n        assert user['full_name'] == 'Mario Rossi'\n        assert user['phone'] == '+39 ************'\n        assert user['bio'] == 'Software Developer'\n\n    def test_update_profile_partial_data(self, client, auth, sample_users):\n        \"\"\"Test profile update with partial data.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'first_name': 'Giovanni'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['user']['first_name'] == 'Giovanni'\n\n    def test_update_profile_invalid_data(self, client, auth, sample_users):\n        \"\"\"Test profile update with invalid data.\"\"\"\n        auth.login()\n        \n        response = client.put(\n            '/api/auth/profile',\n            data='',\n            content_type='application/json'\n        )\n        assert response.status_code == 400\n        \n        data = json.loads(response.data)\n        assert data['success'] is False\n\n    def test_update_profile_unauthorized(self, client, auth):\n        \"\"\"Test profile update without authentication.\"\"\"\n        auth.logout()\n        \n        update_data = {\n            'first_name': 'Mario'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 401\n\n\nclass TestUserPermissions:\n    \"\"\"Test user permissions functionality.\"\"\"\n\n    def test_admin_permissions(self, client, auth, sample_users):\n        \"\"\"Test admin user has all permissions.\"\"\"\n        # Login as admin (assuming first user is admin)\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        if user['role'] == 'admin':\n            permissions = user['permissions']\n            assert len(permissions) > 0\n            # Admin should have admin permission\n            assert 'admin' in permissions\n\n    def test_employee_permissions(self, client, auth, sample_users):\n        \"\"\"Test employee user has limited permissions.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        if user['role'] == 'employee':\n            permissions = user['permissions']\n            # Employee should have basic permissions\n            assert 'view_dashboard' in permissions\n            assert 'view_own_timesheets' in permissions\n            assert 'submit_timesheet' in permissions\n            # Employee should not have admin permissions\n            assert 'admin' not in permissions\n\n\nclass TestProfileCompletion:\n    \"\"\"Test profile completion calculation.\"\"\"\n\n    def test_profile_completion_calculation(self, client, auth, sample_users):\n        \"\"\"Test profile completion percentage calculation.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        # Profile completion should be a number between 0 and 100\n        assert 'profile_completion' in user\n        assert isinstance(user['profile_completion'], (int, float))\n        assert 0 <= user['profile_completion'] <= 100\n", "modifiedCode": "\"\"\"\nTest suite for Auth API endpoints.\n\"\"\"\nimport json\nimport pytest\nfrom datetime import datetime\n\n\nclass TestAuthAPI:\n    \"\"\"Test authentication API endpoints.\"\"\"\n\n    def test_get_current_user_success(self, client, auth, sample_users):\n        \"\"\"Test successful current user retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'user' in data['data']\n        \n        user = data['data']['user']\n        assert 'id' in user\n        assert 'username' in user\n        assert 'email' in user\n        assert 'full_name' in user\n        assert 'role' in user\n        assert 'permissions' in user\n        assert 'profile_completion' in user\n        assert 'preferences' in user\n        \n        # Check permissions structure\n        assert isinstance(user['permissions'], list)\n        \n        # Check preferences structure\n        preferences = user['preferences']\n        assert 'dark_mode' in preferences\n        assert 'language' in preferences\n        assert 'timezone' in preferences\n\n    def test_get_current_user_unauthorized(self, client, auth):\n        \"\"\"Test current user access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 401\n\n    def test_check_session_success(self, client, auth, sample_users):\n        \"\"\"Test successful session check.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/check-session')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        \n        session_data = data['data']\n        assert session_data['valid'] is True\n        assert 'user_id' in session_data\n        assert 'username' in session_data\n        assert 'role' in session_data\n        assert 'last_activity' in session_data\n\n    def test_check_session_unauthorized(self, client, auth):\n        \"\"\"Test session check without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/auth/check-session')\n        assert response.status_code == 401\n\n    def test_get_preferences_success(self, client, auth, sample_users):\n        \"\"\"Test successful preferences retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/preferences')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'preferences' in data['data']\n        \n        preferences = data['data']['preferences']\n        assert 'dark_mode' in preferences\n        assert 'language' in preferences\n        assert 'timezone' in preferences\n        assert 'notifications' in preferences\n\n    def test_update_preferences_success(self, client, auth, sample_users):\n        \"\"\"Test successful preferences update.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'dark_mode': True\n        }\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'preferences' in data['data']\n        \n        preferences = data['data']['preferences']\n        assert preferences['dark_mode'] is True\n\n    def test_update_preferences_invalid_data(self, client, auth, sample_users):\n        \"\"\"Test preferences update with invalid data.\"\"\"\n        auth.login()\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data='',\n            content_type='application/json'\n        )\n        assert response.status_code == 400\n        \n        data = json.loads(response.data)\n        assert data['success'] is False\n\n    def test_update_preferences_unauthorized(self, client, auth):\n        \"\"\"Test preferences update without authentication.\"\"\"\n        auth.logout()\n        \n        update_data = {\n            'dark_mode': True\n        }\n        \n        response = client.put(\n            '/api/auth/preferences',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 401\n\n    def test_update_profile_success(self, client, auth, sample_users):\n        \"\"\"Test successful profile update.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'first_name': 'Mario',\n            'last_name': 'Rossi',\n            'phone': '+39 ************',\n            'bio': 'Software Developer',\n            'address': 'Via Roma 123, Milano',\n            'emergency_contact_name': 'Giulia Rossi',\n            'emergency_contact_phone': '+39 ************'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'user' in data['data']\n        \n        user = data['data']['user']\n        assert user['first_name'] == 'Mario'\n        assert user['last_name'] == 'Rossi'\n        assert user['full_name'] == 'Mario Rossi'\n        assert user['phone'] == '+39 ************'\n        assert user['bio'] == 'Software Developer'\n\n    def test_update_profile_partial_data(self, client, auth, sample_users):\n        \"\"\"Test profile update with partial data.\"\"\"\n        auth.login()\n        \n        update_data = {\n            'first_name': 'Giovanni'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['user']['first_name'] == 'Giovanni'\n\n    def test_update_profile_invalid_data(self, client, auth, sample_users):\n        \"\"\"Test profile update with invalid data.\"\"\"\n        auth.login()\n        \n        response = client.put(\n            '/api/auth/profile',\n            data='',\n            content_type='application/json'\n        )\n        assert response.status_code == 400\n        \n        data = json.loads(response.data)\n        assert data['success'] is False\n\n    def test_update_profile_unauthorized(self, client, auth):\n        \"\"\"Test profile update without authentication.\"\"\"\n        auth.logout()\n        \n        update_data = {\n            'first_name': 'Mario'\n        }\n        \n        response = client.put(\n            '/api/auth/profile',\n            data=json.dumps(update_data),\n            content_type='application/json'\n        )\n        assert response.status_code == 401\n\n\nclass TestUserPermissions:\n    \"\"\"Test user permissions functionality.\"\"\"\n\n    def test_admin_permissions(self, client, auth, sample_users):\n        \"\"\"Test admin user has all permissions.\"\"\"\n        # Login as admin (assuming first user is admin)\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        if user['role'] == 'admin':\n            permissions = user['permissions']\n            assert len(permissions) > 0\n            # Admin should have admin permission\n            assert 'admin' in permissions\n\n    def test_employee_permissions(self, client, auth, sample_users):\n        \"\"\"Test employee user has limited permissions.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        if user['role'] == 'employee':\n            permissions = user['permissions']\n            # Employee should have basic permissions\n            assert 'view_dashboard' in permissions\n            assert 'view_own_timesheets' in permissions\n            assert 'submit_timesheet' in permissions\n            # Employee should not have admin permissions\n            assert 'admin' not in permissions\n\n\nclass TestProfileCompletion:\n    \"\"\"Test profile completion calculation.\"\"\"\n\n    def test_profile_completion_calculation(self, client, auth, sample_users):\n        \"\"\"Test profile completion percentage calculation.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/auth/me')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        user = data['data']['user']\n        \n        # Profile completion should be a number between 0 and 100\n        assert 'profile_completion' in user\n        assert isinstance(user['profile_completion'], (int, float))\n        assert 0 <= user['profile_completion'] <= 100\n"}