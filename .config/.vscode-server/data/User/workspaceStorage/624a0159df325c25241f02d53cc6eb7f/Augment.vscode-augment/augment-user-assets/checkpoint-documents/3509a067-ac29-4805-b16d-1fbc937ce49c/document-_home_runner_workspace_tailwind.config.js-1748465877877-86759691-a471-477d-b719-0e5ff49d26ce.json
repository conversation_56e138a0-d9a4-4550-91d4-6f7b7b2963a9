{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tailwind.config.js"}, "originalCode": "/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    \"./templates/**/*.html\",\n    \"./static/js/**/*.js\",\n    \"./static/js/**/*.vue\",\n    \"./static/js/**/*.ts\",\n    \"./static/css/**/*.css\"\n  ],\n  darkMode: 'class',\n  theme: {\n    extend: {\n      // Brand Colors usando CSS Variables\n      colors: {\n        // Primary Brand Colors\n        'brand-primary': {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        // Secondary Brand Colors\n        'brand-secondary': {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        },\n        // Accent Brand Colors\n        'brand-accent': {\n          50: 'var(--brand-accent-50)',\n          100: 'var(--brand-accent-100)',\n          200: 'var(--brand-accent-200)',\n          300: 'var(--brand-accent-300)',\n          400: 'var(--brand-accent-400)',\n          500: 'var(--brand-accent-500)',\n          600: 'var(--brand-accent-600)',\n          700: 'var(--brand-accent-700)',\n          800: 'var(--brand-accent-800)',\n          900: 'var(--brand-accent-900)',\n        },\n        // Semantic Colors\n        'brand-success': {\n          50: 'var(--brand-success-50)',\n          500: 'var(--brand-success-500)',\n          600: 'var(--brand-success-600)',\n          700: 'var(--brand-success-700)',\n        },\n        'brand-warning': {\n          50: 'var(--brand-warning-50)',\n          500: 'var(--brand-warning-500)',\n          600: 'var(--brand-warning-600)',\n          700: 'var(--brand-warning-700)',\n        },\n        'brand-error': {\n          50: 'var(--brand-error-50)',\n          500: 'var(--brand-error-500)',\n          600: 'var(--brand-error-600)',\n          700: 'var(--brand-error-700)',\n        },\n        // Context Colors\n        'brand-bg': {\n          primary: 'var(--brand-bg-primary)',\n          secondary: 'var(--brand-bg-secondary)',\n          tertiary: 'var(--brand-bg-tertiary)',\n        },\n        'brand-text': {\n          primary: 'var(--brand-text-primary)',\n          secondary: 'var(--brand-text-secondary)',\n          tertiary: 'var(--brand-text-tertiary)',\n        },\n        'brand-border': {\n          primary: 'var(--brand-border-primary)',\n          secondary: 'var(--brand-border-secondary)',\n        },\n        // Backward compatibility (manteniamo i vecchi nomi)\n        primary: {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        secondary: {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        }\n      },\n      \n      // Brand Typography\n      fontFamily: {\n        'brand-heading': 'var(--brand-font-heading)',\n        'brand-body': 'var(--brand-font-body)',\n        'brand-mono': 'var(--brand-font-mono)',\n        // Backward compatibility\n        'sans': 'var(--brand-font-body)',\n        'mono': 'var(--brand-font-mono)',\n      },\n      \n      // Brand Font Weights\n      fontWeight: {\n        'brand-light': 'var(--brand-font-weight-light)',\n        'brand-normal': 'var(--brand-font-weight-normal)',\n        'brand-medium': 'var(--brand-font-weight-medium)',\n        'brand-semibold': 'var(--brand-font-weight-semibold)',\n        'brand-bold': 'var(--brand-font-weight-bold)',\n        'brand-extrabold': 'var(--brand-font-weight-extrabold)',\n      },\n      \n      // Brand Font Sizes\n      fontSize: {\n        'brand-xs': 'var(--brand-text-xs)',\n        'brand-sm': 'var(--brand-text-sm)',\n        'brand-base': 'var(--brand-text-base)',\n        'brand-lg': 'var(--brand-text-lg)',\n        'brand-xl': 'var(--brand-text-xl)',\n        'brand-2xl': 'var(--brand-text-2xl)',\n        'brand-3xl': 'var(--brand-text-3xl)',\n        'brand-4xl': 'var(--brand-text-4xl)',\n      },\n      \n      // Brand Spacing\n      spacing: {\n        'brand-xs': 'var(--brand-spacing-xs)',\n        'brand-sm': 'var(--brand-spacing-sm)',\n        'brand-md': 'var(--brand-spacing-md)',\n        'brand-lg': 'var(--brand-spacing-lg)',\n        'brand-xl': 'var(--brand-spacing-xl)',\n        'brand-2xl': 'var(--brand-spacing-2xl)',\n        'brand-3xl': 'var(--brand-spacing-3xl)',\n        'sidebar': 'var(--brand-sidebar-width)',\n        'sidebar-collapsed': 'var(--brand-sidebar-collapsed-width)',\n        'header': 'var(--brand-header-height)',\n      },\n      \n      // Brand Border Radius\n      borderRadius: {\n        'brand-none': 'var(--brand-radius-none)',\n        'brand-sm': 'var(--brand-radius-sm)',\n        'brand-md': 'var(--brand-radius-md)',\n        'brand-lg': 'var(--brand-radius-lg)',\n        'brand-xl': 'var(--brand-radius-xl)',\n        'brand-2xl': 'var(--brand-radius-2xl)',\n        'brand-full': 'var(--brand-radius-full)',\n      },\n      \n      // Brand Shadows\n      boxShadow: {\n        'brand-sm': 'var(--brand-shadow-sm)',\n        'brand-md': 'var(--brand-shadow-md)',\n        'brand-lg': 'var(--brand-shadow-lg)',\n        'brand-xl': 'var(--brand-shadow-xl)',\n      },\n      \n      // Brand Transitions\n      transitionDuration: {\n        'brand-fast': 'var(--brand-transition-fast)',\n        'brand-normal': 'var(--brand-transition-normal)',\n        'brand-slow': 'var(--brand-transition-slow)',\n      },\n      \n      // Brand Layout\n      maxWidth: {\n        'brand-container': 'var(--brand-container-max-width)',\n      },\n      \n      // Brand Background Images (for logos)\n      backgroundImage: {\n        'brand-logo-main': 'var(--brand-logo-main)',\n        'brand-logo-compact': 'var(--brand-logo-compact)',\n        'brand-logo-white': 'var(--brand-logo-white)',\n        'brand-logo-dark': 'var(--brand-logo-dark)',\n      },\n      \n      // Animation & Keyframes\n      animation: {\n        'brand-fade-in': 'brandFadeIn var(--brand-transition-normal) ease-out',\n        'brand-slide-up': 'brandSlideUp var(--brand-transition-normal) ease-out',\n        'brand-scale-in': 'brandScaleIn var(--brand-transition-fast) ease-out',\n        'brand-bounce-in': 'brandBounceIn var(--brand-transition-slow) ease-out',\n      },\n      \n      keyframes: {\n        brandFadeIn: {\n          '0%': { opacity: '0' },\n          '100%': { opacity: '1' },\n        },\n        brandSlideUp: {\n          '0%': { transform: 'translateY(20px)', opacity: '0' },\n          '100%': { transform: 'translateY(0)', opacity: '1' },\n        },\n        brandScaleIn: {\n          '0%': { transform: 'scale(0.95)', opacity: '0' },\n          '100%': { transform: 'scale(1)', opacity: '1' },\n        },\n        brandBounceIn: {\n          '0%': { transform: 'scale(0.3)', opacity: '0' },\n          '50%': { transform: 'scale(1.05)' },\n          '70%': { transform: 'scale(0.9)' },\n          '100%': { transform: 'scale(1)', opacity: '1' },\n        },\n      },\n    },\n  },\n  plugins: [\n    // Plugin per componenti brand personalizzati\n    function({ addComponents, theme }) {\n      addComponents({\n        '.btn-brand-primary': {\n          backgroundColor: theme('colors.brand-primary.500'),\n          color: theme('colors.white'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          fontFamily: theme('fontFamily.brand-body'),\n          fontWeight: theme('fontWeight.brand-medium'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          boxShadow: theme('boxShadow.brand-sm'),\n          '&:hover': {\n            backgroundColor: theme('colors.brand-primary.600'),\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-1px)',\n          },\n          '&:active': {\n            transform: 'translateY(0)',\n          },\n        },\n        '.btn-brand-secondary': {\n          backgroundColor: theme('colors.brand-secondary.500'),\n          color: theme('colors.white'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          fontFamily: theme('fontFamily.brand-body'),\n          fontWeight: theme('fontWeight.brand-medium'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          boxShadow: theme('boxShadow.brand-sm'),\n          '&:hover': {\n            backgroundColor: theme('colors.brand-secondary.600'),\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-1px)',\n          },\n        },\n        '.card-brand': {\n          backgroundColor: theme('colors.brand-bg.primary'),\n          border: `1px solid ${theme('colors.brand-border.primary')}`,\n          borderRadius: theme('borderRadius.brand-lg'),\n          boxShadow: theme('boxShadow.brand-sm'),\n          transition: `all ${theme('transitionDuration.brand-normal')} ease-in-out`,\n          '&:hover': {\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-2px)',\n          },\n        },\n        '.input-brand': {\n          backgroundColor: theme('colors.brand-bg.primary'),\n          border: `1px solid ${theme('colors.brand-border.secondary')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          fontFamily: theme('fontFamily.brand-body'),\n          color: theme('colors.brand-text.primary'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          '&:focus': {\n            outline: 'none',\n            borderColor: theme('colors.brand-primary.500'),\n            boxShadow: `0 0 0 3px ${theme('colors.brand-primary.500')}20`,\n          },\n        },\n      })\n    },\n  ],\n}\n", "modifiedCode": "/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    \"./templates/**/*.html\",\n    \"./static/js/**/*.js\",\n    \"./static/js/**/*.vue\",\n    \"./static/js/**/*.ts\",\n    \"./static/css/**/*.css\"\n  ],\n  darkMode: 'class',\n  theme: {\n    extend: {\n      // Brand Colors usando CSS Variables\n      colors: {\n        // Primary Brand Colors\n        'brand-primary': {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        // Secondary Brand Colors\n        'brand-secondary': {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        },\n        // Accent Brand Colors\n        'brand-accent': {\n          50: 'var(--brand-accent-50)',\n          100: 'var(--brand-accent-100)',\n          200: 'var(--brand-accent-200)',\n          300: 'var(--brand-accent-300)',\n          400: 'var(--brand-accent-400)',\n          500: 'var(--brand-accent-500)',\n          600: 'var(--brand-accent-600)',\n          700: 'var(--brand-accent-700)',\n          800: 'var(--brand-accent-800)',\n          900: 'var(--brand-accent-900)',\n        },\n        // Semantic Colors\n        'brand-success': {\n          50: 'var(--brand-success-50)',\n          500: 'var(--brand-success-500)',\n          600: 'var(--brand-success-600)',\n          700: 'var(--brand-success-700)',\n        },\n        'brand-warning': {\n          50: 'var(--brand-warning-50)',\n          500: 'var(--brand-warning-500)',\n          600: 'var(--brand-warning-600)',\n          700: 'var(--brand-warning-700)',\n        },\n        'brand-error': {\n          50: 'var(--brand-error-50)',\n          500: 'var(--brand-error-500)',\n          600: 'var(--brand-error-600)',\n          700: 'var(--brand-error-700)',\n        },\n        // Context Colors\n        'brand-bg': {\n          primary: 'var(--brand-bg-primary)',\n          secondary: 'var(--brand-bg-secondary)',\n          tertiary: 'var(--brand-bg-tertiary)',\n        },\n        'brand-text': {\n          primary: 'var(--brand-text-primary)',\n          secondary: 'var(--brand-text-secondary)',\n          tertiary: 'var(--brand-text-tertiary)',\n        },\n        'brand-border': {\n          primary: 'var(--brand-border-primary)',\n          secondary: 'var(--brand-border-secondary)',\n        },\n        // Backward compatibility (manteniamo i vecchi nomi)\n        primary: {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        secondary: {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        }\n      },\n      \n      // Brand Typography\n      fontFamily: {\n        'brand-heading': 'var(--brand-font-heading)',\n        'brand-body': 'var(--brand-font-body)',\n        'brand-mono': 'var(--brand-font-mono)',\n        // Backward compatibility\n        'sans': 'var(--brand-font-body)',\n        'mono': 'var(--brand-font-mono)',\n      },\n      \n      // Brand Font Weights\n      fontWeight: {\n        'brand-light': 'var(--brand-font-weight-light)',\n        'brand-normal': 'var(--brand-font-weight-normal)',\n        'brand-medium': 'var(--brand-font-weight-medium)',\n        'brand-semibold': 'var(--brand-font-weight-semibold)',\n        'brand-bold': 'var(--brand-font-weight-bold)',\n        'brand-extrabold': 'var(--brand-font-weight-extrabold)',\n      },\n      \n      // Brand Font Sizes\n      fontSize: {\n        'brand-xs': 'var(--brand-text-xs)',\n        'brand-sm': 'var(--brand-text-sm)',\n        'brand-base': 'var(--brand-text-base)',\n        'brand-lg': 'var(--brand-text-lg)',\n        'brand-xl': 'var(--brand-text-xl)',\n        'brand-2xl': 'var(--brand-text-2xl)',\n        'brand-3xl': 'var(--brand-text-3xl)',\n        'brand-4xl': 'var(--brand-text-4xl)',\n      },\n      \n      // Brand Spacing\n      spacing: {\n        'brand-xs': 'var(--brand-spacing-xs)',\n        'brand-sm': 'var(--brand-spacing-sm)',\n        'brand-md': 'var(--brand-spacing-md)',\n        'brand-lg': 'var(--brand-spacing-lg)',\n        'brand-xl': 'var(--brand-spacing-xl)',\n        'brand-2xl': 'var(--brand-spacing-2xl)',\n        'brand-3xl': 'var(--brand-spacing-3xl)',\n        'sidebar': 'var(--brand-sidebar-width)',\n        'sidebar-collapsed': 'var(--brand-sidebar-collapsed-width)',\n        'header': 'var(--brand-header-height)',\n      },\n      \n      // Brand Border Radius\n      borderRadius: {\n        'brand-none': 'var(--brand-radius-none)',\n        'brand-sm': 'var(--brand-radius-sm)',\n        'brand-md': 'var(--brand-radius-md)',\n        'brand-lg': 'var(--brand-radius-lg)',\n        'brand-xl': 'var(--brand-radius-xl)',\n        'brand-2xl': 'var(--brand-radius-2xl)',\n        'brand-full': 'var(--brand-radius-full)',\n      },\n      \n      // Brand Shadows\n      boxShadow: {\n        'brand-sm': 'var(--brand-shadow-sm)',\n        'brand-md': 'var(--brand-shadow-md)',\n        'brand-lg': 'var(--brand-shadow-lg)',\n        'brand-xl': 'var(--brand-shadow-xl)',\n      },\n      \n      // Brand Transitions\n      transitionDuration: {\n        'brand-fast': 'var(--brand-transition-fast)',\n        'brand-normal': 'var(--brand-transition-normal)',\n        'brand-slow': 'var(--brand-transition-slow)',\n      },\n      \n      // Brand Layout\n      maxWidth: {\n        'brand-container': 'var(--brand-container-max-width)',\n      },\n      \n      // Brand Background Images (for logos)\n      backgroundImage: {\n        'brand-logo-main': 'var(--brand-logo-main)',\n        'brand-logo-compact': 'var(--brand-logo-compact)',\n        'brand-logo-white': 'var(--brand-logo-white)',\n        'brand-logo-dark': 'var(--brand-logo-dark)',\n      },\n      \n      // Animation & Keyframes\n      animation: {\n        'brand-fade-in': 'brandFadeIn var(--brand-transition-normal) ease-out',\n        'brand-slide-up': 'brandSlideUp var(--brand-transition-normal) ease-out',\n        'brand-scale-in': 'brandScaleIn var(--brand-transition-fast) ease-out',\n        'brand-bounce-in': 'brandBounceIn var(--brand-transition-slow) ease-out',\n      },\n      \n      keyframes: {\n        brandFadeIn: {\n          '0%': { opacity: '0' },\n          '100%': { opacity: '1' },\n        },\n        brandSlideUp: {\n          '0%': { transform: 'translateY(20px)', opacity: '0' },\n          '100%': { transform: 'translateY(0)', opacity: '1' },\n        },\n        brandScaleIn: {\n          '0%': { transform: 'scale(0.95)', opacity: '0' },\n          '100%': { transform: 'scale(1)', opacity: '1' },\n        },\n        brandBounceIn: {\n          '0%': { transform: 'scale(0.3)', opacity: '0' },\n          '50%': { transform: 'scale(1.05)' },\n          '70%': { transform: 'scale(0.9)' },\n          '100%': { transform: 'scale(1)', opacity: '1' },\n        },\n      },\n    },\n  },\n  plugins: [\n    // Plugin per componenti brand personalizzati\n    function({ addComponents, theme }) {\n      addComponents({\n        '.btn-brand-primary': {\n          backgroundColor: theme('colors.brand-primary.500'),\n          color: theme('colors.white'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          fontFamily: theme('fontFamily.brand-body'),\n          fontWeight: theme('fontWeight.brand-medium'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          boxShadow: theme('boxShadow.brand-sm'),\n          '&:hover': {\n            backgroundColor: theme('colors.brand-primary.600'),\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-1px)',\n          },\n          '&:active': {\n            transform: 'translateY(0)',\n          },\n        },\n        '.btn-brand-secondary': {\n          backgroundColor: theme('colors.brand-secondary.500'),\n          color: theme('colors.white'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          fontFamily: theme('fontFamily.brand-body'),\n          fontWeight: theme('fontWeight.brand-medium'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          boxShadow: theme('boxShadow.brand-sm'),\n          '&:hover': {\n            backgroundColor: theme('colors.brand-secondary.600'),\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-1px)',\n          },\n        },\n        '.card-brand': {\n          backgroundColor: theme('colors.brand-bg.primary'),\n          border: `1px solid ${theme('colors.brand-border.primary')}`,\n          borderRadius: theme('borderRadius.brand-lg'),\n          boxShadow: theme('boxShadow.brand-sm'),\n          transition: `all ${theme('transitionDuration.brand-normal')} ease-in-out`,\n          '&:hover': {\n            boxShadow: theme('boxShadow.brand-md'),\n            transform: 'translateY(-2px)',\n          },\n        },\n        '.input-brand': {\n          backgroundColor: theme('colors.brand-bg.primary'),\n          border: `1px solid ${theme('colors.brand-border.secondary')}`,\n          borderRadius: theme('borderRadius.brand-md'),\n          padding: `${theme('spacing.brand-sm')} ${theme('spacing.brand-md')}`,\n          fontFamily: theme('fontFamily.brand-body'),\n          color: theme('colors.brand-text.primary'),\n          transition: `all ${theme('transitionDuration.brand-fast')} ease-in-out`,\n          '&:focus': {\n            outline: 'none',\n            borderColor: theme('colors.brand-primary.500'),\n            boxShadow: `0 0 0 3px ${theme('colors.brand-primary.500')}20`,\n          },\n        },\n      })\n    },\n  ],\n}\n"}