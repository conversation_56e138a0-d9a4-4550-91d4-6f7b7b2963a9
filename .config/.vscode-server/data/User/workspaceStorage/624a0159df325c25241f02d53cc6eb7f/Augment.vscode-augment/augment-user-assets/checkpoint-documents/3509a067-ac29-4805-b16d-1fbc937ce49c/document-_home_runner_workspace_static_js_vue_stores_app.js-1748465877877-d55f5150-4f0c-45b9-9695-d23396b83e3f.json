{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/app.js"}, "originalCode": "/**\n * App Store - Pinia Store per stato globale dell'applicazione\n * Gestisce loading, notifiche, sidebar, modali e stato generale\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useAppStore = defineStore('app', () => {\n  // === STATE ===\n  \n  // Loading states\n  const isLoading = ref(false)\n  const loadingMessage = ref('')\n  const isInitialized = ref(false)\n  \n  // Sidebar state\n  const sidebarOpen = ref(true)\n  const sidebarCollapsed = ref(false)\n  \n  // Notifications\n  const notifications = ref([])\n  const maxNotifications = ref(5)\n  \n  // Modals\n  const activeModals = ref([])\n  \n  // Page state\n  const currentPage = ref('')\n  const pageTitle = ref('DatPortal')\n  const breadcrumbs = ref([])\n  \n  // Theme\n  const darkMode = ref(false)\n  \n  // Mobile detection\n  const isMobile = ref(false)\n  const isTablet = ref(false)\n  \n  // Network status\n  const isOnline = ref(navigator.onLine)\n  \n  // === COMPUTED ===\n  \n  const hasNotifications = computed(() => notifications.value.length > 0)\n  const unreadNotifications = computed(() => \n    notifications.value.filter(n => !n.read).length\n  )\n  const hasActiveModals = computed(() => activeModals.value.length > 0)\n  const isDesktop = computed(() => !isMobile.value && !isTablet.value)\n  \n  // === ACTIONS ===\n  \n  /**\n   * Set loading state\n   * @param {boolean} loading - Loading state\n   * @param {string} message - Optional loading message\n   */\n  function setLoading(loading, message = '') {\n    isLoading.value = loading\n    loadingMessage.value = message\n  }\n  \n  /**\n   * Set initialized state\n   * @param {boolean} initialized - Initialized state\n   */\n  function setInitialized(initialized) {\n    isInitialized.value = initialized\n  }\n  \n  /**\n   * Toggle sidebar\n   */\n  function toggleSidebar() {\n    sidebarOpen.value = !sidebarOpen.value\n    localStorage.setItem('sidebarOpen', sidebarOpen.value.toString())\n  }\n  \n  /**\n   * Set sidebar state\n   * @param {boolean} open - Sidebar open state\n   */\n  function setSidebarOpen(open) {\n    sidebarOpen.value = open\n    localStorage.setItem('sidebarOpen', open.toString())\n  }\n  \n  /**\n   * Toggle sidebar collapsed state\n   */\n  function toggleSidebarCollapsed() {\n    sidebarCollapsed.value = !sidebarCollapsed.value\n    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())\n  }\n  \n  /**\n   * Show notification\n   * @param {string} message - Notification message\n   * @param {string} type - Notification type (success, error, warning, info)\n   * @param {number} duration - Auto-hide duration in ms (0 = no auto-hide)\n   * @param {Object} options - Additional options\n   */\n  function showNotification(message, type = 'info', duration = 5000, options = {}) {\n    const notification = {\n      id: Date.now() + Math.random(),\n      message,\n      type,\n      read: false,\n      timestamp: new Date(),\n      duration,\n      ...options\n    }\n    \n    notifications.value.unshift(notification)\n    \n    // Limit number of notifications\n    if (notifications.value.length > maxNotifications.value) {\n      notifications.value = notifications.value.slice(0, maxNotifications.value)\n    }\n    \n    // Auto-hide notification\n    if (duration > 0) {\n      setTimeout(() => {\n        hideNotification(notification.id)\n      }, duration)\n    }\n    \n    return notification.id\n  }\n  \n  /**\n   * Hide notification\n   * @param {string|number} id - Notification ID\n   */\n  function hideNotification(id) {\n    const index = notifications.value.findIndex(n => n.id === id)\n    if (index > -1) {\n      notifications.value.splice(index, 1)\n    }\n  }\n  \n  /**\n   * Mark notification as read\n   * @param {string|number} id - Notification ID\n   */\n  function markNotificationRead(id) {\n    const notification = notifications.value.find(n => n.id === id)\n    if (notification) {\n      notification.read = true\n    }\n  }\n  \n  /**\n   * Clear all notifications\n   */\n  function clearNotifications() {\n    notifications.value = []\n  }\n  \n  /**\n   * Show modal\n   * @param {string} component - Modal component name\n   * @param {Object} props - Props to pass to modal\n   * @param {Object} options - Modal options\n   */\n  function showModal(component, props = {}, options = {}) {\n    const modal = {\n      id: Date.now() + Math.random(),\n      component,\n      props,\n      options: {\n        closable: true,\n        backdrop: true,\n        ...options\n      }\n    }\n    \n    activeModals.value.push(modal)\n    \n    // Prevent body scroll when modal is open\n    document.body.style.overflow = 'hidden'\n    \n    return modal.id\n  }\n  \n  /**\n   * Hide modal\n   * @param {string|number} id - Modal ID\n   */\n  function hideModal(id) {\n    const index = activeModals.value.findIndex(m => m.id === id)\n    if (index > -1) {\n      activeModals.value.splice(index, 1)\n    }\n    \n    // Restore body scroll if no modals are open\n    if (activeModals.value.length === 0) {\n      document.body.style.overflow = ''\n    }\n  }\n  \n  /**\n   * Hide all modals\n   */\n  function hideAllModals() {\n    activeModals.value = []\n    document.body.style.overflow = ''\n  }\n  \n  /**\n   * Set current page\n   * @param {string} page - Page name\n   * @param {string} title - Page title\n   * @param {Array} breadcrumbsArray - Breadcrumbs array\n   */\n  function setCurrentPage(page, title = '', breadcrumbsArray = []) {\n    currentPage.value = page\n    pageTitle.value = title || 'DatPortal'\n    breadcrumbs.value = breadcrumbsArray\n    \n    // Update document title\n    document.title = title ? `${title} - DatPortal` : 'DatPortal'\n  }\n  \n  /**\n   * Toggle dark mode\n   */\n  function toggleDarkMode() {\n    darkMode.value = !darkMode.value\n    \n    // Apply to document\n    if (darkMode.value) {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n    \n    // Save preference\n    localStorage.setItem('darkMode', darkMode.value.toString())\n    \n    // Emit event for other components\n    window.dispatchEvent(new CustomEvent('darkModeChanged', {\n      detail: { darkMode: darkMode.value }\n    }))\n  }\n  \n  /**\n   * Set dark mode\n   * @param {boolean} enabled - Dark mode enabled\n   */\n  function setDarkMode(enabled) {\n    darkMode.value = enabled\n    \n    if (enabled) {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n    \n    localStorage.setItem('darkMode', enabled.toString())\n    \n    window.dispatchEvent(new CustomEvent('darkModeChanged', {\n      detail: { darkMode: enabled }\n    }))\n  }\n  \n  /**\n   * Update screen size detection\n   */\n  function updateScreenSize() {\n    const width = window.innerWidth\n    isMobile.value = width < 768\n    isTablet.value = width >= 768 && width < 1024\n  }\n  \n  /**\n   * Initialize app store\n   */\n  function initialize() {\n    // Load saved preferences\n    const savedSidebarOpen = localStorage.getItem('sidebarOpen')\n    if (savedSidebarOpen !== null) {\n      sidebarOpen.value = savedSidebarOpen === 'true'\n    }\n    \n    const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')\n    if (savedSidebarCollapsed !== null) {\n      sidebarCollapsed.value = savedSidebarCollapsed === 'true'\n    }\n    \n    const savedDarkMode = localStorage.getItem('darkMode')\n    if (savedDarkMode !== null) {\n      setDarkMode(savedDarkMode === 'true')\n    }\n    \n    // Set up screen size detection\n    updateScreenSize()\n    window.addEventListener('resize', updateScreenSize)\n    \n    // Set up online/offline detection\n    window.addEventListener('online', () => {\n      isOnline.value = true\n      showNotification('Connessione ripristinata', 'success', 3000)\n    })\n    \n    window.addEventListener('offline', () => {\n      isOnline.value = false\n      showNotification('Connessione persa', 'warning', 0)\n    })\n    \n    // Auto-collapse sidebar on mobile\n    if (isMobile.value) {\n      sidebarOpen.value = false\n    }\n  }\n  \n  /**\n   * Handle API errors globally\n   * @param {Error} error - Error object\n   * @param {string} context - Error context\n   */\n  function handleApiError(error, context = '') {\n    console.error('API Error:', error, context)\n    \n    let message = 'Si è verificato un errore'\n    \n    if (error.response) {\n      // Server responded with error status\n      const status = error.response.status\n      \n      switch (status) {\n        case 401:\n          message = 'Sessione scaduta. Effettua nuovamente il login.'\n          // Redirect to login after a delay\n          setTimeout(() => {\n            window.location.href = '/auth/login'\n          }, 2000)\n          break\n        case 403:\n          message = 'Non hai i permessi per eseguire questa operazione'\n          break\n        case 404:\n          message = 'Risorsa non trovata'\n          break\n        case 422:\n          message = 'Dati non validi'\n          break\n        case 500:\n          message = 'Errore interno del server'\n          break\n        default:\n          message = `Errore ${status}: ${error.response.statusText}`\n      }\n    } else if (error.request) {\n      // Network error\n      message = 'Errore di connessione. Verifica la tua connessione internet.'\n    }\n    \n    showNotification(message, 'error', 8000)\n  }\n  \n  // Initialize store\n  initialize()\n  \n  return {\n    // State\n    isLoading,\n    loadingMessage,\n    isInitialized,\n    sidebarOpen,\n    sidebarCollapsed,\n    notifications,\n    activeModals,\n    currentPage,\n    pageTitle,\n    breadcrumbs,\n    darkMode,\n    isMobile,\n    isTablet,\n    isOnline,\n    \n    // Computed\n    hasNotifications,\n    unreadNotifications,\n    hasActiveModals,\n    isDesktop,\n    \n    // Actions\n    setLoading,\n    setInitialized,\n    toggleSidebar,\n    setSidebarOpen,\n    toggleSidebarCollapsed,\n    showNotification,\n    hideNotification,\n    markNotificationRead,\n    clearNotifications,\n    showModal,\n    hideModal,\n    hideAllModals,\n    setCurrentPage,\n    toggleDarkMode,\n    setDarkMode,\n    updateScreenSize,\n    initialize,\n    handleApiError\n  }\n})\n", "modifiedCode": "/**\n * App Store - Pinia Store per stato globale dell'applicazione\n * Gestisce loading, notifiche, sidebar, modali e stato generale\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useAppStore = defineStore('app', () => {\n  // === STATE ===\n  \n  // Loading states\n  const isLoading = ref(false)\n  const loadingMessage = ref('')\n  const isInitialized = ref(false)\n  \n  // Sidebar state\n  const sidebarOpen = ref(true)\n  const sidebarCollapsed = ref(false)\n  \n  // Notifications\n  const notifications = ref([])\n  const maxNotifications = ref(5)\n  \n  // Modals\n  const activeModals = ref([])\n  \n  // Page state\n  const currentPage = ref('')\n  const pageTitle = ref('DatPortal')\n  const breadcrumbs = ref([])\n  \n  // Theme\n  const darkMode = ref(false)\n  \n  // Mobile detection\n  const isMobile = ref(false)\n  const isTablet = ref(false)\n  \n  // Network status\n  const isOnline = ref(navigator.onLine)\n  \n  // === COMPUTED ===\n  \n  const hasNotifications = computed(() => notifications.value.length > 0)\n  const unreadNotifications = computed(() => \n    notifications.value.filter(n => !n.read).length\n  )\n  const hasActiveModals = computed(() => activeModals.value.length > 0)\n  const isDesktop = computed(() => !isMobile.value && !isTablet.value)\n  \n  // === ACTIONS ===\n  \n  /**\n   * Set loading state\n   * @param {boolean} loading - Loading state\n   * @param {string} message - Optional loading message\n   */\n  function setLoading(loading, message = '') {\n    isLoading.value = loading\n    loadingMessage.value = message\n  }\n  \n  /**\n   * Set initialized state\n   * @param {boolean} initialized - Initialized state\n   */\n  function setInitialized(initialized) {\n    isInitialized.value = initialized\n  }\n  \n  /**\n   * Toggle sidebar\n   */\n  function toggleSidebar() {\n    sidebarOpen.value = !sidebarOpen.value\n    localStorage.setItem('sidebarOpen', sidebarOpen.value.toString())\n  }\n  \n  /**\n   * Set sidebar state\n   * @param {boolean} open - Sidebar open state\n   */\n  function setSidebarOpen(open) {\n    sidebarOpen.value = open\n    localStorage.setItem('sidebarOpen', open.toString())\n  }\n  \n  /**\n   * Toggle sidebar collapsed state\n   */\n  function toggleSidebarCollapsed() {\n    sidebarCollapsed.value = !sidebarCollapsed.value\n    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())\n  }\n  \n  /**\n   * Show notification\n   * @param {string} message - Notification message\n   * @param {string} type - Notification type (success, error, warning, info)\n   * @param {number} duration - Auto-hide duration in ms (0 = no auto-hide)\n   * @param {Object} options - Additional options\n   */\n  function showNotification(message, type = 'info', duration = 5000, options = {}) {\n    const notification = {\n      id: Date.now() + Math.random(),\n      message,\n      type,\n      read: false,\n      timestamp: new Date(),\n      duration,\n      ...options\n    }\n    \n    notifications.value.unshift(notification)\n    \n    // Limit number of notifications\n    if (notifications.value.length > maxNotifications.value) {\n      notifications.value = notifications.value.slice(0, maxNotifications.value)\n    }\n    \n    // Auto-hide notification\n    if (duration > 0) {\n      setTimeout(() => {\n        hideNotification(notification.id)\n      }, duration)\n    }\n    \n    return notification.id\n  }\n  \n  /**\n   * Hide notification\n   * @param {string|number} id - Notification ID\n   */\n  function hideNotification(id) {\n    const index = notifications.value.findIndex(n => n.id === id)\n    if (index > -1) {\n      notifications.value.splice(index, 1)\n    }\n  }\n  \n  /**\n   * Mark notification as read\n   * @param {string|number} id - Notification ID\n   */\n  function markNotificationRead(id) {\n    const notification = notifications.value.find(n => n.id === id)\n    if (notification) {\n      notification.read = true\n    }\n  }\n  \n  /**\n   * Clear all notifications\n   */\n  function clearNotifications() {\n    notifications.value = []\n  }\n  \n  /**\n   * Show modal\n   * @param {string} component - Modal component name\n   * @param {Object} props - Props to pass to modal\n   * @param {Object} options - Modal options\n   */\n  function showModal(component, props = {}, options = {}) {\n    const modal = {\n      id: Date.now() + Math.random(),\n      component,\n      props,\n      options: {\n        closable: true,\n        backdrop: true,\n        ...options\n      }\n    }\n    \n    activeModals.value.push(modal)\n    \n    // Prevent body scroll when modal is open\n    document.body.style.overflow = 'hidden'\n    \n    return modal.id\n  }\n  \n  /**\n   * Hide modal\n   * @param {string|number} id - Modal ID\n   */\n  function hideModal(id) {\n    const index = activeModals.value.findIndex(m => m.id === id)\n    if (index > -1) {\n      activeModals.value.splice(index, 1)\n    }\n    \n    // Restore body scroll if no modals are open\n    if (activeModals.value.length === 0) {\n      document.body.style.overflow = ''\n    }\n  }\n  \n  /**\n   * Hide all modals\n   */\n  function hideAllModals() {\n    activeModals.value = []\n    document.body.style.overflow = ''\n  }\n  \n  /**\n   * Set current page\n   * @param {string} page - Page name\n   * @param {string} title - Page title\n   * @param {Array} breadcrumbsArray - Breadcrumbs array\n   */\n  function setCurrentPage(page, title = '', breadcrumbsArray = []) {\n    currentPage.value = page\n    pageTitle.value = title || 'DatPortal'\n    breadcrumbs.value = breadcrumbsArray\n    \n    // Update document title\n    document.title = title ? `${title} - DatPortal` : 'DatPortal'\n  }\n  \n  /**\n   * Toggle dark mode\n   */\n  function toggleDarkMode() {\n    darkMode.value = !darkMode.value\n    \n    // Apply to document\n    if (darkMode.value) {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n    \n    // Save preference\n    localStorage.setItem('darkMode', darkMode.value.toString())\n    \n    // Emit event for other components\n    window.dispatchEvent(new CustomEvent('darkModeChanged', {\n      detail: { darkMode: darkMode.value }\n    }))\n  }\n  \n  /**\n   * Set dark mode\n   * @param {boolean} enabled - Dark mode enabled\n   */\n  function setDarkMode(enabled) {\n    darkMode.value = enabled\n    \n    if (enabled) {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n    \n    localStorage.setItem('darkMode', enabled.toString())\n    \n    window.dispatchEvent(new CustomEvent('darkModeChanged', {\n      detail: { darkMode: enabled }\n    }))\n  }\n  \n  /**\n   * Update screen size detection\n   */\n  function updateScreenSize() {\n    const width = window.innerWidth\n    isMobile.value = width < 768\n    isTablet.value = width >= 768 && width < 1024\n  }\n  \n  /**\n   * Initialize app store\n   */\n  function initialize() {\n    // Load saved preferences\n    const savedSidebarOpen = localStorage.getItem('sidebarOpen')\n    if (savedSidebarOpen !== null) {\n      sidebarOpen.value = savedSidebarOpen === 'true'\n    }\n    \n    const savedSidebarCollapsed = localStorage.getItem('sidebarCollapsed')\n    if (savedSidebarCollapsed !== null) {\n      sidebarCollapsed.value = savedSidebarCollapsed === 'true'\n    }\n    \n    const savedDarkMode = localStorage.getItem('darkMode')\n    if (savedDarkMode !== null) {\n      setDarkMode(savedDarkMode === 'true')\n    }\n    \n    // Set up screen size detection\n    updateScreenSize()\n    window.addEventListener('resize', updateScreenSize)\n    \n    // Set up online/offline detection\n    window.addEventListener('online', () => {\n      isOnline.value = true\n      showNotification('Connessione ripristinata', 'success', 3000)\n    })\n    \n    window.addEventListener('offline', () => {\n      isOnline.value = false\n      showNotification('Connessione persa', 'warning', 0)\n    })\n    \n    // Auto-collapse sidebar on mobile\n    if (isMobile.value) {\n      sidebarOpen.value = false\n    }\n  }\n  \n  /**\n   * Handle API errors globally\n   * @param {Error} error - Error object\n   * @param {string} context - Error context\n   */\n  function handleApiError(error, context = '') {\n    console.error('API Error:', error, context)\n    \n    let message = 'Si è verificato un errore'\n    \n    if (error.response) {\n      // Server responded with error status\n      const status = error.response.status\n      \n      switch (status) {\n        case 401:\n          message = 'Sessione scaduta. Effettua nuovamente il login.'\n          // Redirect to login after a delay\n          setTimeout(() => {\n            window.location.href = '/auth/login'\n          }, 2000)\n          break\n        case 403:\n          message = 'Non hai i permessi per eseguire questa operazione'\n          break\n        case 404:\n          message = 'Risorsa non trovata'\n          break\n        case 422:\n          message = 'Dati non validi'\n          break\n        case 500:\n          message = 'Errore interno del server'\n          break\n        default:\n          message = `Errore ${status}: ${error.response.statusText}`\n      }\n    } else if (error.request) {\n      // Network error\n      message = 'Errore di connessione. Verifica la tua connessione internet.'\n    }\n    \n    showNotification(message, 'error', 8000)\n  }\n  \n  // Initialize store\n  initialize()\n  \n  return {\n    // State\n    isLoading,\n    loadingMessage,\n    isInitialized,\n    sidebarOpen,\n    sidebarCollapsed,\n    notifications,\n    activeModals,\n    currentPage,\n    pageTitle,\n    breadcrumbs,\n    darkMode,\n    isMobile,\n    isTablet,\n    isOnline,\n    \n    // Computed\n    hasNotifications,\n    unreadNotifications,\n    hasActiveModals,\n    isDesktop,\n    \n    // Actions\n    setLoading,\n    setInitialized,\n    toggleSidebar,\n    setSidebarOpen,\n    toggleSidebarCollapsed,\n    showNotification,\n    hideNotification,\n    markNotificationRead,\n    clearNotifications,\n    showModal,\n    hideModal,\n    hideAllModals,\n    setCurrentPage,\n    toggleDarkMode,\n    setDarkMode,\n    updateScreenSize,\n    initialize,\n    handleApiError\n  }\n})\n"}