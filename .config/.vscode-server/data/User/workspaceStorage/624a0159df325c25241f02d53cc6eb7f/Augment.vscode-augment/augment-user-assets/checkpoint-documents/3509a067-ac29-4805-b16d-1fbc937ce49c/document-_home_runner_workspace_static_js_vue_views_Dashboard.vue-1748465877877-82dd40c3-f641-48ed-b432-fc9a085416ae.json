{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/Dashboard.vue"}, "originalCode": "<template>\n  <div class=\"py-6 animate-brand-fade-in\">\n    <!-- Dashboard Header -->\n    <div class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-brand-text-primary\">Dashboard</h1>\n        <p class=\"mt-1 text-sm text-brand-text-secondary\">\n          Benvenuto, {{ authStore.userFullName }}! Ecco una panoramica delle attività della tua azienda.\n        </p>\n      </div>\n      <div class=\"mt-4 md:mt-0 flex space-x-3\">\n        <select \n          v-model=\"selectedPeriod\"\n          @change=\"loadDashboardData\"\n          class=\"input-brand\"\n        >\n          <option value=\"7\">Ultimi 7 giorni</option>\n          <option value=\"30\">Ultimo mese</option>\n          <option value=\"90\">Ultimi 3 mesi</option>\n        </select>\n        <button \n          @click=\"refreshData\"\n          :disabled=\"isLoading\"\n          class=\"btn-brand-primary\"\n        >\n          <i \n            :class=\"['fas fa-sync-alt mr-2', { 'animate-spin': isLoading }]\"\n          ></i>\n          Aggiorna\n        </button>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <StatsCard\n        v-for=\"stat in stats\"\n        :key=\"stat.id\"\n        :title=\"stat.title\"\n        :value=\"stat.value\"\n        :icon=\"stat.icon\"\n        :color=\"stat.color\"\n        :trend=\"stat.trend\"\n        :link=\"stat.link\"\n        class=\"animate-brand-slide-up\"\n        :style=\"{ animationDelay: `${stat.id * 100}ms` }\"\n      />\n    </div>\n\n    <!-- Charts and Activity Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- Project Status Chart -->\n      <ChartCard\n        title=\"Stato Progetti\"\n        :loading=\"chartsLoading\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 400ms\"\n      >\n        <template #actions>\n          <select \n            v-model=\"projectChartFilter\"\n            @change=\"loadProjectChart\"\n            class=\"text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300\"\n          >\n            <option value=\"all\">Tutti</option>\n            <option value=\"quarter\">Ultimo trimestre</option>\n            <option value=\"year\">Anno corrente</option>\n          </select>\n        </template>\n        \n        <DoughnutChart\n          v-if=\"projectChartData\"\n          :data=\"projectChartData\"\n          :options=\"chartOptions.doughnut\"\n        />\n      </ChartCard>\n\n      <!-- Task Status Chart -->\n      <ChartCard\n        title=\"Stato Attività\"\n        :loading=\"chartsLoading\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 500ms\"\n      >\n        <template #actions>\n          <select \n            v-model=\"taskChartFilter\"\n            @change=\"loadTaskChart\"\n            class=\"text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300\"\n          >\n            <option value=\"all\">Tutte</option>\n            <option value=\"active\">Progetti attivi</option>\n            <option value=\"mine\">Le mie attività</option>\n          </select>\n        </template>\n        \n        <BarChart\n          v-if=\"taskChartData\"\n          :data=\"taskChartData\"\n          :options=\"chartOptions.bar\"\n        />\n      </ChartCard>\n    </div>\n\n    <!-- Tasks, News and Calendar Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n      <!-- Upcoming Tasks -->\n      <ActivityCard\n        title=\"Attività in Scadenza\"\n        icon=\"fas fa-tasks\"\n        :items=\"upcomingTasks\"\n        :loading=\"tasksLoading\"\n        link=\"/projects\"\n        linkText=\"Vedi tutti i progetti\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 600ms\"\n      >\n        <template #item=\"{ item }\">\n          <TaskItem :task=\"item\" />\n        </template>\n      </ActivityCard>\n\n      <!-- Latest News -->\n      <ActivityCard\n        title=\"Ultime Notizie\"\n        icon=\"fas fa-newspaper\"\n        :items=\"latestNews\"\n        :loading=\"newsLoading\"\n        link=\"/communications/news\"\n        linkText=\"Vedi tutte le notizie\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 700ms\"\n      >\n        <template #item=\"{ item }\">\n          <NewsItem :news=\"item\" />\n        </template>\n      </ActivityCard>\n\n      <!-- Upcoming Events -->\n      <ActivityCard\n        title=\"Eventi Imminenti\"\n        icon=\"fas fa-calendar\"\n        :items=\"upcomingEvents\"\n        :loading=\"eventsLoading\"\n        link=\"#\"\n        linkText=\"Visualizza calendario\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 800ms\"\n      >\n        <template #item=\"{ item }\">\n          <EventItem :event=\"item\" />\n        </template>\n      </ActivityCard>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue'\nimport { useAuthStore } from '../stores/auth.js'\nimport { useAppStore } from '../stores/app.js'\n\n// Components\nimport StatsCard from '../components/dashboard/StatsCard.vue'\nimport ChartCard from '../components/dashboard/ChartCard.vue'\nimport ActivityCard from '../components/dashboard/ActivityCard.vue'\nimport TaskItem from '../components/dashboard/TaskItem.vue'\nimport NewsItem from '../components/dashboard/NewsItem.vue'\nimport EventItem from '../components/dashboard/EventItem.vue'\nimport DoughnutChart from '../components/charts/DoughnutChart.vue'\nimport BarChart from '../components/charts/BarChart.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\n\n// State\nconst isLoading = ref(false)\nconst chartsLoading = ref(false)\nconst tasksLoading = ref(false)\nconst newsLoading = ref(false)\nconst eventsLoading = ref(false)\n\nconst selectedPeriod = ref('30')\nconst projectChartFilter = ref('all')\nconst taskChartFilter = ref('all')\n\n// Data\nconst stats = ref([])\nconst projectChartData = ref(null)\nconst taskChartData = ref(null)\nconst upcomingTasks = ref([])\nconst latestNews = ref([])\nconst upcomingEvents = ref([])\n\n// Chart options\nconst chartOptions = {\n  doughnut: {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right',\n        labels: {\n          boxWidth: 15,\n          padding: 15,\n          font: { size: 12 }\n        }\n      }\n    },\n    cutout: '70%'\n  },\n  bar: {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: { display: false }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: { color: 'rgba(156, 163, 175, 0.1)' },\n        ticks: { precision: 0 }\n      },\n      x: {\n        grid: { display: false }\n      }\n    }\n  }\n}\n\n// Methods\nasync function loadDashboardData() {\n  isLoading.value = true\n  \n  try {\n    // Load stats\n    await loadStats()\n    \n    // Load charts\n    await Promise.all([\n      loadProjectChart(),\n      loadTaskChart()\n    ])\n    \n    // Load activities\n    await Promise.all([\n      loadUpcomingTasks(),\n      loadLatestNews(),\n      loadUpcomingEvents()\n    ])\n    \n  } catch (error) {\n    console.error('Failed to load dashboard data:', error)\n    appStore.handleApiError(error, 'Dashboard')\n  } finally {\n    isLoading.value = false\n  }\n}\n\nasync function loadStats() {\n  try {\n    const response = await fetch(`/api/dashboard/stats?period=${selectedPeriod.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      stats.value = data.data.stats\n    }\n  } catch (error) {\n    console.error('Failed to load stats:', error)\n  }\n}\n\nasync function loadProjectChart() {\n  chartsLoading.value = true\n  try {\n    const response = await fetch(`/api/dashboard/charts/projects?filter=${projectChartFilter.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      projectChartData.value = data.data\n    }\n  } catch (error) {\n    console.error('Failed to load project chart:', error)\n  } finally {\n    chartsLoading.value = false\n  }\n}\n\nasync function loadTaskChart() {\n  chartsLoading.value = true\n  try {\n    const response = await fetch(`/api/dashboard/charts/tasks?filter=${taskChartFilter.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      taskChartData.value = data.data\n    }\n  } catch (error) {\n    console.error('Failed to load task chart:', error)\n  } finally {\n    chartsLoading.value = false\n  }\n}\n\nasync function loadUpcomingTasks() {\n  tasksLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/tasks/upcoming?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      upcomingTasks.value = data.data.tasks\n    }\n  } catch (error) {\n    console.error('Failed to load upcoming tasks:', error)\n  } finally {\n    tasksLoading.value = false\n  }\n}\n\nasync function loadLatestNews() {\n  newsLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/news?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      latestNews.value = data.data.news\n    }\n  } catch (error) {\n    console.error('Failed to load latest news:', error)\n  } finally {\n    newsLoading.value = false\n  }\n}\n\nasync function loadUpcomingEvents() {\n  eventsLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/events/upcoming?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      upcomingEvents.value = data.data.events\n    }\n  } catch (error) {\n    console.error('Failed to load upcoming events:', error)\n  } finally {\n    eventsLoading.value = false\n  }\n}\n\nasync function refreshData() {\n  await loadDashboardData()\n  appStore.showNotification('Dashboard aggiornata', 'success', 2000)\n}\n\n// Lifecycle\nonMounted(() => {\n  // Set page info\n  appStore.setCurrentPage('dashboard', 'Dashboard', [\n    { name: 'Dashboard', path: '/' }\n  ])\n  \n  // Load initial data\n  loadDashboardData()\n})\n</script>\n\n<style scoped>\n/* Animation delays for staggered entrance */\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-brand-slide-up {\n  animation: slideUp 0.6s ease-out forwards;\n  opacity: 0;\n}\n\n/* Responsive grid adjustments */\n@media (max-width: 640px) {\n  .grid-cols-1.sm\\\\:grid-cols-2.lg\\\\:grid-cols-4 {\n    gap: 1rem;\n  }\n}\n\n/* Loading states */\n.loading-shimmer {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n.dark .loading-shimmer {\n  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);\n  background-size: 200% 100%;\n}\n\n/* Chart container */\n.chart-container {\n  position: relative;\n  height: 300px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .animate-brand-slide-up,\n  .animate-brand-fade-in {\n    animation: none;\n    opacity: 1;\n    transform: none;\n  }\n  \n  .loading-shimmer {\n    animation: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"py-6 animate-brand-fade-in\">\n    <!-- Dashboard Header -->\n    <div class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-brand-text-primary\">Dashboard</h1>\n        <p class=\"mt-1 text-sm text-brand-text-secondary\">\n          Benvenuto, {{ authStore.userFullName }}! Ecco una panoramica delle attività della tua azienda.\n        </p>\n      </div>\n      <div class=\"mt-4 md:mt-0 flex space-x-3\">\n        <select \n          v-model=\"selectedPeriod\"\n          @change=\"loadDashboardData\"\n          class=\"input-brand\"\n        >\n          <option value=\"7\">Ultimi 7 giorni</option>\n          <option value=\"30\">Ultimo mese</option>\n          <option value=\"90\">Ultimi 3 mesi</option>\n        </select>\n        <button \n          @click=\"refreshData\"\n          :disabled=\"isLoading\"\n          class=\"btn-brand-primary\"\n        >\n          <i \n            :class=\"['fas fa-sync-alt mr-2', { 'animate-spin': isLoading }]\"\n          ></i>\n          Aggiorna\n        </button>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <StatsCard\n        v-for=\"stat in stats\"\n        :key=\"stat.id\"\n        :title=\"stat.title\"\n        :value=\"stat.value\"\n        :icon=\"stat.icon\"\n        :color=\"stat.color\"\n        :trend=\"stat.trend\"\n        :link=\"stat.link\"\n        class=\"animate-brand-slide-up\"\n        :style=\"{ animationDelay: `${stat.id * 100}ms` }\"\n      />\n    </div>\n\n    <!-- Charts and Activity Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- Project Status Chart -->\n      <ChartCard\n        title=\"Stato Progetti\"\n        :loading=\"chartsLoading\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 400ms\"\n      >\n        <template #actions>\n          <select \n            v-model=\"projectChartFilter\"\n            @change=\"loadProjectChart\"\n            class=\"text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300\"\n          >\n            <option value=\"all\">Tutti</option>\n            <option value=\"quarter\">Ultimo trimestre</option>\n            <option value=\"year\">Anno corrente</option>\n          </select>\n        </template>\n        \n        <DoughnutChart\n          v-if=\"projectChartData\"\n          :data=\"projectChartData\"\n          :options=\"chartOptions.doughnut\"\n        />\n      </ChartCard>\n\n      <!-- Task Status Chart -->\n      <ChartCard\n        title=\"Stato Attività\"\n        :loading=\"chartsLoading\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 500ms\"\n      >\n        <template #actions>\n          <select \n            v-model=\"taskChartFilter\"\n            @change=\"loadTaskChart\"\n            class=\"text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:text-gray-300\"\n          >\n            <option value=\"all\">Tutte</option>\n            <option value=\"active\">Progetti attivi</option>\n            <option value=\"mine\">Le mie attività</option>\n          </select>\n        </template>\n        \n        <BarChart\n          v-if=\"taskChartData\"\n          :data=\"taskChartData\"\n          :options=\"chartOptions.bar\"\n        />\n      </ChartCard>\n    </div>\n\n    <!-- Tasks, News and Calendar Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n      <!-- Upcoming Tasks -->\n      <ActivityCard\n        title=\"Attività in Scadenza\"\n        icon=\"fas fa-tasks\"\n        :items=\"upcomingTasks\"\n        :loading=\"tasksLoading\"\n        link=\"/projects\"\n        linkText=\"Vedi tutti i progetti\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 600ms\"\n      >\n        <template #item=\"{ item }\">\n          <TaskItem :task=\"item\" />\n        </template>\n      </ActivityCard>\n\n      <!-- Latest News -->\n      <ActivityCard\n        title=\"Ultime Notizie\"\n        icon=\"fas fa-newspaper\"\n        :items=\"latestNews\"\n        :loading=\"newsLoading\"\n        link=\"/communications/news\"\n        linkText=\"Vedi tutte le notizie\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 700ms\"\n      >\n        <template #item=\"{ item }\">\n          <NewsItem :news=\"item\" />\n        </template>\n      </ActivityCard>\n\n      <!-- Upcoming Events -->\n      <ActivityCard\n        title=\"Eventi Imminenti\"\n        icon=\"fas fa-calendar\"\n        :items=\"upcomingEvents\"\n        :loading=\"eventsLoading\"\n        link=\"#\"\n        linkText=\"Visualizza calendario\"\n        class=\"animate-brand-slide-up\"\n        style=\"animation-delay: 800ms\"\n      >\n        <template #item=\"{ item }\">\n          <EventItem :event=\"item\" />\n        </template>\n      </ActivityCard>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue'\nimport { useAuthStore } from '../stores/auth.js'\nimport { useAppStore } from '../stores/app.js'\n\n// Components\nimport StatsCard from '../components/dashboard/StatsCard.vue'\nimport ChartCard from '../components/dashboard/ChartCard.vue'\nimport ActivityCard from '../components/dashboard/ActivityCard.vue'\nimport TaskItem from '../components/dashboard/TaskItem.vue'\nimport NewsItem from '../components/dashboard/NewsItem.vue'\nimport EventItem from '../components/dashboard/EventItem.vue'\nimport DoughnutChart from '../components/charts/DoughnutChart.vue'\nimport BarChart from '../components/charts/BarChart.vue'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\n\n// State\nconst isLoading = ref(false)\nconst chartsLoading = ref(false)\nconst tasksLoading = ref(false)\nconst newsLoading = ref(false)\nconst eventsLoading = ref(false)\n\nconst selectedPeriod = ref('30')\nconst projectChartFilter = ref('all')\nconst taskChartFilter = ref('all')\n\n// Data\nconst stats = ref([])\nconst projectChartData = ref(null)\nconst taskChartData = ref(null)\nconst upcomingTasks = ref([])\nconst latestNews = ref([])\nconst upcomingEvents = ref([])\n\n// Chart options\nconst chartOptions = {\n  doughnut: {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'right',\n        labels: {\n          boxWidth: 15,\n          padding: 15,\n          font: { size: 12 }\n        }\n      }\n    },\n    cutout: '70%'\n  },\n  bar: {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: { display: false }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        grid: { color: 'rgba(156, 163, 175, 0.1)' },\n        ticks: { precision: 0 }\n      },\n      x: {\n        grid: { display: false }\n      }\n    }\n  }\n}\n\n// Methods\nasync function loadDashboardData() {\n  isLoading.value = true\n  \n  try {\n    // Load stats\n    await loadStats()\n    \n    // Load charts\n    await Promise.all([\n      loadProjectChart(),\n      loadTaskChart()\n    ])\n    \n    // Load activities\n    await Promise.all([\n      loadUpcomingTasks(),\n      loadLatestNews(),\n      loadUpcomingEvents()\n    ])\n    \n  } catch (error) {\n    console.error('Failed to load dashboard data:', error)\n    appStore.handleApiError(error, 'Dashboard')\n  } finally {\n    isLoading.value = false\n  }\n}\n\nasync function loadStats() {\n  try {\n    const response = await fetch(`/api/dashboard/stats?period=${selectedPeriod.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      stats.value = data.data.stats\n    }\n  } catch (error) {\n    console.error('Failed to load stats:', error)\n  }\n}\n\nasync function loadProjectChart() {\n  chartsLoading.value = true\n  try {\n    const response = await fetch(`/api/dashboard/charts/projects?filter=${projectChartFilter.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      projectChartData.value = data.data\n    }\n  } catch (error) {\n    console.error('Failed to load project chart:', error)\n  } finally {\n    chartsLoading.value = false\n  }\n}\n\nasync function loadTaskChart() {\n  chartsLoading.value = true\n  try {\n    const response = await fetch(`/api/dashboard/charts/tasks?filter=${taskChartFilter.value}`)\n    if (response.ok) {\n      const data = await response.json()\n      taskChartData.value = data.data\n    }\n  } catch (error) {\n    console.error('Failed to load task chart:', error)\n  } finally {\n    chartsLoading.value = false\n  }\n}\n\nasync function loadUpcomingTasks() {\n  tasksLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/tasks/upcoming?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      upcomingTasks.value = data.data.tasks\n    }\n  } catch (error) {\n    console.error('Failed to load upcoming tasks:', error)\n  } finally {\n    tasksLoading.value = false\n  }\n}\n\nasync function loadLatestNews() {\n  newsLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/news?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      latestNews.value = data.data.news\n    }\n  } catch (error) {\n    console.error('Failed to load latest news:', error)\n  } finally {\n    newsLoading.value = false\n  }\n}\n\nasync function loadUpcomingEvents() {\n  eventsLoading.value = true\n  try {\n    const response = await fetch('/api/dashboard/events/upcoming?limit=5')\n    if (response.ok) {\n      const data = await response.json()\n      upcomingEvents.value = data.data.events\n    }\n  } catch (error) {\n    console.error('Failed to load upcoming events:', error)\n  } finally {\n    eventsLoading.value = false\n  }\n}\n\nasync function refreshData() {\n  await loadDashboardData()\n  appStore.showNotification('Dashboard aggiornata', 'success', 2000)\n}\n\n// Lifecycle\nonMounted(() => {\n  // Set page info\n  appStore.setCurrentPage('dashboard', 'Dashboard', [\n    { name: 'Dashboard', path: '/' }\n  ])\n  \n  // Load initial data\n  loadDashboardData()\n})\n</script>\n\n<style scoped>\n/* Animation delays for staggered entrance */\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.animate-brand-slide-up {\n  animation: slideUp 0.6s ease-out forwards;\n  opacity: 0;\n}\n\n/* Responsive grid adjustments */\n@media (max-width: 640px) {\n  .grid-cols-1.sm\\\\:grid-cols-2.lg\\\\:grid-cols-4 {\n    gap: 1rem;\n  }\n}\n\n/* Loading states */\n.loading-shimmer {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n.dark .loading-shimmer {\n  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);\n  background-size: 200% 100%;\n}\n\n/* Chart container */\n.chart-container {\n  position: relative;\n  height: 300px;\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .animate-brand-slide-up,\n  .animate-brand-fade-in {\n    animation: none;\n    opacity: 1;\n    transform: none;\n  }\n  \n  .loading-shimmer {\n    animation: none;\n  }\n}\n</style>\n"}