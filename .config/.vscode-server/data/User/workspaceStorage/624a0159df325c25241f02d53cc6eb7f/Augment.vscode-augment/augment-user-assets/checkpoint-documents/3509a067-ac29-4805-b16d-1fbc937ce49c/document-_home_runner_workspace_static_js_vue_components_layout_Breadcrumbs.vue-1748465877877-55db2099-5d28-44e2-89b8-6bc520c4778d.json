{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/Breadcrumbs.vue"}, "originalCode": "<template>\n  <nav class=\"flex mb-6\" aria-label=\"Breadcrumb\">\n    <ol class=\"inline-flex items-center space-x-1 md:space-x-3\">\n      <li \n        v-for=\"(item, index) in items\" \n        :key=\"index\"\n        class=\"inline-flex items-center\"\n      >\n        <!-- Separator (except for first item) -->\n        <i \n          v-if=\"index > 0\"\n          class=\"fas fa-chevron-right text-gray-400 mx-2 h-3 w-3\"\n        ></i>\n        \n        <!-- Breadcrumb Item -->\n        <router-link\n          v-if=\"item.path && index < items.length - 1\"\n          :to=\"item.path\"\n          class=\"inline-flex items-center text-sm font-medium text-brand-text-secondary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n        >\n          <i \n            v-if=\"index === 0 && item.icon\"\n            :class=\"[item.icon, 'mr-2 h-4 w-4']\"\n          ></i>\n          {{ item.name }}\n        </router-link>\n        \n        <!-- Current Page (no link) -->\n        <span\n          v-else\n          class=\"inline-flex items-center text-sm font-medium text-brand-text-primary\"\n          :class=\"{ 'text-brand-primary-600 dark:text-brand-primary-400': index === items.length - 1 }\"\n        >\n          <i \n            v-if=\"index === 0 && item.icon\"\n            :class=\"[item.icon, 'mr-2 h-4 w-4']\"\n          ></i>\n          {{ item.name }}\n        </span>\n      </li>\n    </ol>\n  </nav>\n</template>\n\n<script setup>\n// Props\ndefineProps({\n  items: {\n    type: Array,\n    required: true,\n    default: () => []\n  }\n})\n</script>\n\n<style scoped>\n/* Hover effects */\n.router-link:hover {\n  text-decoration: none;\n}\n\n/* Focus styles */\n.router-link:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .md\\\\:space-x-3 > * + * {\n    margin-left: 0.25rem;\n  }\n  \n  .text-sm {\n    font-size: 0.75rem;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <nav class=\"flex mb-6\" aria-label=\"Breadcrumb\">\n    <ol class=\"inline-flex items-center space-x-1 md:space-x-3\">\n      <li \n        v-for=\"(item, index) in items\" \n        :key=\"index\"\n        class=\"inline-flex items-center\"\n      >\n        <!-- Separator (except for first item) -->\n        <i \n          v-if=\"index > 0\"\n          class=\"fas fa-chevron-right text-gray-400 mx-2 h-3 w-3\"\n        ></i>\n        \n        <!-- Breadcrumb Item -->\n        <router-link\n          v-if=\"item.path && index < items.length - 1\"\n          :to=\"item.path\"\n          class=\"inline-flex items-center text-sm font-medium text-brand-text-secondary hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors\"\n        >\n          <i \n            v-if=\"index === 0 && item.icon\"\n            :class=\"[item.icon, 'mr-2 h-4 w-4']\"\n          ></i>\n          {{ item.name }}\n        </router-link>\n        \n        <!-- Current Page (no link) -->\n        <span\n          v-else\n          class=\"inline-flex items-center text-sm font-medium text-brand-text-primary\"\n          :class=\"{ 'text-brand-primary-600 dark:text-brand-primary-400': index === items.length - 1 }\"\n        >\n          <i \n            v-if=\"index === 0 && item.icon\"\n            :class=\"[item.icon, 'mr-2 h-4 w-4']\"\n          ></i>\n          {{ item.name }}\n        </span>\n      </li>\n    </ol>\n  </nav>\n</template>\n\n<script setup>\n// Props\ndefineProps({\n  items: {\n    type: Array,\n    required: true,\n    default: () => []\n  }\n})\n</script>\n\n<style scoped>\n/* Hover effects */\n.router-link:hover {\n  text-decoration: none;\n}\n\n/* Focus styles */\n.router-link:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .md\\\\:space-x-3 > * + * {\n    margin-left: 0.25rem;\n  }\n  \n  .text-sm {\n    font-size: 0.75rem;\n  }\n}\n</style>\n"}