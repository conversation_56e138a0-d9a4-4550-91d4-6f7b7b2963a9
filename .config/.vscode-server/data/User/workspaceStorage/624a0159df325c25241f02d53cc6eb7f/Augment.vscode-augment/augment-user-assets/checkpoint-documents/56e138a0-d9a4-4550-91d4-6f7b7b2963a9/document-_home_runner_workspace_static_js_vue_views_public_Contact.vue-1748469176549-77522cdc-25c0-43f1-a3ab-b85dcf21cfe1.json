{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Contact.vue"}, "originalCode": "// Contact.js - Vue.js component converted from .vue file\nimport { ref } from 'vue'\nimport PublicNavigation from '../../components/public/PublicNavigation.js'\nimport PublicFooter from '../../components/public/PublicFooter.js'\n\nexport default {\n  name: 'Contact',\n  components: {\n    PublicNavigation,\n    PublicFooter\n  },\n  template: `\n    <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n      <PublicNavigation />\n\n      <!-- Hero Section -->\n      <section class=\"bg-brand-primary-600 text-white py-16\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 class=\"text-4xl font-bold mb-4\">\n            Contattaci\n          </h1>\n          <p class=\"text-xl text-brand-primary-100\">\n            Siamo qui per aiutarti\n          </p>\n        </div>\n      </section>\n\n      <!-- Contact Form & Info -->\n      <section class=\"py-16\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            <!-- Contact Form -->\n            <div>\n              <h2 class=\"text-2xl font-bold text-brand-text-primary mb-6\">\n                Invia un messaggio\n              </h2>\n              <form @submit.prevent=\"submitForm\" class=\"space-y-6\">\n                <div>\n                  <label class=\"block text-sm font-medium text-brand-text-secondary mb-2\">\n                    Nome\n                  </label>\n                  <input\n                    v-model=\"form.name\"\n                    type=\"text\"\n                    required\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500\"\n                  >\n                </div>\n                <div>\n                  <label class=\"block text-sm font-medium text-brand-text-secondary mb-2\">\n                    Email\n                  </label>\n                  <input\n                    v-model=\"form.email\"\n                    type=\"email\"\n                    required\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500\"\n                  >\n                </div>\n                <div>\n                  <label class=\"block text-sm font-medium text-brand-text-secondary mb-2\">\n                    Messaggio\n                  </label>\n                  <textarea\n                    v-model=\"form.message\"\n                    rows=\"4\"\n                    required\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500\"\n                  ></textarea>\n                </div>\n                <button\n                  type=\"submit\"\n                  :disabled=\"isSubmitting\"\n                  class=\"w-full bg-brand-primary-600 text-white py-2 px-4 rounded-md hover:bg-brand-primary-700 transition-colors disabled:opacity-50\"\n                >\n                  {{ isSubmitting ? 'Invio...' : 'Invia messaggio' }}\n                </button>\n              </form>\n            </div>\n\n            <!-- Contact Info -->\n            <div>\n              <h2 class=\"text-2xl font-bold text-brand-text-primary mb-6\">\n                Informazioni di contatto\n              </h2>\n              <div class=\"space-y-4\">\n                <div class=\"flex items-center\">\n                  <i class=\"fas fa-map-marker-alt text-brand-primary-500 mr-3\"></i>\n                  <span>Via Roma 123, 00100 Roma</span>\n                </div>\n                <div class=\"flex items-center\">\n                  <i class=\"fas fa-phone text-brand-primary-500 mr-3\"></i>\n                  <span>+39 06 1234567</span>\n                </div>\n                <div class=\"flex items-center\">\n                  <i class=\"fas fa-envelope text-brand-primary-500 mr-3\"></i>\n                  <span><EMAIL></span>\n                </div>\n                <div class=\"flex items-center\">\n                  <i class=\"fas fa-clock text-brand-primary-500 mr-3\"></i>\n                  <span>Lun-Ven 9:00-18:00</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <PublicFooter />\n    </div>\n  `,\n  setup() {\n    const form = ref({\n      name: '',\n      email: '',\n      message: ''\n    })\n\n    const isSubmitting = ref(false)\n\n    async function submitForm() {\n      isSubmitting.value = true\n      try {\n        // Simulate form submission\n        await new Promise(resolve => setTimeout(resolve, 1000))\n        alert('Messaggio inviato!')\n        form.value = { name: '', email: '', message: '' }\n      } catch (error) {\n        alert('Errore nell\\'invio')\n      } finally {\n        isSubmitting.value = false\n      }\n    }\n\n    return {\n      form,\n      isSubmitting,\n      submitForm\n    }\n  }\n}\n"}