{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/tenant.js"}, "originalCode": "/**\n * Tenant Store - Pinia Store per configurazione tenant\n * Gestisce tutti i contenuti personalizzabili per il tenant\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useTenantStore = defineStore('tenant', () => {\n  // === STATE ===\n  const config = ref({})\n  const isLoaded = ref(false)\n  \n  // === COMPUTED ===\n  \n  // Company info\n  const company = computed(() => config.value.company || {})\n  const contact = computed(() => config.value.contact || {})\n  const pages = computed(() => config.value.pages || {})\n  const routes = computed(() => config.value.routes || {})\n  const navigation = computed(() => config.value.navigation || {})\n  const footer = computed(() => config.value.footer || {})\n  \n  // Helper per interpolazione variabili\n  const interpolate = (text, extraVars = {}) => {\n    if (!text) return ''\n    \n    const vars = {\n      'company.name': company.value.name || 'DatVinci',\n      'company.tagline': company.value.tagline || 'Innovazione per il futuro',\n      'company.description': company.value.description || '',\n      'company.mission': company.value.mission || '',\n      'company.vision': company.value.vision || '',\n      'company.founded': company.value.founded || '2018',\n      'company.team_size': company.value.team_size || '25+ professionisti',\n      'contact.email': contact.value.email || '<EMAIL>',\n      'contact.phone': contact.value.phone || '+39 02 1234567',\n      'contact.address': contact.value.address || '',\n      'current_year': new Date().getFullYear(),\n      ...extraVars\n    }\n    \n    let result = text\n    Object.entries(vars).forEach(([key, value]) => {\n      const regex = new RegExp(`\\\\{${key}\\\\}`, 'g')\n      result = result.replace(regex, value)\n    })\n    \n    return result\n  }\n  \n  // Route titles con interpolazione\n  const getRouteTitle = (routeName) => {\n    const routeConfig = routes.value[routeName]\n    if (!routeConfig?.title) return company.value.name || 'DatVinci'\n    \n    return interpolate(routeConfig.title)\n  }\n  \n  const getRouteMetaDescription = (routeName) => {\n    const routeConfig = routes.value[routeName]\n    if (!routeConfig?.meta_description) return company.value.description || ''\n    \n    return interpolate(routeConfig.meta_description)\n  }\n  \n  // Page content con interpolazione\n  const getPageContent = (pageName, sectionName = null, key = null) => {\n    const pageConfig = pages.value[pageName]\n    if (!pageConfig) return ''\n    \n    let content = pageConfig\n    if (sectionName) {\n      content = content[sectionName]\n      if (!content) return ''\n    }\n    \n    if (key) {\n      content = content[key]\n      if (!content) return ''\n    }\n    \n    if (typeof content === 'string') {\n      return interpolate(content)\n    }\n    \n    return content\n  }\n  \n  // Navigation labels\n  const getNavigationLabel = (key) => {\n    return navigation.value[key] || key\n  }\n  \n  // Footer content\n  const getFooterContent = (key) => {\n    const content = footer.value[key]\n    if (!content) return ''\n    \n    return interpolate(content)\n  }\n  \n  // === ACTIONS ===\n  \n  /**\n   * Carica la configurazione del tenant\n   */\n  async function loadConfig() {\n    try {\n      const response = await fetch('/api/public/config')\n      if (response.ok) {\n        const data = await response.json()\n        config.value = data.data\n        isLoaded.value = true\n        \n        console.log('✅ Tenant configuration loaded')\n        return true\n      } else {\n        throw new Error('Failed to load tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to load tenant configuration:', error)\n      \n      // Fallback configuration\n      config.value = {\n        company: {\n          name: 'DatVinci',\n          tagline: 'Innovazione per il futuro',\n          description: 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.'\n        },\n        contact: {\n          email: '<EMAIL>',\n          phone: '+39 02 1234567',\n          address: 'Via dell\\'Innovazione 123, Milano'\n        },\n        routes: {},\n        pages: {},\n        navigation: {},\n        footer: {}\n      }\n      isLoaded.value = true\n      return false\n    }\n  }\n  \n  /**\n   * Aggiorna la configurazione del tenant (solo admin)\n   */\n  async function updateConfig(newConfig) {\n    try {\n      const response = await fetch('/api/admin/tenant/config', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        body: JSON.stringify(newConfig)\n      })\n      \n      if (response.ok) {\n        const data = await response.json()\n        config.value = data.data\n        \n        console.log('✅ Tenant configuration updated')\n        return true\n      } else {\n        throw new Error('Failed to update tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to update tenant configuration:', error)\n      throw error\n    }\n  }\n  \n  /**\n   * Reset alla configurazione di default\n   */\n  async function resetConfig() {\n    try {\n      const response = await fetch('/api/admin/tenant/config/reset', {\n        method: 'POST',\n        headers: {\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        }\n      })\n      \n      if (response.ok) {\n        await loadConfig()\n        console.log('✅ Tenant configuration reset to default')\n        return true\n      } else {\n        throw new Error('Failed to reset tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to reset tenant configuration:', error)\n      throw error\n    }\n  }\n  \n  return {\n    // State\n    config,\n    isLoaded,\n    \n    // Computed\n    company,\n    contact,\n    pages,\n    routes,\n    navigation,\n    footer,\n    \n    // Methods\n    interpolate,\n    getRouteTitle,\n    getRouteMetaDescription,\n    getPageContent,\n    getNavigationLabel,\n    getFooterContent,\n    loadConfig,\n    updateConfig,\n    resetConfig\n  }\n})\n", "modifiedCode": "/**\n * Tenant Store - Pinia Store per configurazione tenant\n * Gestisce tutti i contenuti personalizzabili per il tenant\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useTenantStore = defineStore('tenant', () => {\n  // === STATE ===\n  const config = ref({})\n  const isLoaded = ref(false)\n  \n  // === COMPUTED ===\n  \n  // Company info\n  const company = computed(() => config.value.company || {})\n  const contact = computed(() => config.value.contact || {})\n  const pages = computed(() => config.value.pages || {})\n  const routes = computed(() => config.value.routes || {})\n  const navigation = computed(() => config.value.navigation || {})\n  const footer = computed(() => config.value.footer || {})\n  \n  // Helper per interpolazione variabili\n  const interpolate = (text, extraVars = {}) => {\n    if (!text) return ''\n    \n    const vars = {\n      'company.name': company.value.name || 'DatVinci',\n      'company.tagline': company.value.tagline || 'Innovazione per il futuro',\n      'company.description': company.value.description || '',\n      'company.mission': company.value.mission || '',\n      'company.vision': company.value.vision || '',\n      'company.founded': company.value.founded || '2018',\n      'company.team_size': company.value.team_size || '25+ professionisti',\n      'contact.email': contact.value.email || '<EMAIL>',\n      'contact.phone': contact.value.phone || '+39 02 1234567',\n      'contact.address': contact.value.address || '',\n      'current_year': new Date().getFullYear(),\n      ...extraVars\n    }\n    \n    let result = text\n    Object.entries(vars).forEach(([key, value]) => {\n      const regex = new RegExp(`\\\\{${key}\\\\}`, 'g')\n      result = result.replace(regex, value)\n    })\n    \n    return result\n  }\n  \n  // Route titles con interpolazione\n  const getRouteTitle = (routeName) => {\n    const routeConfig = routes.value[routeName]\n    if (!routeConfig?.title) return company.value.name || 'DatVinci'\n    \n    return interpolate(routeConfig.title)\n  }\n  \n  const getRouteMetaDescription = (routeName) => {\n    const routeConfig = routes.value[routeName]\n    if (!routeConfig?.meta_description) return company.value.description || ''\n    \n    return interpolate(routeConfig.meta_description)\n  }\n  \n  // Page content con interpolazione\n  const getPageContent = (pageName, sectionName = null, key = null) => {\n    const pageConfig = pages.value[pageName]\n    if (!pageConfig) return ''\n    \n    let content = pageConfig\n    if (sectionName) {\n      content = content[sectionName]\n      if (!content) return ''\n    }\n    \n    if (key) {\n      content = content[key]\n      if (!content) return ''\n    }\n    \n    if (typeof content === 'string') {\n      return interpolate(content)\n    }\n    \n    return content\n  }\n  \n  // Navigation labels\n  const getNavigationLabel = (key) => {\n    return navigation.value[key] || key\n  }\n  \n  // Footer content\n  const getFooterContent = (key) => {\n    const content = footer.value[key]\n    if (!content) return ''\n    \n    return interpolate(content)\n  }\n  \n  // === ACTIONS ===\n  \n  /**\n   * Carica la configurazione del tenant\n   */\n  async function loadConfig() {\n    try {\n      const response = await fetch('/api/public/config')\n      if (response.ok) {\n        const data = await response.json()\n        config.value = data.data\n        isLoaded.value = true\n        \n        console.log('✅ Tenant configuration loaded')\n        return true\n      } else {\n        throw new Error('Failed to load tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to load tenant configuration:', error)\n      \n      // Fallback configuration\n      config.value = {\n        company: {\n          name: 'DatVinci',\n          tagline: 'Innovazione per il futuro',\n          description: 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.'\n        },\n        contact: {\n          email: '<EMAIL>',\n          phone: '+39 02 1234567',\n          address: 'Via dell\\'Innovazione 123, Milano'\n        },\n        routes: {},\n        pages: {},\n        navigation: {},\n        footer: {}\n      }\n      isLoaded.value = true\n      return false\n    }\n  }\n  \n  /**\n   * Aggiorna la configurazione del tenant (solo admin)\n   */\n  async function updateConfig(newConfig) {\n    try {\n      const response = await fetch('/api/admin/tenant/config', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        body: JSON.stringify(newConfig)\n      })\n      \n      if (response.ok) {\n        const data = await response.json()\n        config.value = data.data\n        \n        console.log('✅ Tenant configuration updated')\n        return true\n      } else {\n        throw new Error('Failed to update tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to update tenant configuration:', error)\n      throw error\n    }\n  }\n  \n  /**\n   * Reset alla configurazione di default\n   */\n  async function resetConfig() {\n    try {\n      const response = await fetch('/api/admin/tenant/config/reset', {\n        method: 'POST',\n        headers: {\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        }\n      })\n      \n      if (response.ok) {\n        await loadConfig()\n        console.log('✅ Tenant configuration reset to default')\n        return true\n      } else {\n        throw new Error('Failed to reset tenant configuration')\n      }\n    } catch (error) {\n      console.error('Failed to reset tenant configuration:', error)\n      throw error\n    }\n  }\n  \n  return {\n    // State\n    config,\n    isLoaded,\n    \n    // Computed\n    company,\n    contact,\n    pages,\n    routes,\n    navigation,\n    footer,\n    \n    // Methods\n    interpolate,\n    getRouteTitle,\n    getRouteMetaDescription,\n    getPageContent,\n    getNavigationLabel,\n    getFooterContent,\n    loadConfig,\n    updateConfig,\n    resetConfig\n  }\n})\n"}