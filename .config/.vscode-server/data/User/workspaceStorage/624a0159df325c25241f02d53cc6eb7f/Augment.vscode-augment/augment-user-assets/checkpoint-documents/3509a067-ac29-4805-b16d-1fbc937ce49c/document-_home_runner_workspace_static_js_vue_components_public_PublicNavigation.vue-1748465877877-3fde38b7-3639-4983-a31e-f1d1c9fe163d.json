{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicNavigation.vue"}, "originalCode": "<template>\n  <nav class=\"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50\">\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <div class=\"flex justify-between h-16\">\n        <!-- Logo -->\n        <div class=\"flex items-center\">\n          <router-link to=\"/\" class=\"flex items-center\">\n            <img \n              :src=\"brandStore.currentLogo\" \n              alt=\"DatVinci\" \n              class=\"h-8 w-auto\"\n            >\n            <span class=\"ml-2 text-xl font-bold text-brand-primary-600\">\n              {{ brandStore.brandConfig.name }}\n            </span>\n          </router-link>\n        </div>\n\n        <!-- Desktop Navigation -->\n        <div class=\"hidden md:flex items-center space-x-8\">\n          <router-link\n            to=\"/\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'home' }\"\n          >\n            Home\n          </router-link>\n          <router-link\n            to=\"/services\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'services' }\"\n          >\n            Servizi\n          </router-link>\n          <router-link\n            to=\"/about\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'about' }\"\n          >\n            Chi Siamo\n          </router-link>\n          <router-link\n            to=\"/contact\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }\"\n          >\n            Contatti\n          </router-link>\n          \n          <!-- Login Button -->\n          <a\n            href=\"/auth/login\"\n            class=\"bg-brand-primary-600 text-white px-4 py-2 rounded-md hover:bg-brand-primary-700 transition-colors\"\n          >\n            Accedi\n          </a>\n        </div>\n\n        <!-- Mobile menu button -->\n        <div class=\"md:hidden flex items-center\">\n          <button\n            @click=\"mobileMenuOpen = !mobileMenuOpen\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 focus:outline-none focus:text-brand-primary-600\"\n          >\n            <i :class=\"mobileMenuOpen ? 'fas fa-times' : 'fas fa-bars'\" class=\"h-6 w-6\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Navigation -->\n    <div v-if=\"mobileMenuOpen\" class=\"md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\">\n      <div class=\"px-2 pt-2 pb-3 space-y-1\">\n        <router-link\n          to=\"/\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'home' }\"\n        >\n          Home\n        </router-link>\n        <router-link\n          to=\"/services\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'services' }\"\n        >\n          Servizi\n        </router-link>\n        <router-link\n          to=\"/about\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'about' }\"\n        >\n          Chi Siamo\n        </router-link>\n        <router-link\n          to=\"/contact\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }\"\n        >\n          Contatti\n        </router-link>\n        <a\n          href=\"/auth/login\"\n          class=\"block px-3 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700 transition-colors mx-3 mt-4 text-center\"\n        >\n          Accedi\n        </a>\n      </div>\n    </div>\n  </nav>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Store\nconst brandStore = useBrandStore()\n\n// State\nconst mobileMenuOpen = ref(false)\n</script>\n\n<style scoped>\n/* Sticky navigation */\n.sticky {\n  backdrop-filter: blur(10px);\n}\n\n/* Active link styles */\n.router-link-active {\n  color: var(--brand-primary-600);\n  font-weight: 500;\n}\n\n/* Mobile menu animation */\n.md\\\\:hidden > div {\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Focus styles */\nbutton:focus,\na:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n</style>\n", "modifiedCode": "<template>\n  <nav class=\"bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50\">\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <div class=\"flex justify-between h-16\">\n        <!-- Logo -->\n        <div class=\"flex items-center\">\n          <router-link to=\"/\" class=\"flex items-center\">\n            <img \n              :src=\"brandStore.currentLogo\" \n              alt=\"DatVinci\" \n              class=\"h-8 w-auto\"\n            >\n            <span class=\"ml-2 text-xl font-bold text-brand-primary-600\">\n              {{ brandStore.brandConfig.name }}\n            </span>\n          </router-link>\n        </div>\n\n        <!-- Desktop Navigation -->\n        <div class=\"hidden md:flex items-center space-x-8\">\n          <router-link\n            to=\"/\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'home' }\"\n          >\n            Home\n          </router-link>\n          <router-link\n            to=\"/services\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'services' }\"\n          >\n            Servizi\n          </router-link>\n          <router-link\n            to=\"/about\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'about' }\"\n          >\n            Chi Siamo\n          </router-link>\n          <router-link\n            to=\"/contact\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n            :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }\"\n          >\n            Contatti\n          </router-link>\n          \n          <!-- Login Button -->\n          <a\n            href=\"/auth/login\"\n            class=\"bg-brand-primary-600 text-white px-4 py-2 rounded-md hover:bg-brand-primary-700 transition-colors\"\n          >\n            Accedi\n          </a>\n        </div>\n\n        <!-- Mobile menu button -->\n        <div class=\"md:hidden flex items-center\">\n          <button\n            @click=\"mobileMenuOpen = !mobileMenuOpen\"\n            class=\"text-brand-text-secondary hover:text-brand-primary-600 focus:outline-none focus:text-brand-primary-600\"\n          >\n            <i :class=\"mobileMenuOpen ? 'fas fa-times' : 'fas fa-bars'\" class=\"h-6 w-6\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Navigation -->\n    <div v-if=\"mobileMenuOpen\" class=\"md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700\">\n      <div class=\"px-2 pt-2 pb-3 space-y-1\">\n        <router-link\n          to=\"/\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'home' }\"\n        >\n          Home\n        </router-link>\n        <router-link\n          to=\"/services\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'services' }\"\n        >\n          Servizi\n        </router-link>\n        <router-link\n          to=\"/about\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'about' }\"\n        >\n          Chi Siamo\n        </router-link>\n        <router-link\n          to=\"/contact\"\n          @click=\"mobileMenuOpen = false\"\n          class=\"block px-3 py-2 text-brand-text-secondary hover:text-brand-primary-600 transition-colors\"\n          :class=\"{ 'text-brand-primary-600 font-medium': $route.name === 'contact' }\"\n        >\n          Contatti\n        </router-link>\n        <a\n          href=\"/auth/login\"\n          class=\"block px-3 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700 transition-colors mx-3 mt-4 text-center\"\n        >\n          Accedi\n        </a>\n      </div>\n    </div>\n  </nav>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useBrandStore } from '../../stores/brand.js'\n\n// Store\nconst brandStore = useBrandStore()\n\n// State\nconst mobileMenuOpen = ref(false)\n</script>\n\n<style scoped>\n/* Sticky navigation */\n.sticky {\n  backdrop-filter: blur(10px);\n}\n\n/* Active link styles */\n.router-link-active {\n  color: var(--brand-primary-600);\n  font-weight: 500;\n}\n\n/* Mobile menu animation */\n.md\\\\:hidden > div {\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Focus styles */\nbutton:focus,\na:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n</style>\n"}