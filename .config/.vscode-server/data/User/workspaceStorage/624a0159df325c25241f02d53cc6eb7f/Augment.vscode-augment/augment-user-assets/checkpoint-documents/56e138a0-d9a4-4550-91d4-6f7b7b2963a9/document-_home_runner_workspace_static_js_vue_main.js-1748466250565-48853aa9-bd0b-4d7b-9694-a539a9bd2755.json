{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}, "originalCode": "/**\n * Vue.js 3 Main Application Entry Point\n * DatPortal SPA - Test Version\n */\n\nconsole.log('🚀 Loading Vue.js main.js...')\n\n// Test if Vue.js is available\ntry {\n    const { createApp } = await import('vue')\n    console.log('✅ Vue.js imported successfully')\n\n    const { createRouter, createWebHistory } = await import('vue-router')\n    console.log('✅ Vue Router imported successfully')\n\n    const { createPinia } = await import('pinia')\n    console.log('✅ Pinia imported successfully')\n\n    // Simple test component\n    const TestComponent = {\n        template: `\n            <div class=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">🎉 Vue.js Funziona!</h1>\n                    <p class=\"text-lg text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/test\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Test Route\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated }}</p>\n                    </div>\n                </div>\n            </div>\n        `,\n        data() {\n            return {\n                userInfo: window.APP_CONFIG?.user?.username || 'Non disponibile',\n                isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n            }\n        }\n    }\n\n    const TestPage = {\n        template: `\n            <div class=\"min-h-screen bg-blue-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-blue-900 mb-4\">Test Page</h1>\n                    <p class=\"text-lg text-blue-600 mb-8\">Routing funziona correttamente!</p>\n                    <router-link to=\"/\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                        Torna alla Home\n                    </router-link>\n                </div>\n            </div>\n        `\n    }\n\n    // Simple routes for testing\n    const routes = [\n        { path: '/', component: TestComponent },\n        { path: '/test', component: TestPage },\n    {\n        path: '/services',\n        component: {\n            template: `\n                <div class=\"min-h-screen bg-gray-50\">\n                    <nav class=\"bg-white shadow-sm border-b\">\n                        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                            <div class=\"flex justify-between h-16\">\n                                <div class=\"flex items-center\">\n                                    <router-link to=\"/\" class=\"text-xl font-bold text-gray-900\">DatPortal</router-link>\n                                </div>\n                                <div class=\"flex items-center space-x-4\">\n                                    <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                                    <router-link to=\"/services\" class=\"text-blue-600 font-medium\">Servizi</router-link>\n                                    <router-link to=\"/about\" class=\"text-gray-700 hover:text-blue-600\">Chi Siamo</router-link>\n                                    <router-link to=\"/contact\" class=\"text-gray-700 hover:text-blue-600\">Contatti</router-link>\n                                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                                </div>\n                            </div>\n                        </div>\n                    </nav>\n                    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n                        <h1 class=\"text-4xl font-bold text-gray-900 mb-8\">I Nostri Servizi</h1>\n                        <div class=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                            <div class=\"bg-white p-6 rounded-lg shadow-sm\">\n                                <h3 class=\"text-xl font-semibold mb-4\">Gestione Progetti</h3>\n                                <p class=\"text-gray-600\">Pianificazione, monitoraggio e controllo completo dei progetti aziendali.</p>\n                            </div>\n                            <div class=\"bg-white p-6 rounded-lg shadow-sm\">\n                                <h3 class=\"text-xl font-semibold mb-4\">Gestione Risorse</h3>\n                                <p class=\"text-gray-600\">Allocazione ottimale delle risorse umane e materiali.</p>\n                            </div>\n                            <div class=\"bg-white p-6 rounded-lg shadow-sm\">\n                                <h3 class=\"text-xl font-semibold mb-4\">Analytics & KPI</h3>\n                                <p class=\"text-gray-600\">Dashboard avanzate per il monitoraggio delle performance.</p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            `\n        }\n    },\n    {\n        path: '/about',\n        component: {\n            template: `\n                <div class=\"min-h-screen bg-gray-50\">\n                    <nav class=\"bg-white shadow-sm border-b\">\n                        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                            <div class=\"flex justify-between h-16\">\n                                <div class=\"flex items-center\">\n                                    <router-link to=\"/\" class=\"text-xl font-bold text-gray-900\">DatPortal</router-link>\n                                </div>\n                                <div class=\"flex items-center space-x-4\">\n                                    <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                                    <router-link to=\"/services\" class=\"text-gray-700 hover:text-blue-600\">Servizi</router-link>\n                                    <router-link to=\"/about\" class=\"text-blue-600 font-medium\">Chi Siamo</router-link>\n                                    <router-link to=\"/contact\" class=\"text-gray-700 hover:text-blue-600\">Contatti</router-link>\n                                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                                </div>\n                            </div>\n                        </div>\n                    </nav>\n                    <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n                        <h1 class=\"text-4xl font-bold text-gray-900 mb-8\">Chi Siamo</h1>\n                        <div class=\"prose prose-lg\">\n                            <p class=\"text-gray-600 mb-6\">\n                                DatPortal è una piattaforma innovativa per la gestione integrata di progetti, risorse e performance aziendali.\n                            </p>\n                            <p class=\"text-gray-600 mb-6\">\n                                La nostra missione è fornire alle aziende gli strumenti necessari per ottimizzare i processi,\n                                migliorare l'efficienza operativa e raggiungere i propri obiettivi strategici.\n                            </p>\n                            <h2 class=\"text-2xl font-semibold text-gray-900 mb-4\">La Nostra Visione</h2>\n                            <p class=\"text-gray-600\">\n                                Crediamo in un futuro dove la gestione aziendale sia semplice, trasparente e data-driven.\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            `\n        }\n    },\n    {\n        path: '/contact',\n        component: {\n            template: `\n                <div class=\"min-h-screen bg-gray-50\">\n                    <nav class=\"bg-white shadow-sm border-b\">\n                        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                            <div class=\"flex justify-between h-16\">\n                                <div class=\"flex items-center\">\n                                    <router-link to=\"/\" class=\"text-xl font-bold text-gray-900\">DatPortal</router-link>\n                                </div>\n                                <div class=\"flex items-center space-x-4\">\n                                    <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                                    <router-link to=\"/services\" class=\"text-gray-700 hover:text-blue-600\">Servizi</router-link>\n                                    <router-link to=\"/about\" class=\"text-gray-700 hover:text-blue-600\">Chi Siamo</router-link>\n                                    <router-link to=\"/contact\" class=\"text-blue-600 font-medium\">Contatti</router-link>\n                                    <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                                </div>\n                            </div>\n                        </div>\n                    </nav>\n                    <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n                        <h1 class=\"text-4xl font-bold text-gray-900 mb-8\">Contattaci</h1>\n                        <div class=\"grid md:grid-cols-2 gap-8\">\n                            <div>\n                                <h2 class=\"text-2xl font-semibold mb-4\">Informazioni di Contatto</h2>\n                                <div class=\"space-y-4\">\n                                    <div>\n                                        <h3 class=\"font-medium\">Email</h3>\n                                        <p class=\"text-gray-600\"><EMAIL></p>\n                                    </div>\n                                    <div>\n                                        <h3 class=\"font-medium\">Telefono</h3>\n                                        <p class=\"text-gray-600\">+39 02 1234 5678</p>\n                                    </div>\n                                    <div>\n                                        <h3 class=\"font-medium\">Indirizzo</h3>\n                                        <p class=\"text-gray-600\">Via Roma 123<br>20100 Milano, Italia</p>\n                                    </div>\n                                </div>\n                            </div>\n                            <div>\n                                <h2 class=\"text-2xl font-semibold mb-4\">Invia un Messaggio</h2>\n                                <form class=\"space-y-4\">\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700\">Nome</label>\n                                        <input type=\"text\" class=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700\">Email</label>\n                                        <input type=\"email\" class=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\">\n                                    </div>\n                                    <div>\n                                        <label class=\"block text-sm font-medium text-gray-700\">Messaggio</label>\n                                        <textarea rows=\"4\" class=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2\"></textarea>\n                                    </div>\n                                    <button type=\"submit\" class=\"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600\">\n                                        Invia Messaggio\n                                    </button>\n                                </form>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            `\n        }\n    },\n    { path: '/privacy', redirect: '/about' }\n]\n\nconst router = createRouter({\n    history: createWebHistory(),\n    routes\n})\n\n// Create Pinia store\nconst pinia = createPinia()\n\n// App Vue.js con router e Pinia\nconst vueApp = createApp({\n    template: '<router-view />'\n})\n\nvueApp.use(router)\nvueApp.use(pinia)\n\n// Mount the app\nvueApp.mount('#app')\n\n// Make it available globally for debugging\nwindow.vueApp = vueApp\n\nconsole.log('🎉 Vue.js SPA con router e Pinia caricato con successo!')\n\nexport default vueApp\n", "modifiedCode": "/**\n * Vue.js 3 Main Application Entry Point\n * DatPortal SPA - Test Version\n */\n\nconsole.log('🚀 Loading Vue.js main.js...')\n\n// Test if Vue.js is available\ntry {\n    const { createApp } = await import('vue')\n    console.log('✅ Vue.js imported successfully')\n\n    const { createRouter, createWebHistory } = await import('vue-router')\n    console.log('✅ Vue Router imported successfully')\n\n    const { createPinia } = await import('pinia')\n    console.log('✅ Pinia imported successfully')\n\n    // Simple test component\n    const TestComponent = {\n        template: `\n            <div class=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">🎉 Vue.js Funziona!</h1>\n                    <p class=\"text-lg text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/test\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Test Route\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated }}</p>\n                    </div>\n                </div>\n            </div>\n        `,\n        data() {\n            return {\n                userInfo: window.APP_CONFIG?.user?.username || 'Non disponibile',\n                isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n            }\n        }\n    }\n\n    const TestPage = {\n        template: `\n            <div class=\"min-h-screen bg-blue-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-blue-900 mb-4\">Test Page</h1>\n                    <p class=\"text-lg text-blue-600 mb-8\">Routing funziona correttamente!</p>\n                    <router-link to=\"/\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                        Torna alla Home\n                    </router-link>\n                </div>\n            </div>\n        `\n    }\n\n    // Simple routes for testing\n    const routes = [\n        { path: '/', component: TestComponent },\n        { path: '/test', component: TestPage },\n        { path: '/:pathMatch(.*)*', redirect: '/' }\n    ]\n\n    const router = createRouter({\n        history: createWebHistory(),\n        routes\n    })\n\n    // Create Pinia store\n    const pinia = createPinia()\n\n    // App Vue.js con router e Pinia\n    const vueApp = createApp({\n        template: '<router-view />'\n    })\n\n    vueApp.use(router)\n    vueApp.use(pinia)\n\n    // Mount the app\n    vueApp.mount('#app')\n\n    // Make it available globally for debugging\n    window.vueApp = vueApp\n\n    console.log('🎉 Vue.js SPA con router e Pinia caricato con successo!')\n\n} catch (error) {\n    console.error('❌ Errore durante il caricamento di Vue.js:', error)\n\n    // Fallback: show error message\n    document.getElementById('app').innerHTML = `\n        <div class=\"min-h-screen bg-red-50 flex items-center justify-center\">\n            <div class=\"text-center\">\n                <h1 class=\"text-2xl font-bold text-red-900 mb-4\">Errore Vue.js</h1>\n                <p class=\"text-red-600 mb-4\">Impossibile caricare l'applicazione Vue.js</p>\n                <p class=\"text-sm text-red-500\">${error.message}</p>\n                <a href=\"/auth/login\" class=\"mt-4 inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                    Vai al Login\n                </a>\n            </div>\n        </div>\n    `\n}\n"}