{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/UserProfile.vue"}, "originalCode": "<template>\n  <div class=\"flex-shrink-0 w-full group block\">\n    <div class=\"flex items-center\" :class=\"{ 'justify-center': collapsed }\">\n      <!-- Avatar -->\n      <div class=\"inline-block h-9 w-9 rounded-full\" :class=\"{ 'mr-3': !collapsed }\">\n        <img \n          v-if=\"authStore.user?.profile_image\"\n          class=\"h-9 w-9 rounded-full object-cover\"\n          :src=\"authStore.user.profile_image\" \n          :alt=\"authStore.userFullName\"\n        >\n        <div \n          v-else\n          class=\"h-9 w-9 rounded-full bg-brand-primary-500 dark:bg-brand-primary-600 flex items-center justify-center text-white text-sm font-medium uppercase\"\n        >\n          {{ authStore.userInitials }}\n        </div>\n      </div>\n      \n      <!-- User Info -->\n      <div :class=\"{ 'hidden': collapsed }\">\n        <p class=\"text-sm font-medium text-white\">\n          {{ authStore.userFullName }}\n        </p>\n        <div class=\"flex items-center space-x-2\">\n          <span class=\"text-xs text-brand-primary-200\">\n            {{ authStore.userRole }}\n          </span>\n          <button\n            @click=\"logout\"\n            class=\"text-xs font-medium text-brand-primary-200 hover:text-brand-primary-100 transition-colors\"\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n      \n      <!-- Tooltip for collapsed state -->\n      <div \n        v-if=\"collapsed\"\n        class=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap\"\n      >\n        {{ authStore.userFullName }}\n        <div class=\"absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full\">\n          <div class=\"border-4 border-transparent border-r-gray-900\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { useAuthStore } from '../../stores/auth.js'\n\n// Props\ndefineProps({\n  collapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Store\nconst authStore = useAuthStore()\n\n// Methods\nfunction logout() {\n  authStore.logout()\n}\n</script>\n\n<style scoped>\n/* Hover effects */\n.group:hover .text-brand-primary-200 {\n  color: rgb(var(--brand-primary-100));\n}\n\n/* Avatar animation */\n.h-9.w-9 {\n  transition: transform 0.2s ease;\n}\n\n.group:hover .h-9.w-9 {\n  transform: scale(1.05);\n}\n\n/* Tooltip positioning */\n.group {\n  position: relative;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-colors,\n  .transition-opacity,\n  .h-9.w-9,\n  .group:hover .h-9.w-9 {\n    transition: none;\n    transform: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"flex-shrink-0 w-full group block\">\n    <div class=\"flex items-center\" :class=\"{ 'justify-center': collapsed }\">\n      <!-- Avatar -->\n      <div class=\"inline-block h-9 w-9 rounded-full\" :class=\"{ 'mr-3': !collapsed }\">\n        <img \n          v-if=\"authStore.user?.profile_image\"\n          class=\"h-9 w-9 rounded-full object-cover\"\n          :src=\"authStore.user.profile_image\" \n          :alt=\"authStore.userFullName\"\n        >\n        <div \n          v-else\n          class=\"h-9 w-9 rounded-full bg-brand-primary-500 dark:bg-brand-primary-600 flex items-center justify-center text-white text-sm font-medium uppercase\"\n        >\n          {{ authStore.userInitials }}\n        </div>\n      </div>\n      \n      <!-- User Info -->\n      <div :class=\"{ 'hidden': collapsed }\">\n        <p class=\"text-sm font-medium text-white\">\n          {{ authStore.userFullName }}\n        </p>\n        <div class=\"flex items-center space-x-2\">\n          <span class=\"text-xs text-brand-primary-200\">\n            {{ authStore.userRole }}\n          </span>\n          <button\n            @click=\"logout\"\n            class=\"text-xs font-medium text-brand-primary-200 hover:text-brand-primary-100 transition-colors\"\n          >\n            Logout\n          </button>\n        </div>\n      </div>\n      \n      <!-- Tooltip for collapsed state -->\n      <div \n        v-if=\"collapsed\"\n        class=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity duration-200 z-50 whitespace-nowrap\"\n      >\n        {{ authStore.userFullName }}\n        <div class=\"absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full\">\n          <div class=\"border-4 border-transparent border-r-gray-900\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { useAuthStore } from '../../stores/auth.js'\n\n// Props\ndefineProps({\n  collapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Store\nconst authStore = useAuthStore()\n\n// Methods\nfunction logout() {\n  authStore.logout()\n}\n</script>\n\n<style scoped>\n/* Hover effects */\n.group:hover .text-brand-primary-200 {\n  color: rgb(var(--brand-primary-100));\n}\n\n/* Avatar animation */\n.h-9.w-9 {\n  transition: transform 0.2s ease;\n}\n\n.group:hover .h-9.w-9 {\n  transform: scale(1.05);\n}\n\n/* Tooltip positioning */\n.group {\n  position: relative;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-300);\n  outline-offset: 2px;\n  border-radius: var(--brand-radius-sm);\n}\n\n/* Accessibility */\n@media (prefers-reduced-motion: reduce) {\n  .transition-colors,\n  .transition-opacity,\n  .h-9.w-9,\n  .group:hover .h-9.w-9 {\n    transition: none;\n    transform: none;\n  }\n}\n</style>\n"}