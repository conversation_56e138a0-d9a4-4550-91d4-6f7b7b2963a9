{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}, "originalCode": "/**\n * Vue.js 3 Main Application Entry Point\n * DatPortal SPA - Test Version\n */\n\nconsole.log('🚀 Loading Vue.js main.js...')\n\n// Test if Vue.js is available\ntry {\n    const { createApp } = await import('vue')\n    console.log('✅ Vue.js imported successfully')\n\n    const { createRouter, createWebHistory } = await import('vue-router')\n    console.log('✅ Vue Router imported successfully')\n\n    const { createPinia } = await import('pinia')\n    console.log('✅ Pinia imported successfully')\n\n    // Simple test component\n    const TestComponent = {\n        template: `\n            <div class=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">🎉 Vue.js Funziona!</h1>\n                    <p class=\"text-lg text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/test\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Test Route\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated }}</p>\n                    </div>\n                </div>\n            </div>\n        `,\n        data() {\n            return {\n                userInfo: window.APP_CONFIG?.user?.username || 'Non disponibile',\n                isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n            }\n        }\n    }\n\n    const TestPage = {\n        template: `\n            <div class=\"min-h-screen bg-blue-50 flex items-center justify-center\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-4xl font-bold text-blue-900 mb-4\">Test Page</h1>\n                    <p class=\"text-lg text-blue-600 mb-8\">Routing funziona correttamente!</p>\n                    <router-link to=\"/\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                        Torna alla Home\n                    </router-link>\n                </div>\n            </div>\n        `\n    }\n\n    // Simple routes for testing\n    const routes = [\n        { path: '/', component: TestComponent },\n        { path: '/test', component: TestPage },\n        { path: '/:pathMatch(.*)*', redirect: '/' }\n    ]\n\n    const router = createRouter({\n        history: createWebHistory(),\n        routes\n    })\n\n    // Create Pinia store\n    const pinia = createPinia()\n\n    // App Vue.js con router e Pinia\n    const vueApp = createApp({\n        template: '<router-view />'\n    })\n\n    vueApp.use(router)\n    vueApp.use(pinia)\n\n    // Mount the app\n    vueApp.mount('#app')\n\n    // Make it available globally for debugging\n    window.vueApp = vueApp\n\n    console.log('🎉 Vue.js SPA con router e Pinia caricato con successo!')\n\n} catch (error) {\n    console.error('❌ Errore durante il caricamento di Vue.js:', error)\n\n    // Fallback: show error message\n    document.getElementById('app').innerHTML = `\n        <div class=\"min-h-screen bg-red-50 flex items-center justify-center\">\n            <div class=\"text-center\">\n                <h1 class=\"text-2xl font-bold text-red-900 mb-4\">Errore Vue.js</h1>\n                <p class=\"text-red-600 mb-4\">Impossibile caricare l'applicazione Vue.js</p>\n                <p class=\"text-sm text-red-500\">${error.message}</p>\n                <a href=\"/auth/login\" class=\"mt-4 inline-block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                    Vai al Login\n                </a>\n            </div>\n        </div>\n    `\n}\n", "modifiedCode": "// VERSIONE SEMPLICE CHE FUNZIONA SUBITO!\nconsole.log('🚀 Caricamento Vue.js...')\n\nimport { createApp } from 'vue'\nimport { createRouter, createWebHistory } from 'vue-router'\n\n// Componente HOME semplice\nconst Home = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-blue-600 font-medium\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-gray-700 hover:text-blue-600\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"flex items-center justify-center py-20\">\n                <div class=\"text-center\">\n                    <h1 class=\"text-5xl font-bold text-gray-900 mb-6\">🎉 Vue.js FUNZIONA!</h1>\n                    <p class=\"text-xl text-gray-600 mb-8\">DatPortal SPA è operativo</p>\n                    <div class=\"space-x-4\">\n                        <router-link to=\"/dashboard\" class=\"bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600\">\n                            Vai alla Dashboard\n                        </router-link>\n                        <a href=\"/auth/login\" class=\"bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600\">\n                            Login\n                        </a>\n                    </div>\n                    <div class=\"mt-8 text-sm text-gray-500\">\n                        <p>Utente: {{ userInfo }}</p>\n                        <p>Autenticato: {{ isAuthenticated ? 'SÌ' : 'NO' }}</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `,\n    data() {\n        return {\n            userInfo: window.APP_CONFIG?.user?.username || 'Anonimo',\n            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false\n        }\n    }\n}\n\n// Dashboard semplice\nconst Dashboard = {\n    template: `\n        <div class=\"min-h-screen bg-gray-50\">\n            <nav class=\"bg-white shadow border-b\">\n                <div class=\"max-w-7xl mx-auto px-4\">\n                    <div class=\"flex justify-between h-16\">\n                        <div class=\"flex items-center\">\n                            <h1 class=\"text-xl font-bold text-gray-900\">DatPortal</h1>\n                        </div>\n                        <div class=\"flex items-center space-x-4\">\n                            <router-link to=\"/\" class=\"text-gray-700 hover:text-blue-600\">Home</router-link>\n                            <router-link to=\"/dashboard\" class=\"text-blue-600 font-medium\">Dashboard</router-link>\n                            <a href=\"/auth/login\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">Login</a>\n                        </div>\n                    </div>\n                </div>\n            </nav>\n            <div class=\"max-w-7xl mx-auto px-4 py-8\">\n                <h1 class=\"text-3xl font-bold text-gray-900 mb-8\">Dashboard</h1>\n                <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Progetti</h3>\n                        <p class=\"text-3xl font-bold text-blue-600\">12</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Task</h3>\n                        <p class=\"text-3xl font-bold text-green-600\">45</p>\n                    </div>\n                    <div class=\"bg-white p-6 rounded-lg shadow\">\n                        <h3 class=\"text-lg font-semibold mb-2\">Team</h3>\n                        <p class=\"text-3xl font-bold text-purple-600\">8</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `\n}\n\n// Routes semplici\nconst routes = [\n    { path: '/', component: Home },\n    { path: '/dashboard', component: Dashboard },\n    { path: '/:pathMatch(.*)*', redirect: '/' }\n]\n\nconst router = createRouter({\n    history: createWebHistory(),\n    routes\n})\n\n// Crea app\nconst app = createApp({\n    template: '<router-view />'\n})\n\napp.use(router)\napp.mount('#app')\n\nconsole.log('✅ Vue.js caricato con successo!')\nwindow.vueApp = app\n"}