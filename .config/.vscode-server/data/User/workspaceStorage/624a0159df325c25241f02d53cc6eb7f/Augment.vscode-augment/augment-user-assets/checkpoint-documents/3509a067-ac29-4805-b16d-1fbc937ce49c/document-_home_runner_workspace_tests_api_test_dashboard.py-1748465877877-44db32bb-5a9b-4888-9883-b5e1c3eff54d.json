{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/api/test_dashboard.py"}, "originalCode": "\"\"\"\nTest suite for Dashboard API endpoints.\n\"\"\"\nimport json\nimport pytest\nfrom datetime import datetime, timedelta\n\n\nclass TestDashboardStatsAPI:\n    \"\"\"Test dashboard statistics API endpoints.\"\"\"\n\n    def test_get_dashboard_stats_success(self, client, auth, sample_users):\n        \"\"\"Test successful dashboard stats retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/stats')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        \n        stats = data['data']\n        assert 'projects' in stats\n        assert 'tasks' in stats\n        assert 'team' in stats\n        assert 'activities' in stats\n        \n        # Check projects stats structure\n        assert 'active' in stats['projects']\n        assert 'total' in stats['projects']\n        \n        # Check tasks stats structure\n        assert 'total' in stats['tasks']\n        assert 'pending' in stats['tasks']\n        assert 'completed' in stats['tasks']\n        assert 'overdue' in stats['tasks']\n\n    def test_get_dashboard_stats_unauthorized(self, client, auth):\n        \"\"\"Test dashboard stats access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/stats')\n        assert response.status_code == 401\n\n    def test_get_recent_activities_success(self, client, auth, sample_users):\n        \"\"\"Test successful recent activities retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/recent-activities')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'activities' in data['data']\n        \n        activities = data['data']['activities']\n        assert isinstance(activities, list)\n        \n        # Check activity structure if any exist\n        if activities:\n            activity = activities[0]\n            assert 'type' in activity\n            assert 'id' in activity\n            assert 'title' in activity\n            assert 'timestamp' in activity\n\n    def test_get_recent_activities_with_limit(self, client, auth, sample_users):\n        \"\"\"Test recent activities with custom limit.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/recent-activities?limit=5')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        activities = data['data']['activities']\n        assert len(activities) <= 5\n\n    def test_get_upcoming_tasks_success(self, client, auth, sample_users):\n        \"\"\"Test successful upcoming tasks retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/upcoming-tasks')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'tasks' in data['data']\n        \n        tasks = data['data']['tasks']\n        assert isinstance(tasks, list)\n        \n        # Check task structure if any exist\n        if tasks:\n            task = tasks[0]\n            assert 'id' in task\n            assert 'name' in task\n            assert 'project_name' in task\n            assert 'due_date' in task\n            assert 'days_until_due' in task\n\n    def test_get_upcoming_tasks_with_params(self, client, auth, sample_users):\n        \"\"\"Test upcoming tasks with custom parameters.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/upcoming-tasks?days=14&limit=3')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        tasks = data['data']['tasks']\n        assert len(tasks) <= 3\n\n\nclass TestDashboardKPIsAPI:\n    \"\"\"Test dashboard KPIs API endpoints.\"\"\"\n\n    def test_get_dashboard_kpis_success(self, client, auth, sample_users):\n        \"\"\"Test successful KPIs retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/kpis')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'kpis' in data['data']\n        \n        kpis = data['data']['kpis']\n        assert isinstance(kpis, list)\n        \n        # Check KPI structure if any exist\n        if kpis:\n            kpi = kpis[0]\n            assert 'id' in kpi\n            assert 'name' in kpi\n            assert 'category' in kpi\n            assert 'current_value' in kpi\n            assert 'target_value' in kpi\n            assert 'performance_percentage' in kpi\n\n    def test_get_dashboard_kpis_with_category(self, client, auth, sample_users):\n        \"\"\"Test KPIs retrieval with category filter.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/kpis?category=performance')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n\n    def test_get_dashboard_kpis_unauthorized(self, client, auth):\n        \"\"\"Test KPIs access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/kpis')\n        assert response.status_code == 401\n\n\nclass TestDashboardChartsAPI:\n    \"\"\"Test dashboard charts API endpoints.\"\"\"\n\n    def test_get_project_status_chart_success(self, client, auth, sample_users):\n        \"\"\"Test successful project status chart data retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/charts/project-status')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'chart' in data['data']\n        \n        chart = data['data']['chart']\n        assert 'labels' in chart\n        assert 'data' in chart\n        assert 'total' in chart\n        assert isinstance(chart['labels'], list)\n        assert isinstance(chart['data'], list)\n\n    def test_get_task_status_chart_success(self, client, auth, sample_users):\n        \"\"\"Test successful task status chart data retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/charts/task-status')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'chart' in data['data']\n        \n        chart = data['data']['chart']\n        assert 'labels' in chart\n        assert 'data' in chart\n        assert 'total' in chart\n\n    def test_get_charts_unauthorized(self, client, auth):\n        \"\"\"Test charts access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/charts/project-status')\n        assert response.status_code == 401\n        \n        response = client.get('/api/dashboard/charts/task-status')\n        assert response.status_code == 401\n\n\nclass TestDashboardQuickActionsAPI:\n    \"\"\"Test dashboard quick actions API endpoints.\"\"\"\n\n    def test_get_quick_actions_success(self, client, auth, sample_users):\n        \"\"\"Test successful quick actions retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/quick-actions')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'actions' in data['data']\n        \n        actions = data['data']['actions']\n        assert isinstance(actions, list)\n        assert len(actions) > 0  # Should have at least basic actions\n        \n        # Check action structure\n        action = actions[0]\n        assert 'id' in action\n        assert 'title' in action\n        assert 'description' in action\n        assert 'icon' in action\n        assert 'url' in action\n        assert 'category' in action\n\n    def test_get_quick_actions_unauthorized(self, client, auth):\n        \"\"\"Test quick actions access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/quick-actions')\n        assert response.status_code == 401\n\n\nclass TestDashboardNewsAPI:\n    \"\"\"Test dashboard news API endpoints.\"\"\"\n\n    def test_get_dashboard_news_success(self, client, auth, sample_users):\n        \"\"\"Test successful news retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/news')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'news' in data['data']\n        \n        news = data['data']['news']\n        assert isinstance(news, list)\n        \n        # Check news structure if any exist\n        if news:\n            news_item = news[0]\n            assert 'id' in news_item\n            assert 'title' in news_item\n            assert 'content' in news_item\n            assert 'author_name' in news_item\n            assert 'created_at' in news_item\n\n    def test_get_dashboard_news_with_limit(self, client, auth, sample_users):\n        \"\"\"Test news retrieval with custom limit.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/news?limit=3')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        news = data['data']['news']\n        assert len(news) <= 3\n\n    def test_get_dashboard_news_unauthorized(self, client, auth):\n        \"\"\"Test news access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/news')\n        assert response.status_code == 401\n", "modifiedCode": "\"\"\"\nTest suite for Dashboard API endpoints.\n\"\"\"\nimport json\nimport pytest\nfrom datetime import datetime, timedelta\n\n\nclass TestDashboardStatsAPI:\n    \"\"\"Test dashboard statistics API endpoints.\"\"\"\n\n    def test_get_dashboard_stats_success(self, client, auth, sample_users):\n        \"\"\"Test successful dashboard stats retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/stats')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        \n        stats = data['data']\n        assert 'projects' in stats\n        assert 'tasks' in stats\n        assert 'team' in stats\n        assert 'activities' in stats\n        \n        # Check projects stats structure\n        assert 'active' in stats['projects']\n        assert 'total' in stats['projects']\n        \n        # Check tasks stats structure\n        assert 'total' in stats['tasks']\n        assert 'pending' in stats['tasks']\n        assert 'completed' in stats['tasks']\n        assert 'overdue' in stats['tasks']\n\n    def test_get_dashboard_stats_unauthorized(self, client, auth):\n        \"\"\"Test dashboard stats access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/stats')\n        assert response.status_code == 401\n\n    def test_get_recent_activities_success(self, client, auth, sample_users):\n        \"\"\"Test successful recent activities retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/recent-activities')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'activities' in data['data']\n        \n        activities = data['data']['activities']\n        assert isinstance(activities, list)\n        \n        # Check activity structure if any exist\n        if activities:\n            activity = activities[0]\n            assert 'type' in activity\n            assert 'id' in activity\n            assert 'title' in activity\n            assert 'timestamp' in activity\n\n    def test_get_recent_activities_with_limit(self, client, auth, sample_users):\n        \"\"\"Test recent activities with custom limit.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/recent-activities?limit=5')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        activities = data['data']['activities']\n        assert len(activities) <= 5\n\n    def test_get_upcoming_tasks_success(self, client, auth, sample_users):\n        \"\"\"Test successful upcoming tasks retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/upcoming-tasks')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'tasks' in data['data']\n        \n        tasks = data['data']['tasks']\n        assert isinstance(tasks, list)\n        \n        # Check task structure if any exist\n        if tasks:\n            task = tasks[0]\n            assert 'id' in task\n            assert 'name' in task\n            assert 'project_name' in task\n            assert 'due_date' in task\n            assert 'days_until_due' in task\n\n    def test_get_upcoming_tasks_with_params(self, client, auth, sample_users):\n        \"\"\"Test upcoming tasks with custom parameters.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/upcoming-tasks?days=14&limit=3')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        tasks = data['data']['tasks']\n        assert len(tasks) <= 3\n\n\nclass TestDashboardKPIsAPI:\n    \"\"\"Test dashboard KPIs API endpoints.\"\"\"\n\n    def test_get_dashboard_kpis_success(self, client, auth, sample_users):\n        \"\"\"Test successful KPIs retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/kpis')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'kpis' in data['data']\n        \n        kpis = data['data']['kpis']\n        assert isinstance(kpis, list)\n        \n        # Check KPI structure if any exist\n        if kpis:\n            kpi = kpis[0]\n            assert 'id' in kpi\n            assert 'name' in kpi\n            assert 'category' in kpi\n            assert 'current_value' in kpi\n            assert 'target_value' in kpi\n            assert 'performance_percentage' in kpi\n\n    def test_get_dashboard_kpis_with_category(self, client, auth, sample_users):\n        \"\"\"Test KPIs retrieval with category filter.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/kpis?category=performance')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n\n    def test_get_dashboard_kpis_unauthorized(self, client, auth):\n        \"\"\"Test KPIs access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/kpis')\n        assert response.status_code == 401\n\n\nclass TestDashboardChartsAPI:\n    \"\"\"Test dashboard charts API endpoints.\"\"\"\n\n    def test_get_project_status_chart_success(self, client, auth, sample_users):\n        \"\"\"Test successful project status chart data retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/charts/project-status')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'chart' in data['data']\n        \n        chart = data['data']['chart']\n        assert 'labels' in chart\n        assert 'data' in chart\n        assert 'total' in chart\n        assert isinstance(chart['labels'], list)\n        assert isinstance(chart['data'], list)\n\n    def test_get_task_status_chart_success(self, client, auth, sample_users):\n        \"\"\"Test successful task status chart data retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/charts/task-status')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'chart' in data['data']\n        \n        chart = data['data']['chart']\n        assert 'labels' in chart\n        assert 'data' in chart\n        assert 'total' in chart\n\n    def test_get_charts_unauthorized(self, client, auth):\n        \"\"\"Test charts access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/charts/project-status')\n        assert response.status_code == 401\n        \n        response = client.get('/api/dashboard/charts/task-status')\n        assert response.status_code == 401\n\n\nclass TestDashboardQuickActionsAPI:\n    \"\"\"Test dashboard quick actions API endpoints.\"\"\"\n\n    def test_get_quick_actions_success(self, client, auth, sample_users):\n        \"\"\"Test successful quick actions retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/quick-actions')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'actions' in data['data']\n        \n        actions = data['data']['actions']\n        assert isinstance(actions, list)\n        assert len(actions) > 0  # Should have at least basic actions\n        \n        # Check action structure\n        action = actions[0]\n        assert 'id' in action\n        assert 'title' in action\n        assert 'description' in action\n        assert 'icon' in action\n        assert 'url' in action\n        assert 'category' in action\n\n    def test_get_quick_actions_unauthorized(self, client, auth):\n        \"\"\"Test quick actions access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/quick-actions')\n        assert response.status_code == 401\n\n\nclass TestDashboardNewsAPI:\n    \"\"\"Test dashboard news API endpoints.\"\"\"\n\n    def test_get_dashboard_news_success(self, client, auth, sample_users):\n        \"\"\"Test successful news retrieval.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/news')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'data' in data\n        assert 'news' in data['data']\n        \n        news = data['data']['news']\n        assert isinstance(news, list)\n        \n        # Check news structure if any exist\n        if news:\n            news_item = news[0]\n            assert 'id' in news_item\n            assert 'title' in news_item\n            assert 'content' in news_item\n            assert 'author_name' in news_item\n            assert 'created_at' in news_item\n\n    def test_get_dashboard_news_with_limit(self, client, auth, sample_users):\n        \"\"\"Test news retrieval with custom limit.\"\"\"\n        auth.login()\n        \n        response = client.get('/api/dashboard/news?limit=3')\n        assert response.status_code == 200\n        \n        data = json.loads(response.data)\n        news = data['data']['news']\n        assert len(news) <= 3\n\n    def test_get_dashboard_news_unauthorized(self, client, auth):\n        \"\"\"Test news access without authentication.\"\"\"\n        auth.logout()\n        \n        response = client.get('/api/dashboard/news')\n        assert response.status_code == 401\n"}