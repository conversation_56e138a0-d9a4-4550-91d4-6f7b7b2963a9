{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/UserDropdown.vue"}, "originalCode": "<template>\n  <div class=\"relative\" v-click-outside=\"closeDropdown\">\n    <!-- User Button -->\n    <button\n      @click=\"toggleDropdown\"\n      class=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 transition-all\"\n    >\n      <img \n        v-if=\"authStore.user?.profile_image\"\n        class=\"h-8 w-8 rounded-full object-cover\"\n        :src=\"authStore.user.profile_image\" \n        :alt=\"authStore.userFullName\"\n      >\n      <div \n        v-else\n        class=\"h-8 w-8 rounded-full bg-brand-primary-500 flex items-center justify-center text-white text-sm font-medium\"\n      >\n        {{ authStore.userInitials }}\n      </div>\n    </button>\n\n    <!-- Dropdown -->\n    <transition name=\"dropdown\">\n      <div \n        v-if=\"isOpen\"\n        class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\"\n      >\n        <!-- User Info -->\n        <div class=\"px-4 py-3 border-b border-gray-200 dark:border-gray-700\">\n          <p class=\"text-sm font-medium text-brand-text-primary\">\n            {{ authStore.userFullName }}\n          </p>\n          <p class=\"text-xs text-brand-text-secondary\">\n            {{ authStore.user?.email }}\n          </p>\n          <span class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-brand-primary-100 text-brand-primary-800 dark:bg-brand-primary-900 dark:text-brand-primary-200 mt-1\">\n            {{ authStore.userRole }}\n          </span>\n        </div>\n\n        <!-- Menu Items -->\n        <div class=\"py-1\">\n          <!-- Profile -->\n          <router-link\n            to=\"/profile\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-user mr-3 h-4 w-4 text-gray-400\"></i>\n            Il mio profilo\n          </router-link>\n\n          <!-- Settings -->\n          <router-link\n            to=\"/settings\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-cog mr-3 h-4 w-4 text-gray-400\"></i>\n            Impostazioni\n          </router-link>\n\n          <!-- Dark Mode Toggle -->\n          <button\n            @click=\"toggleDarkMode\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i \n              :class=\"[\n                appStore.darkMode ? 'fas fa-sun' : 'fas fa-moon',\n                'mr-3 h-4 w-4 text-gray-400'\n              ]\"\n            ></i>\n            {{ appStore.darkMode ? 'Modalità chiara' : 'Modalità scura' }}\n          </button>\n\n          <div class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- Admin Panel (Admin only) -->\n          <router-link\n            v-if=\"authStore.isAdmin\"\n            to=\"/admin\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-shield-alt mr-3 h-4 w-4 text-brand-warning-500\"></i>\n            Pannello Admin\n          </router-link>\n\n          <!-- Brand Settings (Admin only) -->\n          <router-link\n            v-if=\"authStore.isAdmin\"\n            to=\"/settings/brand\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-palette mr-3 h-4 w-4 text-brand-accent-500\"></i>\n            Impostazioni Brand\n          </router-link>\n\n          <div v-if=\"authStore.isAdmin\" class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- Logout -->\n          <button\n            @click=\"logout\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-error-600 hover:bg-brand-error-50 dark:hover:bg-brand-error-900/20 transition-colors\"\n          >\n            <i class=\"fas fa-sign-out-alt mr-3 h-4 w-4\"></i>\n            Logout\n          </button>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\n\n// State\nconst isOpen = ref(false)\n\n// Methods\nfunction toggleDropdown() {\n  isOpen.value = !isOpen.value\n}\n\nfunction closeDropdown() {\n  isOpen.value = false\n}\n\nfunction toggleDarkMode() {\n  appStore.toggleDarkMode()\n}\n\nfunction logout() {\n  closeDropdown()\n  authStore.logout()\n}\n</script>\n\n<style scoped>\n/* Dropdown animations */\n.dropdown-enter-active,\n.dropdown-leave-active {\n  transition: all 0.2s ease;\n}\n\n.dropdown-enter-from,\n.dropdown-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Avatar hover effect */\nbutton:hover img,\nbutton:hover div {\n  transform: scale(1.05);\n  transition: transform 0.2s ease;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n\n/* Menu item hover effects */\n.router-link:hover i,\nbutton:hover i {\n  color: var(--brand-primary-500);\n  transition: color 0.2s ease;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"relative\" v-click-outside=\"closeDropdown\">\n    <!-- User Button -->\n    <button\n      @click=\"toggleDropdown\"\n      class=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 transition-all\"\n    >\n      <img \n        v-if=\"authStore.user?.profile_image\"\n        class=\"h-8 w-8 rounded-full object-cover\"\n        :src=\"authStore.user.profile_image\" \n        :alt=\"authStore.userFullName\"\n      >\n      <div \n        v-else\n        class=\"h-8 w-8 rounded-full bg-brand-primary-500 flex items-center justify-center text-white text-sm font-medium\"\n      >\n        {{ authStore.userInitials }}\n      </div>\n    </button>\n\n    <!-- Dropdown -->\n    <transition name=\"dropdown\">\n      <div \n        v-if=\"isOpen\"\n        class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\"\n      >\n        <!-- User Info -->\n        <div class=\"px-4 py-3 border-b border-gray-200 dark:border-gray-700\">\n          <p class=\"text-sm font-medium text-brand-text-primary\">\n            {{ authStore.userFullName }}\n          </p>\n          <p class=\"text-xs text-brand-text-secondary\">\n            {{ authStore.user?.email }}\n          </p>\n          <span class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-brand-primary-100 text-brand-primary-800 dark:bg-brand-primary-900 dark:text-brand-primary-200 mt-1\">\n            {{ authStore.userRole }}\n          </span>\n        </div>\n\n        <!-- Menu Items -->\n        <div class=\"py-1\">\n          <!-- Profile -->\n          <router-link\n            to=\"/profile\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-user mr-3 h-4 w-4 text-gray-400\"></i>\n            Il mio profilo\n          </router-link>\n\n          <!-- Settings -->\n          <router-link\n            to=\"/settings\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-cog mr-3 h-4 w-4 text-gray-400\"></i>\n            Impostazioni\n          </router-link>\n\n          <!-- Dark Mode Toggle -->\n          <button\n            @click=\"toggleDarkMode\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i \n              :class=\"[\n                appStore.darkMode ? 'fas fa-sun' : 'fas fa-moon',\n                'mr-3 h-4 w-4 text-gray-400'\n              ]\"\n            ></i>\n            {{ appStore.darkMode ? 'Modalità chiara' : 'Modalità scura' }}\n          </button>\n\n          <div class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- Admin Panel (Admin only) -->\n          <router-link\n            v-if=\"authStore.isAdmin\"\n            to=\"/admin\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-shield-alt mr-3 h-4 w-4 text-brand-warning-500\"></i>\n            Pannello Admin\n          </router-link>\n\n          <!-- Brand Settings (Admin only) -->\n          <router-link\n            v-if=\"authStore.isAdmin\"\n            to=\"/settings/brand\"\n            @click=\"closeDropdown\"\n            class=\"flex items-center px-4 py-2 text-sm text-brand-text-primary hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i class=\"fas fa-palette mr-3 h-4 w-4 text-brand-accent-500\"></i>\n            Impostazioni Brand\n          </router-link>\n\n          <div v-if=\"authStore.isAdmin\" class=\"border-t border-gray-200 dark:border-gray-700 my-1\"></div>\n\n          <!-- Logout -->\n          <button\n            @click=\"logout\"\n            class=\"flex items-center w-full px-4 py-2 text-sm text-brand-error-600 hover:bg-brand-error-50 dark:hover:bg-brand-error-900/20 transition-colors\"\n          >\n            <i class=\"fas fa-sign-out-alt mr-3 h-4 w-4\"></i>\n            Logout\n          </button>\n        </div>\n      </div>\n    </transition>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { useAuthStore } from '../../stores/auth.js'\nimport { useAppStore } from '../../stores/app.js'\n\n// Stores\nconst authStore = useAuthStore()\nconst appStore = useAppStore()\n\n// State\nconst isOpen = ref(false)\n\n// Methods\nfunction toggleDropdown() {\n  isOpen.value = !isOpen.value\n}\n\nfunction closeDropdown() {\n  isOpen.value = false\n}\n\nfunction toggleDarkMode() {\n  appStore.toggleDarkMode()\n}\n\nfunction logout() {\n  closeDropdown()\n  authStore.logout()\n}\n</script>\n\n<style scoped>\n/* Dropdown animations */\n.dropdown-enter-active,\n.dropdown-leave-active {\n  transition: all 0.2s ease;\n}\n\n.dropdown-enter-from,\n.dropdown-leave-to {\n  opacity: 0;\n  transform: translateY(-10px);\n}\n\n/* Avatar hover effect */\nbutton:hover img,\nbutton:hover div {\n  transform: scale(1.05);\n  transition: transform 0.2s ease;\n}\n\n/* Focus styles */\nbutton:focus {\n  outline: 2px solid var(--brand-primary-500);\n  outline-offset: 2px;\n}\n\n/* Menu item hover effects */\n.router-link:hover i,\nbutton:hover i {\n  color: var(--brand-primary-500);\n  transition: color 0.2s ease;\n}\n</style>\n"}