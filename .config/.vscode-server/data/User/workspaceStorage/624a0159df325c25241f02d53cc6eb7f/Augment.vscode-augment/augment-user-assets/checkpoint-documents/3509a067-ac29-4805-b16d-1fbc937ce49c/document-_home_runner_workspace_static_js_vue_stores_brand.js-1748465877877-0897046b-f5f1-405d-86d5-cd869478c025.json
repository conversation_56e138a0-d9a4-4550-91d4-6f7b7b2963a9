{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/brand.js"}, "originalCode": "/**\n * Brand Store - Pinia Store per gestione branding\n * Gestisce colori, font, logo e altre impostazioni di brand\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed, watch } from 'vue'\n\nexport const useBrandStore = defineStore('brand', () => {\n  // === STATE ===\n  const brandConfig = ref({\n    // Brand Identity (personalizzabile per ogni tenant)\n    name: window.APP_CONFIG?.brand?.name || 'DatVinci',\n    tagline: window.APP_CONFIG?.brand?.tagline || 'Innovazione per il futuro',\n    description: window.APP_CONFIG?.brand?.description || 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n\n    // Logo Configuration\n    logos: {\n      main: '/static/img/logo.png',\n      compact: '/static/img/logo_transparent.png',\n      white: '/static/img/logo_white.png',\n      dark: '/static/img/logo_dark.png',\n      favicon: '/static/favicon.ico'\n    },\n\n    // Color Palette\n    colors: {\n      primary: {\n        50: '#e6f4fb',\n        100: '#b3e0f3',\n        200: '#80cceb',\n        300: '#4db7e3',\n        400: '#1aa3dc',\n        500: '#0080c0',\n        600: '#006699',\n        700: '#004d73',\n        800: '#00334d',\n        900: '#001a26'\n      },\n      secondary: {\n        50: '#e6fff9',\n        100: '#b3ffec',\n        200: '#80ffdf',\n        300: '#4dffd3',\n        400: '#1affc6',\n        500: '#00cc99',\n        600: '#00a677',\n        700: '#007f59',\n        800: '#00533c',\n        900: '#00291e'\n      },\n      accent: {\n        50: '#fef7e6',\n        100: '#fde8b3',\n        200: '#fcd980',\n        300: '#fbca4d',\n        400: '#fabb1a',\n        500: '#f59e0b',\n        600: '#d97706',\n        700: '#b45309',\n        800: '#92400e',\n        900: '#78350f'\n      },\n      success: {\n        50: '#ecfdf5',\n        500: '#10b981',\n        600: '#059669',\n        700: '#047857'\n      },\n      warning: {\n        50: '#fffbeb',\n        500: '#f59e0b',\n        600: '#d97706',\n        700: '#b45309'\n      },\n      error: {\n        50: '#fef2f2',\n        500: '#ef4444',\n        600: '#dc2626',\n        700: '#b91c1c'\n      }\n    },\n\n    // Typography\n    typography: {\n      fonts: {\n        heading: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n        body: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n        mono: \"'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace\"\n      },\n      weights: {\n        light: 300,\n        normal: 400,\n        medium: 500,\n        semibold: 600,\n        bold: 700,\n        extrabold: 800\n      },\n      sizes: {\n        xs: '0.75rem',\n        sm: '0.875rem',\n        base: '1rem',\n        lg: '1.125rem',\n        xl: '1.25rem',\n        '2xl': '1.5rem',\n        '3xl': '1.875rem',\n        '4xl': '2.25rem'\n      }\n    },\n\n    // Layout & Spacing\n    layout: {\n      spacing: {\n        xs: '0.25rem',\n        sm: '0.5rem',\n        md: '1rem',\n        lg: '1.5rem',\n        xl: '2rem',\n        '2xl': '3rem',\n        '3xl': '4rem'\n      },\n      borderRadius: {\n        none: '0',\n        sm: '0.125rem',\n        md: '0.375rem',\n        lg: '0.5rem',\n        xl: '0.75rem',\n        '2xl': '1rem',\n        full: '9999px'\n      },\n      shadows: {\n        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      },\n      transitions: {\n        fast: '150ms ease-in-out',\n        normal: '250ms ease-in-out',\n        slow: '350ms ease-in-out'\n      }\n    },\n\n    // Component Styles\n    components: {\n      sidebar: {\n        width: '16rem',\n        collapsedWidth: '5rem'\n      },\n      header: {\n        height: '4rem'\n      },\n      container: {\n        maxWidth: '1280px'\n      }\n    }\n  })\n\n  // Current theme (light/dark)\n  const currentTheme = ref('light')\n\n  // === COMPUTED ===\n  const primaryColor = computed(() => brandConfig.value.colors.primary[500])\n  const secondaryColor = computed(() => brandConfig.value.colors.secondary[500])\n  const accentColor = computed(() => brandConfig.value.colors.accent[500])\n\n  const currentLogo = computed(() => {\n    if (currentTheme.value === 'dark') {\n      return brandConfig.value.logos.white || brandConfig.value.logos.main\n    }\n    return brandConfig.value.logos.main\n  })\n\n  const compactLogo = computed(() => {\n    if (currentTheme.value === 'dark') {\n      return brandConfig.value.logos.white || brandConfig.value.logos.compact\n    }\n    return brandConfig.value.logos.compact\n  })\n\n  // === ACTIONS ===\n\n  /**\n   * Aggiorna la configurazione del brand\n   * @param {Object} newConfig - Nuova configurazione (merge con quella esistente)\n   */\n  function updateBrandConfig(newConfig) {\n    brandConfig.value = {\n      ...brandConfig.value,\n      ...newConfig,\n      colors: { ...brandConfig.value.colors, ...newConfig.colors },\n      typography: { ...brandConfig.value.typography, ...newConfig.typography },\n      layout: { ...brandConfig.value.layout, ...newConfig.layout },\n      components: { ...brandConfig.value.components, ...newConfig.components }\n    }\n\n    // Applica le modifiche al CSS\n    applyBrandToCss()\n  }\n\n  /**\n   * Cambia il tema corrente\n   * @param {string} theme - 'light' o 'dark'\n   */\n  function setTheme(theme) {\n    currentTheme.value = theme\n    document.documentElement.setAttribute('data-theme', theme)\n\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n\n    // Salva la preferenza\n    localStorage.setItem('brand-theme', theme)\n  }\n\n  /**\n   * Applica la configurazione brand alle variabili CSS\n   */\n  function applyBrandToCss() {\n    const root = document.documentElement\n    const config = brandConfig.value\n\n    // Applica colori\n    Object.entries(config.colors.primary).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-primary-${key}`, value)\n    })\n\n    Object.entries(config.colors.secondary).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-secondary-${key}`, value)\n    })\n\n    Object.entries(config.colors.accent).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-accent-${key}`, value)\n    })\n\n    // Applica tipografia\n    root.style.setProperty('--brand-font-heading', config.typography.fonts.heading)\n    root.style.setProperty('--brand-font-body', config.typography.fonts.body)\n    root.style.setProperty('--brand-font-mono', config.typography.fonts.mono)\n\n    Object.entries(config.typography.weights).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-font-weight-${key}`, value)\n    })\n\n    Object.entries(config.typography.sizes).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-text-${key}`, value)\n    })\n\n    // Applica layout\n    Object.entries(config.layout.spacing).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-spacing-${key}`, value)\n    })\n\n    Object.entries(config.layout.borderRadius).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-radius-${key}`, value)\n    })\n\n    Object.entries(config.layout.shadows).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-shadow-${key}`, value)\n    })\n\n    Object.entries(config.layout.transitions).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-transition-${key}`, value)\n    })\n\n    // Applica logo paths\n    Object.entries(config.logos).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-logo-${key}`, `url('${value}')`)\n    })\n\n    // Applica componenti\n    root.style.setProperty('--brand-sidebar-width', config.components.sidebar.width)\n    root.style.setProperty('--brand-sidebar-collapsed-width', config.components.sidebar.collapsedWidth)\n    root.style.setProperty('--brand-header-height', config.components.header.height)\n    root.style.setProperty('--brand-container-max-width', config.components.container.maxWidth)\n  }\n\n  /**\n   * Carica la configurazione brand da API o localStorage\n   */\n  async function loadBrandConfig() {\n    try {\n      // Prova a caricare da API (per configurazioni salvate)\n      const response = await fetch('/api/brand/config')\n      if (response.ok) {\n        const config = await response.json()\n        updateBrandConfig(config.data)\n      }\n    } catch (error) {\n      console.log('Using default brand configuration')\n    }\n\n    // Carica tema salvato\n    const savedTheme = localStorage.getItem('brand-theme') || 'light'\n    setTheme(savedTheme)\n\n    // Applica configurazione iniziale\n    applyBrandToCss()\n  }\n\n  /**\n   * Salva la configurazione brand\n   */\n  async function saveBrandConfig() {\n    try {\n      await fetch('/api/brand/config', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        body: JSON.stringify(brandConfig.value)\n      })\n    } catch (error) {\n      console.error('Failed to save brand configuration:', error)\n    }\n  }\n\n  /**\n   * Reset alla configurazione di default\n   */\n  function resetBrandConfig() {\n    // Ricarica la configurazione di default\n    loadBrandConfig()\n  }\n\n  // Watch per auto-save quando cambia la configurazione\n  watch(brandConfig, () => {\n    applyBrandToCss()\n  }, { deep: true })\n\n  return {\n    // State\n    brandConfig,\n    currentTheme,\n\n    // Computed\n    primaryColor,\n    secondaryColor,\n    accentColor,\n    currentLogo,\n    compactLogo,\n\n    // Actions\n    updateBrandConfig,\n    setTheme,\n    applyBrandToCss,\n    loadBrandConfig,\n    saveBrandConfig,\n    resetBrandConfig\n  }\n})\n", "modifiedCode": "/**\n * Brand Store - Pinia Store per gestione branding\n * Gestisce colori, font, logo e altre impostazioni di brand\n */\n\nimport { defineStore } from 'pinia'\nimport { ref, computed, watch } from 'vue'\n\nexport const useBrandStore = defineStore('brand', () => {\n  // === STATE ===\n  const brandConfig = ref({\n    // Brand Identity (personalizzabile per ogni tenant)\n    name: window.APP_CONFIG?.brand?.name || 'DatVinci',\n    tagline: window.APP_CONFIG?.brand?.tagline || 'Innovazione per il futuro',\n    description: window.APP_CONFIG?.brand?.description || 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\\'avanguardia.',\n\n    // Logo Configuration\n    logos: {\n      main: '/static/img/logo.png',\n      compact: '/static/img/logo_transparent.png',\n      white: '/static/img/logo_white.png',\n      dark: '/static/img/logo_dark.png',\n      favicon: '/static/favicon.ico'\n    },\n\n    // Color Palette\n    colors: {\n      primary: {\n        50: '#e6f4fb',\n        100: '#b3e0f3',\n        200: '#80cceb',\n        300: '#4db7e3',\n        400: '#1aa3dc',\n        500: '#0080c0',\n        600: '#006699',\n        700: '#004d73',\n        800: '#00334d',\n        900: '#001a26'\n      },\n      secondary: {\n        50: '#e6fff9',\n        100: '#b3ffec',\n        200: '#80ffdf',\n        300: '#4dffd3',\n        400: '#1affc6',\n        500: '#00cc99',\n        600: '#00a677',\n        700: '#007f59',\n        800: '#00533c',\n        900: '#00291e'\n      },\n      accent: {\n        50: '#fef7e6',\n        100: '#fde8b3',\n        200: '#fcd980',\n        300: '#fbca4d',\n        400: '#fabb1a',\n        500: '#f59e0b',\n        600: '#d97706',\n        700: '#b45309',\n        800: '#92400e',\n        900: '#78350f'\n      },\n      success: {\n        50: '#ecfdf5',\n        500: '#10b981',\n        600: '#059669',\n        700: '#047857'\n      },\n      warning: {\n        50: '#fffbeb',\n        500: '#f59e0b',\n        600: '#d97706',\n        700: '#b45309'\n      },\n      error: {\n        50: '#fef2f2',\n        500: '#ef4444',\n        600: '#dc2626',\n        700: '#b91c1c'\n      }\n    },\n\n    // Typography\n    typography: {\n      fonts: {\n        heading: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n        body: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n        mono: \"'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace\"\n      },\n      weights: {\n        light: 300,\n        normal: 400,\n        medium: 500,\n        semibold: 600,\n        bold: 700,\n        extrabold: 800\n      },\n      sizes: {\n        xs: '0.75rem',\n        sm: '0.875rem',\n        base: '1rem',\n        lg: '1.125rem',\n        xl: '1.25rem',\n        '2xl': '1.5rem',\n        '3xl': '1.875rem',\n        '4xl': '2.25rem'\n      }\n    },\n\n    // Layout & Spacing\n    layout: {\n      spacing: {\n        xs: '0.25rem',\n        sm: '0.5rem',\n        md: '1rem',\n        lg: '1.5rem',\n        xl: '2rem',\n        '2xl': '3rem',\n        '3xl': '4rem'\n      },\n      borderRadius: {\n        none: '0',\n        sm: '0.125rem',\n        md: '0.375rem',\n        lg: '0.5rem',\n        xl: '0.75rem',\n        '2xl': '1rem',\n        full: '9999px'\n      },\n      shadows: {\n        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',\n        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n      },\n      transitions: {\n        fast: '150ms ease-in-out',\n        normal: '250ms ease-in-out',\n        slow: '350ms ease-in-out'\n      }\n    },\n\n    // Component Styles\n    components: {\n      sidebar: {\n        width: '16rem',\n        collapsedWidth: '5rem'\n      },\n      header: {\n        height: '4rem'\n      },\n      container: {\n        maxWidth: '1280px'\n      }\n    }\n  })\n\n  // Current theme (light/dark)\n  const currentTheme = ref('light')\n\n  // === COMPUTED ===\n  const primaryColor = computed(() => brandConfig.value.colors.primary[500])\n  const secondaryColor = computed(() => brandConfig.value.colors.secondary[500])\n  const accentColor = computed(() => brandConfig.value.colors.accent[500])\n\n  const currentLogo = computed(() => {\n    if (currentTheme.value === 'dark') {\n      return brandConfig.value.logos.white || brandConfig.value.logos.main\n    }\n    return brandConfig.value.logos.main\n  })\n\n  const compactLogo = computed(() => {\n    if (currentTheme.value === 'dark') {\n      return brandConfig.value.logos.white || brandConfig.value.logos.compact\n    }\n    return brandConfig.value.logos.compact\n  })\n\n  // === ACTIONS ===\n\n  /**\n   * Aggiorna la configurazione del brand\n   * @param {Object} newConfig - Nuova configurazione (merge con quella esistente)\n   */\n  function updateBrandConfig(newConfig) {\n    brandConfig.value = {\n      ...brandConfig.value,\n      ...newConfig,\n      colors: { ...brandConfig.value.colors, ...newConfig.colors },\n      typography: { ...brandConfig.value.typography, ...newConfig.typography },\n      layout: { ...brandConfig.value.layout, ...newConfig.layout },\n      components: { ...brandConfig.value.components, ...newConfig.components }\n    }\n\n    // Applica le modifiche al CSS\n    applyBrandToCss()\n  }\n\n  /**\n   * Cambia il tema corrente\n   * @param {string} theme - 'light' o 'dark'\n   */\n  function setTheme(theme) {\n    currentTheme.value = theme\n    document.documentElement.setAttribute('data-theme', theme)\n\n    if (theme === 'dark') {\n      document.documentElement.classList.add('dark')\n    } else {\n      document.documentElement.classList.remove('dark')\n    }\n\n    // Salva la preferenza\n    localStorage.setItem('brand-theme', theme)\n  }\n\n  /**\n   * Applica la configurazione brand alle variabili CSS\n   */\n  function applyBrandToCss() {\n    const root = document.documentElement\n    const config = brandConfig.value\n\n    // Applica colori\n    Object.entries(config.colors.primary).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-primary-${key}`, value)\n    })\n\n    Object.entries(config.colors.secondary).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-secondary-${key}`, value)\n    })\n\n    Object.entries(config.colors.accent).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-accent-${key}`, value)\n    })\n\n    // Applica tipografia\n    root.style.setProperty('--brand-font-heading', config.typography.fonts.heading)\n    root.style.setProperty('--brand-font-body', config.typography.fonts.body)\n    root.style.setProperty('--brand-font-mono', config.typography.fonts.mono)\n\n    Object.entries(config.typography.weights).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-font-weight-${key}`, value)\n    })\n\n    Object.entries(config.typography.sizes).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-text-${key}`, value)\n    })\n\n    // Applica layout\n    Object.entries(config.layout.spacing).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-spacing-${key}`, value)\n    })\n\n    Object.entries(config.layout.borderRadius).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-radius-${key}`, value)\n    })\n\n    Object.entries(config.layout.shadows).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-shadow-${key}`, value)\n    })\n\n    Object.entries(config.layout.transitions).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-transition-${key}`, value)\n    })\n\n    // Applica logo paths\n    Object.entries(config.logos).forEach(([key, value]) => {\n      root.style.setProperty(`--brand-logo-${key}`, `url('${value}')`)\n    })\n\n    // Applica componenti\n    root.style.setProperty('--brand-sidebar-width', config.components.sidebar.width)\n    root.style.setProperty('--brand-sidebar-collapsed-width', config.components.sidebar.collapsedWidth)\n    root.style.setProperty('--brand-header-height', config.components.header.height)\n    root.style.setProperty('--brand-container-max-width', config.components.container.maxWidth)\n  }\n\n  /**\n   * Carica la configurazione brand da API o localStorage\n   */\n  async function loadBrandConfig() {\n    try {\n      // Prova a caricare da API (per configurazioni salvate)\n      const response = await fetch('/api/brand/config')\n      if (response.ok) {\n        const config = await response.json()\n        updateBrandConfig(config.data)\n      }\n    } catch (error) {\n      console.log('Using default brand configuration')\n    }\n\n    // Carica tema salvato\n    const savedTheme = localStorage.getItem('brand-theme') || 'light'\n    setTheme(savedTheme)\n\n    // Applica configurazione iniziale\n    applyBrandToCss()\n  }\n\n  /**\n   * Salva la configurazione brand\n   */\n  async function saveBrandConfig() {\n    try {\n      await fetch('/api/brand/config', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': window.APP_CONFIG.csrfToken\n        },\n        body: JSON.stringify(brandConfig.value)\n      })\n    } catch (error) {\n      console.error('Failed to save brand configuration:', error)\n    }\n  }\n\n  /**\n   * Reset alla configurazione di default\n   */\n  function resetBrandConfig() {\n    // Ricarica la configurazione di default\n    loadBrandConfig()\n  }\n\n  // Watch per auto-save quando cambia la configurazione\n  watch(brandConfig, () => {\n    applyBrandToCss()\n  }, { deep: true })\n\n  return {\n    // State\n    brandConfig,\n    currentTheme,\n\n    // Computed\n    primaryColor,\n    secondaryColor,\n    accentColor,\n    currentLogo,\n    compactLogo,\n\n    // Actions\n    updateBrandConfig,\n    setTheme,\n    applyBrandToCss,\n    loadBrandConfig,\n    saveBrandConfig,\n    resetBrandConfig\n  }\n})\n"}