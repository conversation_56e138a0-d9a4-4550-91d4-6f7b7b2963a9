{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/About.vue"}, "originalCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <!-- Hero Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h1 class=\"text-4xl font-bold mb-4\">\n          {{ tenantStore.getPageContent('about', 'hero', 'title') || 'Chi Siamo' }}\n        </h1>\n        <p class=\"text-xl text-brand-primary-100\">\n          {{ tenantStore.getPageContent('about', 'hero', 'subtitle') || 'La nostra storia e i nostri valori' }}\n        </p>\n      </div>\n    </section>\n\n    <!-- Content -->\n    <section class=\"py-16\">\n      <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"prose prose-lg max-w-none\">\n          <h2>{{ tenantStore.getPageContent('about', 'story_section', 'title') || 'La nostra storia' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'story_section', 'content') || 'Contenuto della storia aziendale...' }}</p>\n          \n          <h2>{{ tenantStore.getPageContent('about', 'mission_section', 'title') || 'La nostra missione' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'mission_section', 'content') || 'La nostra missione...' }}</p>\n          \n          <h2>{{ tenantStore.getPageContent('about', 'vision_section', 'title') || 'La nostra visione' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'vision_section', 'content') || 'La nostra visione...' }}</p>\n        </div>\n      </div>\n    </section>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst tenantStore = useTenantStore()\n</script>\n", "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-white dark:bg-gray-900\">\n    <PublicNavigation />\n    \n    <!-- Hero Section -->\n    <section class=\"bg-brand-primary-600 text-white py-16\">\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <h1 class=\"text-4xl font-bold mb-4\">\n          {{ tenantStore.getPageContent('about', 'hero', 'title') || 'Chi Siamo' }}\n        </h1>\n        <p class=\"text-xl text-brand-primary-100\">\n          {{ tenantStore.getPageContent('about', 'hero', 'subtitle') || 'La nostra storia e i nostri valori' }}\n        </p>\n      </div>\n    </section>\n\n    <!-- Content -->\n    <section class=\"py-16\">\n      <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div class=\"prose prose-lg max-w-none\">\n          <h2>{{ tenantStore.getPageContent('about', 'story_section', 'title') || 'La nostra storia' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'story_section', 'content') || 'Contenuto della storia aziendale...' }}</p>\n          \n          <h2>{{ tenantStore.getPageContent('about', 'mission_section', 'title') || 'La nostra missione' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'mission_section', 'content') || 'La nostra missione...' }}</p>\n          \n          <h2>{{ tenantStore.getPageContent('about', 'vision_section', 'title') || 'La nostra visione' }}</h2>\n          <p>{{ tenantStore.getPageContent('about', 'vision_section', 'content') || 'La nostra visione...' }}</p>\n        </div>\n      </div>\n    </section>\n\n    <PublicFooter />\n  </div>\n</template>\n\n<script setup>\nimport { useTenantStore } from '../../stores/tenant.js'\nimport PublicNavigation from '../../components/public/PublicNavigation.vue'\nimport PublicFooter from '../../components/public/PublicFooter.vue'\n\nconst tenantStore = useTenantStore()\n</script>\n"}