// VERSIONE SEMPLICE CHE FUNZIONA SUBITO!
console.log('🚀 Caricamento Vue.js...')

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'

// Componente HOME semplice
const Home = {
    template: `
        <div class="min-h-screen bg-gray-50">
            <nav class="bg-white shadow border-b">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h1 class="text-xl font-bold text-gray-900">DatPortal</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <router-link to="/" class="text-blue-600 font-medium">Home</router-link>
                            <router-link to="/dashboard" class="text-gray-700 hover:text-blue-600">Dashboard</router-link>
                            <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="flex items-center justify-center py-20">
                <div class="text-center">
                    <h1 class="text-5xl font-bold text-gray-900 mb-6">🎉 Vue.js FUNZIONA!</h1>
                    <p class="text-xl text-gray-600 mb-8">DatPortal SPA è operativo</p>
                    <div class="space-x-4">
                        <router-link to="/dashboard" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
                            Vai alla Dashboard
                        </router-link>
                        <a href="/auth/login" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600">
                            Login
                        </a>
                    </div>
                    <div class="mt-8 text-sm text-gray-500">
                        <p>Utente: {{ userInfo }}</p>
                        <p>Autenticato: {{ isAuthenticated ? 'SÌ' : 'NO' }}</p>
                    </div>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            userInfo: window.APP_CONFIG?.user?.username || 'Anonimo',
            isAuthenticated: window.APP_CONFIG?.isAuthenticated || false
        }
    }
}

// Dashboard semplice
const Dashboard = {
    template: `
        <div class="min-h-screen bg-gray-50">
            <nav class="bg-white shadow border-b">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h1 class="text-xl font-bold text-gray-900">DatPortal</h1>
                        </div>
                        <div class="flex items-center space-x-4">
                            <router-link to="/" class="text-gray-700 hover:text-blue-600">Home</router-link>
                            <router-link to="/dashboard" class="text-blue-600 font-medium">Dashboard</router-link>
                            <a href="/auth/login" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Login</a>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="max-w-7xl mx-auto px-4 py-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-semibold mb-2">Progetti</h3>
                        <p class="text-3xl font-bold text-blue-600">12</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-semibold mb-2">Task</h3>
                        <p class="text-3xl font-bold text-green-600">45</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow">
                        <h3 class="text-lg font-semibold mb-2">Team</h3>
                        <p class="text-3xl font-bold text-purple-600">8</p>
                    </div>
                </div>
            </div>
        </div>
    `
}

// Routes semplici
const routes = [
    { path: '/', component: Home },
    { path: '/dashboard', component: Dashboard },
    { path: '/:pathMatch(.*)*', redirect: '/' }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// Crea app
const app = createApp({
    template: '<router-view />'
})

app.use(router)
app.mount('#app')

console.log('✅ Vue.js caricato con successo!')
window.vueApp = app
