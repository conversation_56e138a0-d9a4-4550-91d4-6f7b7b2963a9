// Privacy.js - Vue.js component converted from .vue file
import PublicNavigation from '../../components/public/PublicNavigation.js'
import PublicFooter from '../../components/public/PublicFooter.js'

export default {
  name: 'Privacy',
  components: {
    PublicNavigation,
    PublicFooter
  },
  template: `
    <div class="min-h-screen bg-white dark:bg-gray-900">
      <PublicNavigation />

      <!-- Hero Section -->
      <section class="bg-brand-primary-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 class="text-4xl font-bold mb-4">
            Privacy Policy
          </h1>
          <p class="text-xl text-brand-primary-100">
            Informativa sulla privacy
          </p>
        </div>
      </section>

      <!-- Privacy Content -->
      <section class="py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="prose prose-lg max-w-none">
            <p class="text-sm text-gray-500 mb-8">
              Ultimo aggiornamento: 1 novembre 2023
            </p>

            <div class="space-y-8">
              <div>
                <h2>Introduzione</h2>
                <p>Questa informativa sulla privacy descrive come DatPortal raccoglie, utilizza e protegge le informazioni personali degli utenti.</p>
              </div>

              <div>
                <h2>Raccolta dei dati</h2>
                <p>Raccogliamo informazioni quando ti registri sul nostro sito, effettui un ordine o compili un modulo di contatto.</p>
              </div>

              <div>
                <h2>Utilizzo dei dati</h2>
                <p>Le informazioni che raccogliamo vengono utilizzate per personalizzare la tua esperienza e migliorare i nostri servizi.</p>
              </div>

              <div>
                <h2>Contatti</h2>
                <p>Per qualsiasi domanda riguardante questa privacy policy, puoi contattarci all'indirizzo <EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <PublicFooter />
    </div>
  `
}
