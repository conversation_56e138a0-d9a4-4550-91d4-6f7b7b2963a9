"use strict";var nf=Object.create;var Un=Object.defineProperty;var rf=Object.getOwnPropertyDescriptor;var of=Object.getOwnPropertyNames;var sf=Object.getPrototypeOf,af=Object.prototype.hasOwnProperty;var i=(t,e)=>Un(t,"name",{value:e,configurable:!0});var uf=(t,e)=>()=>(t&&(e=t(t=0)),e);var Z=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),lf=(t,e)=>{for(var n in e)Un(t,n,{get:e[n],enumerable:!0})},pa=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of of(e))!af.call(t,o)&&o!==n&&Un(t,o,{get:()=>e[o],enumerable:!(r=rf(e,o))||r.enumerable});return t};var Mt=(t,e,n)=>(n=t!=null?nf(sf(t)):{},pa(e||!t||!t.__esModule?Un(n,"default",{value:t,enumerable:!0}):n,t)),cf=t=>pa(Un({},"__esModule",{value:!0}),t);var importMetaUrlShim,y=uf(()=>{"use strict";importMetaUrlShim=typeof document>"u"?require("node:url").pathToFileURL(__filename).href:importMetaUrlShim});var xa=Z((exports,module)=>{y();var Module=Module!==void 0?Module:{},TreeSitter=function(){var initPromise,document=typeof window=="object"?{currentScript:window.document.currentScript}:null;class Parser{static{i(this,"Parser")}constructor(){this.initialize()}initialize(){throw new Error("cannot construct a Parser before calling `init()`")}static init(moduleOptions){return initPromise||(Module=Object.assign({},Module,moduleOptions),initPromise=new Promise(resolveInitPromise=>{var moduleOverrides=Object.assign({},Module),arguments_=[],thisProgram="./this.program",quit_=i((t,e)=>{throw e},"quit_"),ENVIRONMENT_IS_WEB=typeof window=="object",ENVIRONMENT_IS_WORKER=typeof importScripts=="function",ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",scriptDirectory="",read_,readAsync,readBinary,setWindowTitle;function locateFile(t){return Module.locateFile?Module.locateFile(t,scriptDirectory):scriptDirectory+t}i(locateFile,"locateFile");function logExceptionOnExit(t){t instanceof ExitStatus||err("exiting due to exception: "+t)}if(i(logExceptionOnExit,"logExceptionOnExit"),ENVIRONMENT_IS_NODE){var fs=require("fs"),nodePath=require("path");scriptDirectory=ENVIRONMENT_IS_WORKER?nodePath.dirname(scriptDirectory)+"/":__dirname+"/",read_=i((t,e)=>(t=isFileURI(t)?new URL(t):nodePath.normalize(t),fs.readFileSync(t,e?void 0:"utf8")),"read_"),readBinary=i(t=>{var e=read_(t,!0);return e.buffer||(e=new Uint8Array(e)),e},"readBinary"),readAsync=i((t,e,n)=>{t=isFileURI(t)?new URL(t):nodePath.normalize(t),fs.readFile(t,function(r,o){r?n(r):e(o.buffer)})},"readAsync"),process.argv.length>1&&(thisProgram=process.argv[1].replace(/\\/g,"/")),arguments_=process.argv.slice(2),typeof module<"u"&&(module.exports=Module),quit_=i((t,e)=>{if(keepRuntimeAlive())throw process.exitCode=t,e;logExceptionOnExit(e),process.exit(t)},"quit_"),Module.inspect=function(){return"[Emscripten Module object]"}}else(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)&&(ENVIRONMENT_IS_WORKER?scriptDirectory=self.location.href:document!==void 0&&document.currentScript&&(scriptDirectory=document.currentScript.src),scriptDirectory=scriptDirectory.indexOf("blob:")!==0?scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1):"",read_=i(t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},"read_"),ENVIRONMENT_IS_WORKER&&(readBinary=i(t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)},"readBinary")),readAsync=i((t,e,n)=>{var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="arraybuffer",r.onload=()=>{r.status==200||r.status==0&&r.response?e(r.response):n()},r.onerror=n,r.send(null)},"readAsync"),setWindowTitle=i(t=>document.title=t,"setWindowTitle"));var out=Module.print||console.log.bind(console),err=Module.printErr||console.warn.bind(console);Object.assign(Module,moduleOverrides),moduleOverrides=null,Module.arguments&&(arguments_=Module.arguments),Module.thisProgram&&(thisProgram=Module.thisProgram),Module.quit&&(quit_=Module.quit);var STACK_ALIGN=16,dynamicLibraries=Module.dynamicLibraries||[],wasmBinary;Module.wasmBinary&&(wasmBinary=Module.wasmBinary);var noExitRuntime=Module.noExitRuntime||!0,wasmMemory;typeof WebAssembly!="object"&&abort("no native wasm support detected");var ABORT=!1,EXITSTATUS,UTF8Decoder=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function UTF8ArrayToString(t,e,n){for(var r=e+n,o=e;t[o]&&!(o>=r);)++o;if(o-e>16&&t.buffer&&UTF8Decoder)return UTF8Decoder.decode(t.subarray(e,o));for(var s="";e<o;){var u=t[e++];if(128&u){var l=63&t[e++];if((224&u)!=192){var f=63&t[e++];if((u=(240&u)==224?(15&u)<<12|l<<6|f:(7&u)<<18|l<<12|f<<6|63&t[e++])<65536)s+=String.fromCharCode(u);else{var _=u-65536;s+=String.fromCharCode(55296|_>>10,56320|1023&_)}}else s+=String.fromCharCode((31&u)<<6|l)}else s+=String.fromCharCode(u)}return s}i(UTF8ArrayToString,"UTF8ArrayToString");function UTF8ToString(t,e){return t?UTF8ArrayToString(HEAPU8,t,e):""}i(UTF8ToString,"UTF8ToString");function stringToUTF8Array(t,e,n,r){if(!(r>0))return 0;for(var o=n,s=n+r-1,u=0;u<t.length;++u){var l=t.charCodeAt(u);if(l>=55296&&l<=57343&&(l=65536+((1023&l)<<10)|1023&t.charCodeAt(++u)),l<=127){if(n>=s)break;e[n++]=l}else if(l<=2047){if(n+1>=s)break;e[n++]=192|l>>6,e[n++]=128|63&l}else if(l<=65535){if(n+2>=s)break;e[n++]=224|l>>12,e[n++]=128|l>>6&63,e[n++]=128|63&l}else{if(n+3>=s)break;e[n++]=240|l>>18,e[n++]=128|l>>12&63,e[n++]=128|l>>6&63,e[n++]=128|63&l}}return e[n]=0,n-o}i(stringToUTF8Array,"stringToUTF8Array");function stringToUTF8(t,e,n){return stringToUTF8Array(t,HEAPU8,e,n)}i(stringToUTF8,"stringToUTF8");function lengthBytesUTF8(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);r<=127?e++:r<=2047?e+=2:r>=55296&&r<=57343?(e+=4,++n):e+=3}return e}i(lengthBytesUTF8,"lengthBytesUTF8");function updateGlobalBufferAndViews(t){buffer=t,Module.HEAP8=HEAP8=new Int8Array(t),Module.HEAP16=HEAP16=new Int16Array(t),Module.HEAP32=HEAP32=new Int32Array(t),Module.HEAPU8=HEAPU8=new Uint8Array(t),Module.HEAPU16=HEAPU16=new Uint16Array(t),Module.HEAPU32=HEAPU32=new Uint32Array(t),Module.HEAPF32=HEAPF32=new Float32Array(t),Module.HEAPF64=HEAPF64=new Float64Array(t)}i(updateGlobalBufferAndViews,"updateGlobalBufferAndViews");var INITIAL_MEMORY=Module.INITIAL_MEMORY||33554432;wasmMemory=Module.wasmMemory?Module.wasmMemory:new WebAssembly.Memory({initial:INITIAL_MEMORY/65536,maximum:32768}),wasmMemory&&(buffer=wasmMemory.buffer),INITIAL_MEMORY=buffer.byteLength,updateGlobalBufferAndViews(buffer);var wasmTable=new WebAssembly.Table({initial:20,element:"anyfunc"}),__ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATPOSTRUN__=[],__RELOC_FUNCS__=[],runtimeInitialized=!1;function keepRuntimeAlive(){return noExitRuntime}i(keepRuntimeAlive,"keepRuntimeAlive");function preRun(){if(Module.preRun)for(typeof Module.preRun=="function"&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}i(preRun,"preRun");function initRuntime(){runtimeInitialized=!0,callRuntimeCallbacks(__RELOC_FUNCS__),callRuntimeCallbacks(__ATINIT__)}i(initRuntime,"initRuntime");function preMain(){callRuntimeCallbacks(__ATMAIN__)}i(preMain,"preMain");function postRun(){if(Module.postRun)for(typeof Module.postRun=="function"&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}i(postRun,"postRun");function addOnPreRun(t){__ATPRERUN__.unshift(t)}i(addOnPreRun,"addOnPreRun");function addOnInit(t){__ATINIT__.unshift(t)}i(addOnInit,"addOnInit");function addOnPostRun(t){__ATPOSTRUN__.unshift(t)}i(addOnPostRun,"addOnPostRun");var runDependencies=0,runDependencyWatcher=null,dependenciesFulfilled=null;function addRunDependency(t){runDependencies++,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies)}i(addRunDependency,"addRunDependency");function removeRunDependency(t){if(runDependencies--,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies),runDependencies==0&&(runDependencyWatcher!==null&&(clearInterval(runDependencyWatcher),runDependencyWatcher=null),dependenciesFulfilled)){var e=dependenciesFulfilled;dependenciesFulfilled=null,e()}}i(removeRunDependency,"removeRunDependency");function abort(t){throw Module.onAbort&&Module.onAbort(t),err(t="Aborted("+t+")"),ABORT=!0,EXITSTATUS=1,t+=". Build with -sASSERTIONS for more info.",new WebAssembly.RuntimeError(t)}i(abort,"abort");var dataURIPrefix="data:application/octet-stream;base64,",wasmBinaryFile,tempDouble,tempI64;function isDataURI(t){return t.startsWith(dataURIPrefix)}i(isDataURI,"isDataURI");function isFileURI(t){return t.startsWith("file://")}i(isFileURI,"isFileURI");function getBinary(t){try{if(t==wasmBinaryFile&&wasmBinary)return new Uint8Array(wasmBinary);if(readBinary)return readBinary(t);throw"both async and sync fetching of the wasm failed"}catch(e){abort(e)}}i(getBinary,"getBinary");function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"&&!isFileURI(wasmBinaryFile))return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(t){if(!t.ok)throw"failed to load wasm binary file at '"+wasmBinaryFile+"'";return t.arrayBuffer()}).catch(function(){return getBinary(wasmBinaryFile)});if(readAsync)return new Promise(function(t,e){readAsync(wasmBinaryFile,function(n){t(new Uint8Array(n))},e)})}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}i(getBinaryPromise,"getBinaryPromise");function createWasm(){var t={env:asmLibraryArg,wasi_snapshot_preview1:asmLibraryArg,"GOT.mem":new Proxy(asmLibraryArg,GOTHandler),"GOT.func":new Proxy(asmLibraryArg,GOTHandler)};function e(o,s){var u=o.exports;u=relocateExports(u,1024);var l=getDylinkMetadata(s);l.neededDynlibs&&(dynamicLibraries=l.neededDynlibs.concat(dynamicLibraries)),mergeLibSymbols(u,"main"),Module.asm=u,addOnInit(Module.asm.__wasm_call_ctors),__RELOC_FUNCS__.push(Module.asm.__wasm_apply_data_relocs),removeRunDependency("wasm-instantiate")}i(e,"t");function n(o){e(o.instance,o.module)}i(n,"r");function r(o){return getBinaryPromise().then(function(s){return WebAssembly.instantiate(s,t)}).then(function(s){return s}).then(o,function(s){err("failed to asynchronously prepare wasm: "+s),abort(s)})}if(i(r,"_"),addRunDependency("wasm-instantiate"),Module.instantiateWasm)try{return Module.instantiateWasm(t,e)}catch(o){return err("Module.instantiateWasm callback failed with error: "+o),!1}return wasmBinary||typeof WebAssembly.instantiateStreaming!="function"||isDataURI(wasmBinaryFile)||isFileURI(wasmBinaryFile)||ENVIRONMENT_IS_NODE||typeof fetch!="function"?r(n):fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(o){return WebAssembly.instantiateStreaming(o,t).then(n,function(s){return err("wasm streaming compile failed: "+s),err("falling back to ArrayBuffer instantiation"),r(n)})}),{}}i(createWasm,"createWasm"),wasmBinaryFile="tree-sitter.wasm",isDataURI(wasmBinaryFile)||(wasmBinaryFile=locateFile(wasmBinaryFile));var ASM_CONSTS={};function ExitStatus(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}i(ExitStatus,"ExitStatus");var GOT={},CurrentModuleWeakSymbols=new Set([]),GOTHandler={get:i(function(t,e){var n=GOT[e];return n||(n=GOT[e]=new WebAssembly.Global({value:"i32",mutable:!0})),CurrentModuleWeakSymbols.has(e)||(n.required=!0),n},"get")};function callRuntimeCallbacks(t){for(;t.length>0;)t.shift()(Module)}i(callRuntimeCallbacks,"callRuntimeCallbacks");function getDylinkMetadata(t){var e=0,n=0;function r(){for(var z=0,L=1;;){var H=t[e++];if(z+=(127&H)*L,L*=128,!(128&H))break}return z}i(r,"_");function o(){var z=r();return UTF8ArrayToString(t,(e+=z)-z,z)}i(o,"n");function s(z,L){if(z)throw new Error(L)}i(s,"s");var u="dylink.0";if(t instanceof WebAssembly.Module){var l=WebAssembly.Module.customSections(t,u);l.length===0&&(u="dylink",l=WebAssembly.Module.customSections(t,u)),s(l.length===0,"need dylink section"),n=(t=new Uint8Array(l[0])).length}else{s(new Uint32Array(new Uint8Array(t.subarray(0,24)).buffer)[0]!=1836278016,"need to see wasm magic number"),s(t[8]!==0,"need the dylink section to be first"),e=9;var f=r();n=e+f,u=o()}var _={neededDynlibs:[],tlsExports:new Set,weakImports:new Set};if(u=="dylink"){_.memorySize=r(),_.memoryAlign=r(),_.tableSize=r(),_.tableAlign=r();for(var v=r(),T=0;T<v;++T){var M=o();_.neededDynlibs.push(M)}}else for(s(u!=="dylink.0");e<n;){var D=t[e++],W=r();if(D===1)_.memorySize=r(),_.memoryAlign=r(),_.tableSize=r(),_.tableAlign=r();else if(D===2)for(v=r(),T=0;T<v;++T)M=o(),_.neededDynlibs.push(M);else if(D===3)for(var F=r();F--;){var P=o();256&r()&&_.tlsExports.add(P)}else if(D===4)for(F=r();F--;)o(),P=o(),(3&r())==1&&_.weakImports.add(P);else e+=W}return _}i(getDylinkMetadata,"getDylinkMetadata");function getValue(t,e="i8"){switch(e.endsWith("*")&&(e="*"),e){case"i1":case"i8":return HEAP8[t>>0];case"i16":return HEAP16[t>>1];case"i32":case"i64":return HEAP32[t>>2];case"float":return HEAPF32[t>>2];case"double":return HEAPF64[t>>3];case"*":return HEAPU32[t>>2];default:abort("invalid type for getValue: "+e)}return null}i(getValue,"getValue");function asmjsMangle(t){return t.indexOf("dynCall_")==0||["stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0"].includes(t)?t:"_"+t}i(asmjsMangle,"asmjsMangle");function mergeLibSymbols(t,e){for(var n in t)if(t.hasOwnProperty(n)){asmLibraryArg.hasOwnProperty(n)||(asmLibraryArg[n]=t[n]);var r=asmjsMangle(n);Module.hasOwnProperty(r)||(Module[r]=t[n]),n=="__main_argc_argv"&&(Module._main=t[n])}}i(mergeLibSymbols,"mergeLibSymbols");var LDSO={loadedLibsByName:{},loadedLibsByHandle:{}};function dynCallLegacy(t,e,n){var r=Module["dynCall_"+t];return n&&n.length?r.apply(null,[e].concat(n)):r.call(null,e)}i(dynCallLegacy,"dynCallLegacy");var wasmTableMirror=[];function getWasmTableEntry(t){var e=wasmTableMirror[t];return e||(t>=wasmTableMirror.length&&(wasmTableMirror.length=t+1),wasmTableMirror[t]=e=wasmTable.get(t)),e}i(getWasmTableEntry,"getWasmTableEntry");function dynCall(t,e,n){return t.includes("j")?dynCallLegacy(t,e,n):getWasmTableEntry(e).apply(null,n)}i(dynCall,"dynCall");function createInvokeFunction(t){return function(){var e=stackSave();try{return dynCall(t,arguments[0],Array.prototype.slice.call(arguments,1))}catch(n){if(stackRestore(e),n!==n+0)throw n;_setThrew(1,0)}}}i(createInvokeFunction,"createInvokeFunction");var ___heap_base=78144;function zeroMemory(t,e){return HEAPU8.fill(0,t,t+e),t}i(zeroMemory,"zeroMemory");function getMemory(t){if(runtimeInitialized)return zeroMemory(_malloc(t),t);var e=___heap_base,n=e+t+15&-16;return ___heap_base=n,GOT.__heap_base.value=n,e}i(getMemory,"getMemory");function isInternalSym(t){return["__cpp_exception","__c_longjmp","__wasm_apply_data_relocs","__dso_handle","__tls_size","__tls_align","__set_stack_limits","_emscripten_tls_init","__wasm_init_tls","__wasm_call_ctors","__start_em_asm","__stop_em_asm"].includes(t)}i(isInternalSym,"isInternalSym");function uleb128Encode(t,e){t<128?e.push(t):e.push(t%128|128,t>>7)}i(uleb128Encode,"uleb128Encode");function sigToWasmTypes(t){for(var e={i:"i32",j:"i32",f:"f32",d:"f64",p:"i32"},n={parameters:[],results:t[0]=="v"?[]:[e[t[0]]]},r=1;r<t.length;++r)n.parameters.push(e[t[r]]),t[r]==="j"&&n.parameters.push("i32");return n}i(sigToWasmTypes,"sigToWasmTypes");function generateFuncType(t,e){var n=t.slice(0,1),r=t.slice(1),o={i:127,p:127,j:126,f:125,d:124};e.push(96),uleb128Encode(r.length,e);for(var s=0;s<r.length;++s)e.push(o[r[s]]);n=="v"?e.push(0):e.push(1,o[n])}i(generateFuncType,"generateFuncType");function convertJsFunctionToWasm(t,e){if(typeof WebAssembly.Function=="function")return new WebAssembly.Function(sigToWasmTypes(e),t);var n=[1];generateFuncType(e,n);var r=[0,97,115,109,1,0,0,0,1];uleb128Encode(n.length,r),r.push.apply(r,n),r.push(2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0);var o=new WebAssembly.Module(new Uint8Array(r));return new WebAssembly.Instance(o,{e:{f:t}}).exports.f}i(convertJsFunctionToWasm,"convertJsFunctionToWasm");function updateTableMap(t,e){if(functionsInTableMap)for(var n=t;n<t+e;n++){var r=getWasmTableEntry(n);r&&functionsInTableMap.set(r,n)}}i(updateTableMap,"updateTableMap");var functionsInTableMap=void 0,freeTableIndexes=[];function getEmptyTableSlot(){if(freeTableIndexes.length)return freeTableIndexes.pop();try{wasmTable.grow(1)}catch(t){throw t instanceof RangeError?"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.":t}return wasmTable.length-1}i(getEmptyTableSlot,"getEmptyTableSlot");function setWasmTableEntry(t,e){wasmTable.set(t,e),wasmTableMirror[t]=wasmTable.get(t)}i(setWasmTableEntry,"setWasmTableEntry");function addFunction(t,e){if(functionsInTableMap||(functionsInTableMap=new WeakMap,updateTableMap(0,wasmTable.length)),functionsInTableMap.has(t))return functionsInTableMap.get(t);var n=getEmptyTableSlot();try{setWasmTableEntry(n,t)}catch(r){if(!(r instanceof TypeError))throw r;setWasmTableEntry(n,convertJsFunctionToWasm(t,e))}return functionsInTableMap.set(t,n),n}i(addFunction,"addFunction");function updateGOT(t,e){for(var n in t)if(!isInternalSym(n)){var r=t[n];n.startsWith("orig$")&&(n=n.split("$")[1],e=!0),GOT[n]||(GOT[n]=new WebAssembly.Global({value:"i32",mutable:!0})),(e||GOT[n].value==0)&&(typeof r=="function"?GOT[n].value=addFunction(r):typeof r=="number"?GOT[n].value=r:err("unhandled export type for `"+n+"`: "+typeof r))}}i(updateGOT,"updateGOT");function relocateExports(t,e,n){var r={};for(var o in t){var s=t[o];typeof s=="object"&&(s=s.value),typeof s=="number"&&(s+=e),r[o]=s}return updateGOT(r,n),r}i(relocateExports,"relocateExports");function resolveGlobalSymbol(t,e){var n;return e&&(n=asmLibraryArg["orig$"+t]),n||(n=asmLibraryArg[t])&&n.stub&&(n=void 0),n||(n=Module[asmjsMangle(t)]),!n&&t.startsWith("invoke_")&&(n=createInvokeFunction(t.split("_")[1])),n}i(resolveGlobalSymbol,"resolveGlobalSymbol");function alignMemory(t,e){return Math.ceil(t/e)*e}i(alignMemory,"alignMemory");function loadWebAssemblyModule(binary,flags,handle){var metadata=getDylinkMetadata(binary);function loadModule(){var firstLoad=!handle||!HEAP8[handle+12>>0];if(firstLoad){var memAlign=Math.pow(2,metadata.memoryAlign);memAlign=Math.max(memAlign,STACK_ALIGN);var memoryBase=metadata.memorySize?alignMemory(getMemory(metadata.memorySize+memAlign),memAlign):0,tableBase=metadata.tableSize?wasmTable.length:0;handle&&(HEAP8[handle+12>>0]=1,HEAPU32[handle+16>>2]=memoryBase,HEAP32[handle+20>>2]=metadata.memorySize,HEAPU32[handle+24>>2]=tableBase,HEAP32[handle+28>>2]=metadata.tableSize)}else memoryBase=HEAPU32[handle+16>>2],tableBase=HEAPU32[handle+24>>2];var tableGrowthNeeded=tableBase+metadata.tableSize-wasmTable.length,moduleExports;function resolveSymbol(t){var e=resolveGlobalSymbol(t,!1);return e||(e=moduleExports[t]),e}i(resolveSymbol,"resolveSymbol"),tableGrowthNeeded>0&&wasmTable.grow(tableGrowthNeeded);var proxyHandler={get:i(function(t,e){switch(e){case"__memory_base":return memoryBase;case"__table_base":return tableBase}if(e in asmLibraryArg)return asmLibraryArg[e];var n;return e in t||(t[e]=function(){return n||(n=resolveSymbol(e)),n.apply(null,arguments)}),t[e]},"get")},proxy=new Proxy({},proxyHandler),info={"GOT.mem":new Proxy({},GOTHandler),"GOT.func":new Proxy({},GOTHandler),env:proxy,wasi_snapshot_preview1:proxy};function postInstantiation(instance){function addEmAsm(addr,body){for(var args=[],arity=0;arity<16&&body.indexOf("$"+arity)!=-1;arity++)args.push("$"+arity);args=args.join(",");var func="("+args+" ) => { "+body+"};";ASM_CONSTS[start]=eval(func)}if(i(addEmAsm,"addEmAsm"),updateTableMap(tableBase,metadata.tableSize),moduleExports=relocateExports(instance.exports,memoryBase),flags.allowUndefined||reportUndefinedSymbols(),"__start_em_asm"in moduleExports)for(var start=moduleExports.__start_em_asm,stop=moduleExports.__stop_em_asm;start<stop;){var jsString=UTF8ToString(start);addEmAsm(start,jsString),start=HEAPU8.indexOf(0,start)+1}var applyRelocs=moduleExports.__wasm_apply_data_relocs;applyRelocs&&(runtimeInitialized?applyRelocs():__RELOC_FUNCS__.push(applyRelocs));var init=moduleExports.__wasm_call_ctors;return init&&(runtimeInitialized?init():__ATINIT__.push(init)),moduleExports}if(i(postInstantiation,"postInstantiation"),flags.loadAsync){if(binary instanceof WebAssembly.Module){var instance=new WebAssembly.Instance(binary,info);return Promise.resolve(postInstantiation(instance))}return WebAssembly.instantiate(binary,info).then(function(t){return postInstantiation(t.instance)})}var module=binary instanceof WebAssembly.Module?binary:new WebAssembly.Module(binary),instance=new WebAssembly.Instance(module,info);return postInstantiation(instance)}return i(loadModule,"loadModule"),CurrentModuleWeakSymbols=metadata.weakImports,flags.loadAsync?metadata.neededDynlibs.reduce(function(t,e){return t.then(function(){return loadDynamicLibrary(e,flags)})},Promise.resolve()).then(function(){return loadModule()}):(metadata.neededDynlibs.forEach(function(t){loadDynamicLibrary(t,flags)}),loadModule())}i(loadWebAssemblyModule,"loadWebAssemblyModule");function loadDynamicLibrary(t,e,n){e=e||{global:!0,nodelete:!0};var r=LDSO.loadedLibsByName[t];if(r)return e.global&&!r.global&&(r.global=!0,r.module!=="loading"&&mergeLibSymbols(r.module,t)),e.nodelete&&r.refcount!==1/0&&(r.refcount=1/0),r.refcount++,n&&(LDSO.loadedLibsByHandle[n]=r),!e.loadAsync||Promise.resolve(!0);function o(l){if(e.fs&&e.fs.findObject(l)){var f=e.fs.readFile(l,{encoding:"binary"});return f instanceof Uint8Array||(f=new Uint8Array(f)),e.loadAsync?Promise.resolve(f):f}if(l=locateFile(l),e.loadAsync)return new Promise(function(_,v){readAsync(l,T=>_(new Uint8Array(T)),v)});if(!readBinary)throw new Error(l+": file not found, and synchronous loading of external files is not available");return readBinary(l)}i(o,"n");function s(){if(typeof preloadedWasm<"u"&&preloadedWasm[t]){var l=preloadedWasm[t];return e.loadAsync?Promise.resolve(l):l}return e.loadAsync?o(t).then(function(f){return loadWebAssemblyModule(f,e,n)}):loadWebAssemblyModule(o(t),e,n)}i(s,"s");function u(l){r.global&&mergeLibSymbols(l,t),r.module=l}return i(u,"a"),r={refcount:e.nodelete?1/0:1,name:t,module:"loading",global:e.global},LDSO.loadedLibsByName[t]=r,n&&(LDSO.loadedLibsByHandle[n]=r),e.loadAsync?s().then(function(l){return u(l),!0}):(u(s()),!0)}i(loadDynamicLibrary,"loadDynamicLibrary");function reportUndefinedSymbols(){for(var t in GOT)if(GOT[t].value==0){var e=resolveGlobalSymbol(t,!0);if(!e&&!GOT[t].required)continue;if(typeof e=="function")GOT[t].value=addFunction(e,e.sig);else{if(typeof e!="number")throw new Error("bad export type for `"+t+"`: "+typeof e);GOT[t].value=e}}}i(reportUndefinedSymbols,"reportUndefinedSymbols");function preloadDylibs(){dynamicLibraries.length?(addRunDependency("preloadDylibs"),dynamicLibraries.reduce(function(t,e){return t.then(function(){return loadDynamicLibrary(e,{loadAsync:!0,global:!0,nodelete:!0,allowUndefined:!0})})},Promise.resolve()).then(function(){reportUndefinedSymbols(),removeRunDependency("preloadDylibs")})):reportUndefinedSymbols()}i(preloadDylibs,"preloadDylibs");function setValue(t,e,n="i8"){switch(n.endsWith("*")&&(n="*"),n){case"i1":case"i8":HEAP8[t>>0]=e;break;case"i16":HEAP16[t>>1]=e;break;case"i32":HEAP32[t>>2]=e;break;case"i64":tempI64=[e>>>0,(tempDouble=e,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[t>>2]=tempI64[0],HEAP32[t+4>>2]=tempI64[1];break;case"float":HEAPF32[t>>2]=e;break;case"double":HEAPF64[t>>3]=e;break;case"*":HEAPU32[t>>2]=e;break;default:abort("invalid type for setValue: "+n)}}i(setValue,"setValue");var ___memory_base=new WebAssembly.Global({value:"i32",mutable:!1},1024),___stack_pointer=new WebAssembly.Global({value:"i32",mutable:!0},78144),___table_base=new WebAssembly.Global({value:"i32",mutable:!1},1),nowIsMonotonic=!0,_emscripten_get_now;function __emscripten_get_now_is_monotonic(){return nowIsMonotonic}i(__emscripten_get_now_is_monotonic,"__emscripten_get_now_is_monotonic");function _abort(){abort("")}i(_abort,"_abort");function _emscripten_date_now(){return Date.now()}i(_emscripten_date_now,"_emscripten_date_now");function _emscripten_memcpy_big(t,e,n){HEAPU8.copyWithin(t,e,e+n)}i(_emscripten_memcpy_big,"_emscripten_memcpy_big");function getHeapMax(){return 2147483648}i(getHeapMax,"getHeapMax");function emscripten_realloc_buffer(t){try{return wasmMemory.grow(t-buffer.byteLength+65535>>>16),updateGlobalBufferAndViews(wasmMemory.buffer),1}catch{}}i(emscripten_realloc_buffer,"emscripten_realloc_buffer");function _emscripten_resize_heap(t){var e=HEAPU8.length;t>>>=0;var n=getHeapMax();if(t>n)return!1;for(var r=1;r<=4;r*=2){var o=e*(1+.2/r);if(o=Math.min(o,t+100663296),emscripten_realloc_buffer(Math.min(n,(s=Math.max(t,o))+((u=65536)-s%u)%u)))return!0}var s,u;return!1}i(_emscripten_resize_heap,"_emscripten_resize_heap"),__emscripten_get_now_is_monotonic.sig="i",Module._abort=_abort,_abort.sig="v",_emscripten_date_now.sig="d",_emscripten_get_now=ENVIRONMENT_IS_NODE?()=>{var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:()=>performance.now(),_emscripten_get_now.sig="d",_emscripten_memcpy_big.sig="vppp",_emscripten_resize_heap.sig="ip";var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt:i(function(t,e,n){if(PATH.isAbs(e))return e;var r;if(t===-100?r=FS.cwd():r=SYSCALLS.getStreamFromFD(t).path,e.length==0){if(!n)throw new FS.ErrnoError(44);return r}return PATH.join2(r,e)},"calculateAt"),doStat:i(function(t,e,n){try{var r=t(e)}catch(l){if(l&&l.node&&PATH.normalize(e)!==PATH.normalize(FS.getPath(l.node)))return-54;throw l}HEAP32[n>>2]=r.dev,HEAP32[n+8>>2]=r.ino,HEAP32[n+12>>2]=r.mode,HEAPU32[n+16>>2]=r.nlink,HEAP32[n+20>>2]=r.uid,HEAP32[n+24>>2]=r.gid,HEAP32[n+28>>2]=r.rdev,tempI64=[r.size>>>0,(tempDouble=r.size,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n+40>>2]=tempI64[0],HEAP32[n+44>>2]=tempI64[1],HEAP32[n+48>>2]=4096,HEAP32[n+52>>2]=r.blocks;var o=r.atime.getTime(),s=r.mtime.getTime(),u=r.ctime.getTime();return tempI64=[Math.floor(o/1e3)>>>0,(tempDouble=Math.floor(o/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n+56>>2]=tempI64[0],HEAP32[n+60>>2]=tempI64[1],HEAPU32[n+64>>2]=o%1e3*1e3,tempI64=[Math.floor(s/1e3)>>>0,(tempDouble=Math.floor(s/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n+72>>2]=tempI64[0],HEAP32[n+76>>2]=tempI64[1],HEAPU32[n+80>>2]=s%1e3*1e3,tempI64=[Math.floor(u/1e3)>>>0,(tempDouble=Math.floor(u/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n+88>>2]=tempI64[0],HEAP32[n+92>>2]=tempI64[1],HEAPU32[n+96>>2]=u%1e3*1e3,tempI64=[r.ino>>>0,(tempDouble=r.ino,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[n+104>>2]=tempI64[0],HEAP32[n+108>>2]=tempI64[1],0},"doStat"),doMsync:i(function(t,e,n,r,o){if(!FS.isFile(e.node.mode))throw new FS.ErrnoError(43);if(2&r)return 0;var s=HEAPU8.slice(t,t+n);FS.msync(e,s,o,n,r)},"doMsync"),varargs:void 0,get:i(function(){return SYSCALLS.varargs+=4,HEAP32[SYSCALLS.varargs-4>>2]},"get"),getStr:i(function(t){return UTF8ToString(t)},"getStr"),getStreamFromFD:i(function(t){var e=FS.getStream(t);if(!e)throw new FS.ErrnoError(8);return e},"getStreamFromFD")};function _proc_exit(t){EXITSTATUS=t,keepRuntimeAlive()||(Module.onExit&&Module.onExit(t),ABORT=!0),quit_(t,new ExitStatus(t))}i(_proc_exit,"_proc_exit");function exitJS(t,e){EXITSTATUS=t,_proc_exit(t)}i(exitJS,"exitJS"),_proc_exit.sig="vi";var _exit=exitJS;function _fd_close(t){try{var e=SYSCALLS.getStreamFromFD(t);return FS.close(e),0}catch(n){if(typeof FS>"u"||!(n instanceof FS.ErrnoError))throw n;return n.errno}}i(_fd_close,"_fd_close");function convertI32PairToI53Checked(t,e){return e+2097152>>>0<4194305-!!t?(t>>>0)+4294967296*e:NaN}i(convertI32PairToI53Checked,"convertI32PairToI53Checked");function _fd_seek(t,e,n,r,o){try{var s=convertI32PairToI53Checked(e,n);if(isNaN(s))return 61;var u=SYSCALLS.getStreamFromFD(t);return FS.llseek(u,s,r),tempI64=[u.position>>>0,(tempDouble=u.position,+Math.abs(tempDouble)>=1?tempDouble>0?(0|Math.min(+Math.floor(tempDouble/4294967296),4294967295))>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[o>>2]=tempI64[0],HEAP32[o+4>>2]=tempI64[1],u.getdents&&s===0&&r===0&&(u.getdents=null),0}catch(l){if(typeof FS>"u"||!(l instanceof FS.ErrnoError))throw l;return l.errno}}i(_fd_seek,"_fd_seek");function doWritev(t,e,n,r){for(var o=0,s=0;s<n;s++){var u=HEAPU32[e>>2],l=HEAPU32[e+4>>2];e+=8;var f=FS.write(t,HEAP8,u,l,r);if(f<0)return-1;o+=f,r!==void 0&&(r+=f)}return o}i(doWritev,"doWritev");function _fd_write(t,e,n,r){try{var o=doWritev(SYSCALLS.getStreamFromFD(t),e,n);return HEAPU32[r>>2]=o,0}catch(s){if(typeof FS>"u"||!(s instanceof FS.ErrnoError))throw s;return s.errno}}i(_fd_write,"_fd_write");function _tree_sitter_log_callback(t,e){if(currentLogCallback){let n=UTF8ToString(e);currentLogCallback(n,t!==0)}}i(_tree_sitter_log_callback,"_tree_sitter_log_callback");function _tree_sitter_parse_callback(t,e,n,r,o){var s=currentParseCallback(e,{row:n,column:r});typeof s=="string"?(setValue(o,s.length,"i32"),stringToUTF16(s,t,10240)):setValue(o,0,"i32")}i(_tree_sitter_parse_callback,"_tree_sitter_parse_callback");function handleException(t){if(t instanceof ExitStatus||t=="unwind")return EXITSTATUS;quit_(1,t)}i(handleException,"handleException");function allocateUTF8OnStack(t){var e=lengthBytesUTF8(t)+1,n=stackAlloc(e);return stringToUTF8Array(t,HEAP8,n,e),n}i(allocateUTF8OnStack,"allocateUTF8OnStack");function stringToUTF16(t,e,n){if(n===void 0&&(n=2147483647),n<2)return 0;for(var r=e,o=(n-=2)<2*t.length?n/2:t.length,s=0;s<o;++s){var u=t.charCodeAt(s);HEAP16[e>>1]=u,e+=2}return HEAP16[e>>1]=0,e-r}i(stringToUTF16,"stringToUTF16");function AsciiToString(t){for(var e="";;){var n=HEAPU8[t++>>0];if(!n)return e;e+=String.fromCharCode(n)}}i(AsciiToString,"AsciiToString"),_exit.sig="vi",_fd_close.sig="ii",_fd_seek.sig="iijip",_fd_write.sig="iippp";var asmLibraryArg={__heap_base:___heap_base,__indirect_function_table:wasmTable,__memory_base:___memory_base,__stack_pointer:___stack_pointer,__table_base:___table_base,_emscripten_get_now_is_monotonic:__emscripten_get_now_is_monotonic,abort:_abort,emscripten_get_now:_emscripten_get_now,emscripten_memcpy_big:_emscripten_memcpy_big,emscripten_resize_heap:_emscripten_resize_heap,exit:_exit,fd_close:_fd_close,fd_seek:_fd_seek,fd_write:_fd_write,memory:wasmMemory,tree_sitter_log_callback:_tree_sitter_log_callback,tree_sitter_parse_callback:_tree_sitter_parse_callback},asm=createWasm(),___wasm_call_ctors=Module.___wasm_call_ctors=function(){return(___wasm_call_ctors=Module.___wasm_call_ctors=Module.asm.__wasm_call_ctors).apply(null,arguments)},___wasm_apply_data_relocs=Module.___wasm_apply_data_relocs=function(){return(___wasm_apply_data_relocs=Module.___wasm_apply_data_relocs=Module.asm.__wasm_apply_data_relocs).apply(null,arguments)},_malloc=Module._malloc=function(){return(_malloc=Module._malloc=Module.asm.malloc).apply(null,arguments)},_calloc=Module._calloc=function(){return(_calloc=Module._calloc=Module.asm.calloc).apply(null,arguments)},_realloc=Module._realloc=function(){return(_realloc=Module._realloc=Module.asm.realloc).apply(null,arguments)},_free=Module._free=function(){return(_free=Module._free=Module.asm.free).apply(null,arguments)},_ts_language_symbol_count=Module._ts_language_symbol_count=function(){return(_ts_language_symbol_count=Module._ts_language_symbol_count=Module.asm.ts_language_symbol_count).apply(null,arguments)},_ts_language_version=Module._ts_language_version=function(){return(_ts_language_version=Module._ts_language_version=Module.asm.ts_language_version).apply(null,arguments)},_ts_language_field_count=Module._ts_language_field_count=function(){return(_ts_language_field_count=Module._ts_language_field_count=Module.asm.ts_language_field_count).apply(null,arguments)},_ts_language_symbol_name=Module._ts_language_symbol_name=function(){return(_ts_language_symbol_name=Module._ts_language_symbol_name=Module.asm.ts_language_symbol_name).apply(null,arguments)},_ts_language_symbol_for_name=Module._ts_language_symbol_for_name=function(){return(_ts_language_symbol_for_name=Module._ts_language_symbol_for_name=Module.asm.ts_language_symbol_for_name).apply(null,arguments)},_ts_language_symbol_type=Module._ts_language_symbol_type=function(){return(_ts_language_symbol_type=Module._ts_language_symbol_type=Module.asm.ts_language_symbol_type).apply(null,arguments)},_ts_language_field_name_for_id=Module._ts_language_field_name_for_id=function(){return(_ts_language_field_name_for_id=Module._ts_language_field_name_for_id=Module.asm.ts_language_field_name_for_id).apply(null,arguments)},_memset=Module._memset=function(){return(_memset=Module._memset=Module.asm.memset).apply(null,arguments)},_memcpy=Module._memcpy=function(){return(_memcpy=Module._memcpy=Module.asm.memcpy).apply(null,arguments)},_ts_parser_delete=Module._ts_parser_delete=function(){return(_ts_parser_delete=Module._ts_parser_delete=Module.asm.ts_parser_delete).apply(null,arguments)},_ts_parser_reset=Module._ts_parser_reset=function(){return(_ts_parser_reset=Module._ts_parser_reset=Module.asm.ts_parser_reset).apply(null,arguments)},_ts_parser_set_language=Module._ts_parser_set_language=function(){return(_ts_parser_set_language=Module._ts_parser_set_language=Module.asm.ts_parser_set_language).apply(null,arguments)},_ts_parser_timeout_micros=Module._ts_parser_timeout_micros=function(){return(_ts_parser_timeout_micros=Module._ts_parser_timeout_micros=Module.asm.ts_parser_timeout_micros).apply(null,arguments)},_ts_parser_set_timeout_micros=Module._ts_parser_set_timeout_micros=function(){return(_ts_parser_set_timeout_micros=Module._ts_parser_set_timeout_micros=Module.asm.ts_parser_set_timeout_micros).apply(null,arguments)},_memmove=Module._memmove=function(){return(_memmove=Module._memmove=Module.asm.memmove).apply(null,arguments)},_memcmp=Module._memcmp=function(){return(_memcmp=Module._memcmp=Module.asm.memcmp).apply(null,arguments)},_ts_query_new=Module._ts_query_new=function(){return(_ts_query_new=Module._ts_query_new=Module.asm.ts_query_new).apply(null,arguments)},_ts_query_delete=Module._ts_query_delete=function(){return(_ts_query_delete=Module._ts_query_delete=Module.asm.ts_query_delete).apply(null,arguments)},_iswspace=Module._iswspace=function(){return(_iswspace=Module._iswspace=Module.asm.iswspace).apply(null,arguments)},_iswalnum=Module._iswalnum=function(){return(_iswalnum=Module._iswalnum=Module.asm.iswalnum).apply(null,arguments)},_ts_query_pattern_count=Module._ts_query_pattern_count=function(){return(_ts_query_pattern_count=Module._ts_query_pattern_count=Module.asm.ts_query_pattern_count).apply(null,arguments)},_ts_query_capture_count=Module._ts_query_capture_count=function(){return(_ts_query_capture_count=Module._ts_query_capture_count=Module.asm.ts_query_capture_count).apply(null,arguments)},_ts_query_string_count=Module._ts_query_string_count=function(){return(_ts_query_string_count=Module._ts_query_string_count=Module.asm.ts_query_string_count).apply(null,arguments)},_ts_query_capture_name_for_id=Module._ts_query_capture_name_for_id=function(){return(_ts_query_capture_name_for_id=Module._ts_query_capture_name_for_id=Module.asm.ts_query_capture_name_for_id).apply(null,arguments)},_ts_query_string_value_for_id=Module._ts_query_string_value_for_id=function(){return(_ts_query_string_value_for_id=Module._ts_query_string_value_for_id=Module.asm.ts_query_string_value_for_id).apply(null,arguments)},_ts_query_predicates_for_pattern=Module._ts_query_predicates_for_pattern=function(){return(_ts_query_predicates_for_pattern=Module._ts_query_predicates_for_pattern=Module.asm.ts_query_predicates_for_pattern).apply(null,arguments)},_ts_tree_copy=Module._ts_tree_copy=function(){return(_ts_tree_copy=Module._ts_tree_copy=Module.asm.ts_tree_copy).apply(null,arguments)},_ts_tree_delete=Module._ts_tree_delete=function(){return(_ts_tree_delete=Module._ts_tree_delete=Module.asm.ts_tree_delete).apply(null,arguments)},_ts_init=Module._ts_init=function(){return(_ts_init=Module._ts_init=Module.asm.ts_init).apply(null,arguments)},_ts_parser_new_wasm=Module._ts_parser_new_wasm=function(){return(_ts_parser_new_wasm=Module._ts_parser_new_wasm=Module.asm.ts_parser_new_wasm).apply(null,arguments)},_ts_parser_enable_logger_wasm=Module._ts_parser_enable_logger_wasm=function(){return(_ts_parser_enable_logger_wasm=Module._ts_parser_enable_logger_wasm=Module.asm.ts_parser_enable_logger_wasm).apply(null,arguments)},_ts_parser_parse_wasm=Module._ts_parser_parse_wasm=function(){return(_ts_parser_parse_wasm=Module._ts_parser_parse_wasm=Module.asm.ts_parser_parse_wasm).apply(null,arguments)},_ts_language_type_is_named_wasm=Module._ts_language_type_is_named_wasm=function(){return(_ts_language_type_is_named_wasm=Module._ts_language_type_is_named_wasm=Module.asm.ts_language_type_is_named_wasm).apply(null,arguments)},_ts_language_type_is_visible_wasm=Module._ts_language_type_is_visible_wasm=function(){return(_ts_language_type_is_visible_wasm=Module._ts_language_type_is_visible_wasm=Module.asm.ts_language_type_is_visible_wasm).apply(null,arguments)},_ts_tree_root_node_wasm=Module._ts_tree_root_node_wasm=function(){return(_ts_tree_root_node_wasm=Module._ts_tree_root_node_wasm=Module.asm.ts_tree_root_node_wasm).apply(null,arguments)},_ts_tree_edit_wasm=Module._ts_tree_edit_wasm=function(){return(_ts_tree_edit_wasm=Module._ts_tree_edit_wasm=Module.asm.ts_tree_edit_wasm).apply(null,arguments)},_ts_tree_get_changed_ranges_wasm=Module._ts_tree_get_changed_ranges_wasm=function(){return(_ts_tree_get_changed_ranges_wasm=Module._ts_tree_get_changed_ranges_wasm=Module.asm.ts_tree_get_changed_ranges_wasm).apply(null,arguments)},_ts_tree_cursor_new_wasm=Module._ts_tree_cursor_new_wasm=function(){return(_ts_tree_cursor_new_wasm=Module._ts_tree_cursor_new_wasm=Module.asm.ts_tree_cursor_new_wasm).apply(null,arguments)},_ts_tree_cursor_delete_wasm=Module._ts_tree_cursor_delete_wasm=function(){return(_ts_tree_cursor_delete_wasm=Module._ts_tree_cursor_delete_wasm=Module.asm.ts_tree_cursor_delete_wasm).apply(null,arguments)},_ts_tree_cursor_reset_wasm=Module._ts_tree_cursor_reset_wasm=function(){return(_ts_tree_cursor_reset_wasm=Module._ts_tree_cursor_reset_wasm=Module.asm.ts_tree_cursor_reset_wasm).apply(null,arguments)},_ts_tree_cursor_goto_first_child_wasm=Module._ts_tree_cursor_goto_first_child_wasm=function(){return(_ts_tree_cursor_goto_first_child_wasm=Module._ts_tree_cursor_goto_first_child_wasm=Module.asm.ts_tree_cursor_goto_first_child_wasm).apply(null,arguments)},_ts_tree_cursor_goto_next_sibling_wasm=Module._ts_tree_cursor_goto_next_sibling_wasm=function(){return(_ts_tree_cursor_goto_next_sibling_wasm=Module._ts_tree_cursor_goto_next_sibling_wasm=Module.asm.ts_tree_cursor_goto_next_sibling_wasm).apply(null,arguments)},_ts_tree_cursor_goto_parent_wasm=Module._ts_tree_cursor_goto_parent_wasm=function(){return(_ts_tree_cursor_goto_parent_wasm=Module._ts_tree_cursor_goto_parent_wasm=Module.asm.ts_tree_cursor_goto_parent_wasm).apply(null,arguments)},_ts_tree_cursor_current_node_type_id_wasm=Module._ts_tree_cursor_current_node_type_id_wasm=function(){return(_ts_tree_cursor_current_node_type_id_wasm=Module._ts_tree_cursor_current_node_type_id_wasm=Module.asm.ts_tree_cursor_current_node_type_id_wasm).apply(null,arguments)},_ts_tree_cursor_current_node_is_named_wasm=Module._ts_tree_cursor_current_node_is_named_wasm=function(){return(_ts_tree_cursor_current_node_is_named_wasm=Module._ts_tree_cursor_current_node_is_named_wasm=Module.asm.ts_tree_cursor_current_node_is_named_wasm).apply(null,arguments)},_ts_tree_cursor_current_node_is_missing_wasm=Module._ts_tree_cursor_current_node_is_missing_wasm=function(){return(_ts_tree_cursor_current_node_is_missing_wasm=Module._ts_tree_cursor_current_node_is_missing_wasm=Module.asm.ts_tree_cursor_current_node_is_missing_wasm).apply(null,arguments)},_ts_tree_cursor_current_node_id_wasm=Module._ts_tree_cursor_current_node_id_wasm=function(){return(_ts_tree_cursor_current_node_id_wasm=Module._ts_tree_cursor_current_node_id_wasm=Module.asm.ts_tree_cursor_current_node_id_wasm).apply(null,arguments)},_ts_tree_cursor_start_position_wasm=Module._ts_tree_cursor_start_position_wasm=function(){return(_ts_tree_cursor_start_position_wasm=Module._ts_tree_cursor_start_position_wasm=Module.asm.ts_tree_cursor_start_position_wasm).apply(null,arguments)},_ts_tree_cursor_end_position_wasm=Module._ts_tree_cursor_end_position_wasm=function(){return(_ts_tree_cursor_end_position_wasm=Module._ts_tree_cursor_end_position_wasm=Module.asm.ts_tree_cursor_end_position_wasm).apply(null,arguments)},_ts_tree_cursor_start_index_wasm=Module._ts_tree_cursor_start_index_wasm=function(){return(_ts_tree_cursor_start_index_wasm=Module._ts_tree_cursor_start_index_wasm=Module.asm.ts_tree_cursor_start_index_wasm).apply(null,arguments)},_ts_tree_cursor_end_index_wasm=Module._ts_tree_cursor_end_index_wasm=function(){return(_ts_tree_cursor_end_index_wasm=Module._ts_tree_cursor_end_index_wasm=Module.asm.ts_tree_cursor_end_index_wasm).apply(null,arguments)},_ts_tree_cursor_current_field_id_wasm=Module._ts_tree_cursor_current_field_id_wasm=function(){return(_ts_tree_cursor_current_field_id_wasm=Module._ts_tree_cursor_current_field_id_wasm=Module.asm.ts_tree_cursor_current_field_id_wasm).apply(null,arguments)},_ts_tree_cursor_current_node_wasm=Module._ts_tree_cursor_current_node_wasm=function(){return(_ts_tree_cursor_current_node_wasm=Module._ts_tree_cursor_current_node_wasm=Module.asm.ts_tree_cursor_current_node_wasm).apply(null,arguments)},_ts_node_symbol_wasm=Module._ts_node_symbol_wasm=function(){return(_ts_node_symbol_wasm=Module._ts_node_symbol_wasm=Module.asm.ts_node_symbol_wasm).apply(null,arguments)},_ts_node_child_count_wasm=Module._ts_node_child_count_wasm=function(){return(_ts_node_child_count_wasm=Module._ts_node_child_count_wasm=Module.asm.ts_node_child_count_wasm).apply(null,arguments)},_ts_node_named_child_count_wasm=Module._ts_node_named_child_count_wasm=function(){return(_ts_node_named_child_count_wasm=Module._ts_node_named_child_count_wasm=Module.asm.ts_node_named_child_count_wasm).apply(null,arguments)},_ts_node_child_wasm=Module._ts_node_child_wasm=function(){return(_ts_node_child_wasm=Module._ts_node_child_wasm=Module.asm.ts_node_child_wasm).apply(null,arguments)},_ts_node_named_child_wasm=Module._ts_node_named_child_wasm=function(){return(_ts_node_named_child_wasm=Module._ts_node_named_child_wasm=Module.asm.ts_node_named_child_wasm).apply(null,arguments)},_ts_node_child_by_field_id_wasm=Module._ts_node_child_by_field_id_wasm=function(){return(_ts_node_child_by_field_id_wasm=Module._ts_node_child_by_field_id_wasm=Module.asm.ts_node_child_by_field_id_wasm).apply(null,arguments)},_ts_node_next_sibling_wasm=Module._ts_node_next_sibling_wasm=function(){return(_ts_node_next_sibling_wasm=Module._ts_node_next_sibling_wasm=Module.asm.ts_node_next_sibling_wasm).apply(null,arguments)},_ts_node_prev_sibling_wasm=Module._ts_node_prev_sibling_wasm=function(){return(_ts_node_prev_sibling_wasm=Module._ts_node_prev_sibling_wasm=Module.asm.ts_node_prev_sibling_wasm).apply(null,arguments)},_ts_node_next_named_sibling_wasm=Module._ts_node_next_named_sibling_wasm=function(){return(_ts_node_next_named_sibling_wasm=Module._ts_node_next_named_sibling_wasm=Module.asm.ts_node_next_named_sibling_wasm).apply(null,arguments)},_ts_node_prev_named_sibling_wasm=Module._ts_node_prev_named_sibling_wasm=function(){return(_ts_node_prev_named_sibling_wasm=Module._ts_node_prev_named_sibling_wasm=Module.asm.ts_node_prev_named_sibling_wasm).apply(null,arguments)},_ts_node_parent_wasm=Module._ts_node_parent_wasm=function(){return(_ts_node_parent_wasm=Module._ts_node_parent_wasm=Module.asm.ts_node_parent_wasm).apply(null,arguments)},_ts_node_descendant_for_index_wasm=Module._ts_node_descendant_for_index_wasm=function(){return(_ts_node_descendant_for_index_wasm=Module._ts_node_descendant_for_index_wasm=Module.asm.ts_node_descendant_for_index_wasm).apply(null,arguments)},_ts_node_named_descendant_for_index_wasm=Module._ts_node_named_descendant_for_index_wasm=function(){return(_ts_node_named_descendant_for_index_wasm=Module._ts_node_named_descendant_for_index_wasm=Module.asm.ts_node_named_descendant_for_index_wasm).apply(null,arguments)},_ts_node_descendant_for_position_wasm=Module._ts_node_descendant_for_position_wasm=function(){return(_ts_node_descendant_for_position_wasm=Module._ts_node_descendant_for_position_wasm=Module.asm.ts_node_descendant_for_position_wasm).apply(null,arguments)},_ts_node_named_descendant_for_position_wasm=Module._ts_node_named_descendant_for_position_wasm=function(){return(_ts_node_named_descendant_for_position_wasm=Module._ts_node_named_descendant_for_position_wasm=Module.asm.ts_node_named_descendant_for_position_wasm).apply(null,arguments)},_ts_node_start_point_wasm=Module._ts_node_start_point_wasm=function(){return(_ts_node_start_point_wasm=Module._ts_node_start_point_wasm=Module.asm.ts_node_start_point_wasm).apply(null,arguments)},_ts_node_end_point_wasm=Module._ts_node_end_point_wasm=function(){return(_ts_node_end_point_wasm=Module._ts_node_end_point_wasm=Module.asm.ts_node_end_point_wasm).apply(null,arguments)},_ts_node_start_index_wasm=Module._ts_node_start_index_wasm=function(){return(_ts_node_start_index_wasm=Module._ts_node_start_index_wasm=Module.asm.ts_node_start_index_wasm).apply(null,arguments)},_ts_node_end_index_wasm=Module._ts_node_end_index_wasm=function(){return(_ts_node_end_index_wasm=Module._ts_node_end_index_wasm=Module.asm.ts_node_end_index_wasm).apply(null,arguments)},_ts_node_to_string_wasm=Module._ts_node_to_string_wasm=function(){return(_ts_node_to_string_wasm=Module._ts_node_to_string_wasm=Module.asm.ts_node_to_string_wasm).apply(null,arguments)},_ts_node_children_wasm=Module._ts_node_children_wasm=function(){return(_ts_node_children_wasm=Module._ts_node_children_wasm=Module.asm.ts_node_children_wasm).apply(null,arguments)},_ts_node_named_children_wasm=Module._ts_node_named_children_wasm=function(){return(_ts_node_named_children_wasm=Module._ts_node_named_children_wasm=Module.asm.ts_node_named_children_wasm).apply(null,arguments)},_ts_node_descendants_of_type_wasm=Module._ts_node_descendants_of_type_wasm=function(){return(_ts_node_descendants_of_type_wasm=Module._ts_node_descendants_of_type_wasm=Module.asm.ts_node_descendants_of_type_wasm).apply(null,arguments)},_ts_node_is_named_wasm=Module._ts_node_is_named_wasm=function(){return(_ts_node_is_named_wasm=Module._ts_node_is_named_wasm=Module.asm.ts_node_is_named_wasm).apply(null,arguments)},_ts_node_has_changes_wasm=Module._ts_node_has_changes_wasm=function(){return(_ts_node_has_changes_wasm=Module._ts_node_has_changes_wasm=Module.asm.ts_node_has_changes_wasm).apply(null,arguments)},_ts_node_has_error_wasm=Module._ts_node_has_error_wasm=function(){return(_ts_node_has_error_wasm=Module._ts_node_has_error_wasm=Module.asm.ts_node_has_error_wasm).apply(null,arguments)},_ts_node_is_missing_wasm=Module._ts_node_is_missing_wasm=function(){return(_ts_node_is_missing_wasm=Module._ts_node_is_missing_wasm=Module.asm.ts_node_is_missing_wasm).apply(null,arguments)},_ts_query_matches_wasm=Module._ts_query_matches_wasm=function(){return(_ts_query_matches_wasm=Module._ts_query_matches_wasm=Module.asm.ts_query_matches_wasm).apply(null,arguments)},_ts_query_captures_wasm=Module._ts_query_captures_wasm=function(){return(_ts_query_captures_wasm=Module._ts_query_captures_wasm=Module.asm.ts_query_captures_wasm).apply(null,arguments)},___cxa_atexit=Module.___cxa_atexit=function(){return(___cxa_atexit=Module.___cxa_atexit=Module.asm.__cxa_atexit).apply(null,arguments)},_iswdigit=Module._iswdigit=function(){return(_iswdigit=Module._iswdigit=Module.asm.iswdigit).apply(null,arguments)},_iswalpha=Module._iswalpha=function(){return(_iswalpha=Module._iswalpha=Module.asm.iswalpha).apply(null,arguments)},_iswlower=Module._iswlower=function(){return(_iswlower=Module._iswlower=Module.asm.iswlower).apply(null,arguments)},_memchr=Module._memchr=function(){return(_memchr=Module._memchr=Module.asm.memchr).apply(null,arguments)},_strlen=Module._strlen=function(){return(_strlen=Module._strlen=Module.asm.strlen).apply(null,arguments)},_towupper=Module._towupper=function(){return(_towupper=Module._towupper=Module.asm.towupper).apply(null,arguments)},_setThrew=Module._setThrew=function(){return(_setThrew=Module._setThrew=Module.asm.setThrew).apply(null,arguments)},stackSave=Module.stackSave=function(){return(stackSave=Module.stackSave=Module.asm.stackSave).apply(null,arguments)},stackRestore=Module.stackRestore=function(){return(stackRestore=Module.stackRestore=Module.asm.stackRestore).apply(null,arguments)},stackAlloc=Module.stackAlloc=function(){return(stackAlloc=Module.stackAlloc=Module.asm.stackAlloc).apply(null,arguments)},__Znwm=Module.__Znwm=function(){return(__Znwm=Module.__Znwm=Module.asm._Znwm).apply(null,arguments)},__ZdlPv=Module.__ZdlPv=function(){return(__ZdlPv=Module.__ZdlPv=Module.asm._ZdlPv).apply(null,arguments)},__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEED2Ev=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEED2Ev=function(){return(__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEED2Ev=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEED2Ev=Module.asm._ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEED2Ev).apply(null,arguments)},__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9__grow_byEmmmmmm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9__grow_byEmmmmmm=function(){return(__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9__grow_byEmmmmmm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9__grow_byEmmmmmm=Module.asm._ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9__grow_byEmmmmmm).apply(null,arguments)},__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE6__initEPKcm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE6__initEPKcm=function(){return(__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE6__initEPKcm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE6__initEPKcm=Module.asm._ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE6__initEPKcm).apply(null,arguments)},__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE7reserveEm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE7reserveEm=function(){return(__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE7reserveEm=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE7reserveEm=Module.asm._ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE7reserveEm).apply(null,arguments)},__ZNKSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE4copyEPcmm=Module.__ZNKSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE4copyEPcmm=function(){return(__ZNKSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE4copyEPcmm=Module.__ZNKSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE4copyEPcmm=Module.asm._ZNKSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE4copyEPcmm).apply(null,arguments)},__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9push_backEc=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9push_backEc=function(){return(__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9push_backEc=Module.__ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9push_backEc=Module.asm._ZNSt3__212basic_stringIcNS_11char_traitsIcEENS_9allocatorIcEEE9push_backEc).apply(null,arguments)},__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEED2Ev=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEED2Ev=function(){return(__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEED2Ev=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEED2Ev=Module.asm._ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEED2Ev).apply(null,arguments)},__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE9push_backEw=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE9push_backEw=function(){return(__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE9push_backEw=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE9push_backEw=Module.asm._ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE9push_backEw).apply(null,arguments)},__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE6resizeEmw=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE6resizeEmw=function(){return(__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE6resizeEmw=Module.__ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE6resizeEmw=Module.asm._ZNSt3__212basic_stringIwNS_11char_traitsIwEENS_9allocatorIwEEE6resizeEmw).apply(null,arguments)},dynCall_jiji=Module.dynCall_jiji=function(){return(dynCall_jiji=Module.dynCall_jiji=Module.asm.dynCall_jiji).apply(null,arguments)},_orig$ts_parser_timeout_micros=Module._orig$ts_parser_timeout_micros=function(){return(_orig$ts_parser_timeout_micros=Module._orig$ts_parser_timeout_micros=Module.asm.orig$ts_parser_timeout_micros).apply(null,arguments)},_orig$ts_parser_set_timeout_micros=Module._orig$ts_parser_set_timeout_micros=function(){return(_orig$ts_parser_set_timeout_micros=Module._orig$ts_parser_set_timeout_micros=Module.asm.orig$ts_parser_set_timeout_micros).apply(null,arguments)},calledRun;function callMain(t){var e=Module._main;if(e){(t=t||[]).unshift(thisProgram);var n=t.length,r=stackAlloc(4*(n+1)),o=r>>2;t.forEach(u=>{HEAP32[o++]=allocateUTF8OnStack(u)}),HEAP32[o]=0;try{var s=e(n,r);return exitJS(s,!0),s}catch(u){return handleException(u)}}}i(callMain,"callMain"),Module.AsciiToString=AsciiToString,Module.stringToUTF16=stringToUTF16,dependenciesFulfilled=i(function t(){calledRun||run(),calledRun||(dependenciesFulfilled=t)},"e");var dylibsLoaded=!1;function run(t){function e(){calledRun||(calledRun=!0,Module.calledRun=!0,ABORT||(initRuntime(),preMain(),Module.onRuntimeInitialized&&Module.onRuntimeInitialized(),shouldRunNow&&callMain(t),postRun()))}i(e,"t"),t=t||arguments_,runDependencies>0||!dylibsLoaded&&(preloadDylibs(),dylibsLoaded=!0,runDependencies>0)||(preRun(),runDependencies>0||(Module.setStatus?(Module.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Module.setStatus("")},1),e()},1)):e()))}if(i(run,"run"),Module.preInit)for(typeof Module.preInit=="function"&&(Module.preInit=[Module.preInit]);Module.preInit.length>0;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),run();let C=Module,INTERNAL={},SIZE_OF_INT=4,SIZE_OF_NODE=5*SIZE_OF_INT,SIZE_OF_POINT=2*SIZE_OF_INT,SIZE_OF_RANGE=2*SIZE_OF_INT+2*SIZE_OF_POINT,ZERO_POINT={row:0,column:0},QUERY_WORD_REGEX=/[\w-.]*/g,PREDICATE_STEP_TYPE_CAPTURE=1,PREDICATE_STEP_TYPE_STRING=2,LANGUAGE_FUNCTION_REGEX=/^_?tree_sitter_\w+/;var VERSION,MIN_COMPATIBLE_VERSION,TRANSFER_BUFFER,currentParseCallback,currentLogCallback;class ParserImpl{static{i(this,"ParserImpl")}static init(){TRANSFER_BUFFER=C._ts_init(),VERSION=getValue(TRANSFER_BUFFER,"i32"),MIN_COMPATIBLE_VERSION=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32")}initialize(){C._ts_parser_new_wasm(),this[0]=getValue(TRANSFER_BUFFER,"i32"),this[1]=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32")}delete(){C._ts_parser_delete(this[0]),C._free(this[1]),this[0]=0,this[1]=0}setLanguage(e){let n;if(e){if(e.constructor!==Language)throw new Error("Argument must be a Language");{n=e[0];let r=C._ts_language_version(n);if(r<MIN_COMPATIBLE_VERSION||VERSION<r)throw new Error(`Incompatible language version ${r}. Compatibility range ${MIN_COMPATIBLE_VERSION} through ${VERSION}.`)}}else n=0,e=null;return this.language=e,C._ts_parser_set_language(this[0],n),this}getLanguage(){return this.language}parse(e,n,r){if(typeof e=="string")currentParseCallback=i((f,_,v)=>e.slice(f,v),"currentParseCallback");else{if(typeof e!="function")throw new Error("Argument must be a string or a function");currentParseCallback=e}this.logCallback?(currentLogCallback=this.logCallback,C._ts_parser_enable_logger_wasm(this[0],1)):(currentLogCallback=null,C._ts_parser_enable_logger_wasm(this[0],0));let o=0,s=0;if(r&&r.includedRanges){o=r.includedRanges.length,s=C._calloc(o,SIZE_OF_RANGE);let f=s;for(let _=0;_<o;_++)marshalRange(f,r.includedRanges[_]),f+=SIZE_OF_RANGE}let u=C._ts_parser_parse_wasm(this[0],this[1],n?n[0]:0,s,o);if(!u)throw currentParseCallback=null,currentLogCallback=null,new Error("Parsing failed");let l=new Tree(INTERNAL,u,this.language,currentParseCallback);return currentParseCallback=null,currentLogCallback=null,l}reset(){C._ts_parser_reset(this[0])}setTimeoutMicros(e){C._ts_parser_set_timeout_micros(this[0],e)}getTimeoutMicros(){return C._ts_parser_timeout_micros(this[0])}setLogger(e){if(e){if(typeof e!="function")throw new Error("Logger callback must be a function")}else e=null;return this.logCallback=e,this}getLogger(){return this.logCallback}}class Tree{static{i(this,"Tree")}constructor(e,n,r,o){assertInternal(e),this[0]=n,this.language=r,this.textCallback=o}copy(){let e=C._ts_tree_copy(this[0]);return new Tree(INTERNAL,e,this.language,this.textCallback)}delete(){C._ts_tree_delete(this[0]),this[0]=0}edit(e){marshalEdit(e),C._ts_tree_edit_wasm(this[0])}get rootNode(){return C._ts_tree_root_node_wasm(this[0]),unmarshalNode(this)}getLanguage(){return this.language}walk(){return this.rootNode.walk()}getChangedRanges(e){if(e.constructor!==Tree)throw new TypeError("Argument must be a Tree");C._ts_tree_get_changed_ranges_wasm(this[0],e[0]);let n=getValue(TRANSFER_BUFFER,"i32"),r=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),o=new Array(n);if(n>0){let s=r;for(let u=0;u<n;u++)o[u]=unmarshalRange(s),s+=SIZE_OF_RANGE;C._free(r)}return o}}class Node{static{i(this,"Node")}constructor(e,n){assertInternal(e),this.tree=n}get typeId(){return marshalNode(this),C._ts_node_symbol_wasm(this.tree[0])}get type(){return this.tree.language.types[this.typeId]||"ERROR"}get endPosition(){return marshalNode(this),C._ts_node_end_point_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get endIndex(){return marshalNode(this),C._ts_node_end_index_wasm(this.tree[0])}get text(){return getText(this.tree,this.startIndex,this.endIndex)}isNamed(){return marshalNode(this),C._ts_node_is_named_wasm(this.tree[0])===1}hasError(){return marshalNode(this),C._ts_node_has_error_wasm(this.tree[0])===1}hasChanges(){return marshalNode(this),C._ts_node_has_changes_wasm(this.tree[0])===1}isMissing(){return marshalNode(this),C._ts_node_is_missing_wasm(this.tree[0])===1}equals(e){return this.id===e.id}child(e){return marshalNode(this),C._ts_node_child_wasm(this.tree[0],e),unmarshalNode(this.tree)}namedChild(e){return marshalNode(this),C._ts_node_named_child_wasm(this.tree[0],e),unmarshalNode(this.tree)}childForFieldId(e){return marshalNode(this),C._ts_node_child_by_field_id_wasm(this.tree[0],e),unmarshalNode(this.tree)}childForFieldName(e){let n=this.tree.language.fields.indexOf(e);if(n!==-1)return this.childForFieldId(n)}get childCount(){return marshalNode(this),C._ts_node_child_count_wasm(this.tree[0])}get namedChildCount(){return marshalNode(this),C._ts_node_named_child_count_wasm(this.tree[0])}get firstChild(){return this.child(0)}get firstNamedChild(){return this.namedChild(0)}get lastChild(){return this.child(this.childCount-1)}get lastNamedChild(){return this.namedChild(this.namedChildCount-1)}get children(){if(!this._children){marshalNode(this),C._ts_node_children_wasm(this.tree[0]);let e=getValue(TRANSFER_BUFFER,"i32"),n=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32");if(this._children=new Array(e),e>0){let r=n;for(let o=0;o<e;o++)this._children[o]=unmarshalNode(this.tree,r),r+=SIZE_OF_NODE;C._free(n)}}return this._children}get namedChildren(){if(!this._namedChildren){marshalNode(this),C._ts_node_named_children_wasm(this.tree[0]);let e=getValue(TRANSFER_BUFFER,"i32"),n=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32");if(this._namedChildren=new Array(e),e>0){let r=n;for(let o=0;o<e;o++)this._namedChildren[o]=unmarshalNode(this.tree,r),r+=SIZE_OF_NODE;C._free(n)}}return this._namedChildren}descendantsOfType(e,n,r){Array.isArray(e)||(e=[e]),n||(n=ZERO_POINT),r||(r=ZERO_POINT);let o=[],s=this.tree.language.types;for(let v=0,T=s.length;v<T;v++)e.includes(s[v])&&o.push(v);let u=C._malloc(SIZE_OF_INT*o.length);for(let v=0,T=o.length;v<T;v++)setValue(u+v*SIZE_OF_INT,o[v],"i32");marshalNode(this),C._ts_node_descendants_of_type_wasm(this.tree[0],u,o.length,n.row,n.column,r.row,r.column);let l=getValue(TRANSFER_BUFFER,"i32"),f=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),_=new Array(l);if(l>0){let v=f;for(let T=0;T<l;T++)_[T]=unmarshalNode(this.tree,v),v+=SIZE_OF_NODE}return C._free(f),C._free(u),_}get nextSibling(){return marshalNode(this),C._ts_node_next_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get previousSibling(){return marshalNode(this),C._ts_node_prev_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get nextNamedSibling(){return marshalNode(this),C._ts_node_next_named_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get previousNamedSibling(){return marshalNode(this),C._ts_node_prev_named_sibling_wasm(this.tree[0]),unmarshalNode(this.tree)}get parent(){return marshalNode(this),C._ts_node_parent_wasm(this.tree[0]),unmarshalNode(this.tree)}descendantForIndex(e,n=e){if(typeof e!="number"||typeof n!="number")throw new Error("Arguments must be numbers");marshalNode(this);let r=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(r,e,"i32"),setValue(r+SIZE_OF_INT,n,"i32"),C._ts_node_descendant_for_index_wasm(this.tree[0]),unmarshalNode(this.tree)}namedDescendantForIndex(e,n=e){if(typeof e!="number"||typeof n!="number")throw new Error("Arguments must be numbers");marshalNode(this);let r=TRANSFER_BUFFER+SIZE_OF_NODE;return setValue(r,e,"i32"),setValue(r+SIZE_OF_INT,n,"i32"),C._ts_node_named_descendant_for_index_wasm(this.tree[0]),unmarshalNode(this.tree)}descendantForPosition(e,n=e){if(!isPoint(e)||!isPoint(n))throw new Error("Arguments must be {row, column} objects");marshalNode(this);let r=TRANSFER_BUFFER+SIZE_OF_NODE;return marshalPoint(r,e),marshalPoint(r+SIZE_OF_POINT,n),C._ts_node_descendant_for_position_wasm(this.tree[0]),unmarshalNode(this.tree)}namedDescendantForPosition(e,n=e){if(!isPoint(e)||!isPoint(n))throw new Error("Arguments must be {row, column} objects");marshalNode(this);let r=TRANSFER_BUFFER+SIZE_OF_NODE;return marshalPoint(r,e),marshalPoint(r+SIZE_OF_POINT,n),C._ts_node_named_descendant_for_position_wasm(this.tree[0]),unmarshalNode(this.tree)}walk(){return marshalNode(this),C._ts_tree_cursor_new_wasm(this.tree[0]),new TreeCursor(INTERNAL,this.tree)}toString(){marshalNode(this);let e=C._ts_node_to_string_wasm(this.tree[0]),n=AsciiToString(e);return C._free(e),n}}class TreeCursor{static{i(this,"TreeCursor")}constructor(e,n){assertInternal(e),this.tree=n,unmarshalTreeCursor(this)}delete(){marshalTreeCursor(this),C._ts_tree_cursor_delete_wasm(this.tree[0]),this[0]=this[1]=this[2]=0}reset(e){marshalNode(e),marshalTreeCursor(this,TRANSFER_BUFFER+SIZE_OF_NODE),C._ts_tree_cursor_reset_wasm(this.tree[0]),unmarshalTreeCursor(this)}get nodeType(){return this.tree.language.types[this.nodeTypeId]||"ERROR"}get nodeTypeId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_type_id_wasm(this.tree[0])}get nodeId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_id_wasm(this.tree[0])}get nodeIsNamed(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_is_named_wasm(this.tree[0])===1}get nodeIsMissing(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_is_missing_wasm(this.tree[0])===1}get nodeText(){marshalTreeCursor(this);let e=C._ts_tree_cursor_start_index_wasm(this.tree[0]),n=C._ts_tree_cursor_end_index_wasm(this.tree[0]);return getText(this.tree,e,n)}get startPosition(){return marshalTreeCursor(this),C._ts_tree_cursor_start_position_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get endPosition(){return marshalTreeCursor(this),C._ts_tree_cursor_end_position_wasm(this.tree[0]),unmarshalPoint(TRANSFER_BUFFER)}get startIndex(){return marshalTreeCursor(this),C._ts_tree_cursor_start_index_wasm(this.tree[0])}get endIndex(){return marshalTreeCursor(this),C._ts_tree_cursor_end_index_wasm(this.tree[0])}currentNode(){return marshalTreeCursor(this),C._ts_tree_cursor_current_node_wasm(this.tree[0]),unmarshalNode(this.tree)}currentFieldId(){return marshalTreeCursor(this),C._ts_tree_cursor_current_field_id_wasm(this.tree[0])}currentFieldName(){return this.tree.language.fields[this.currentFieldId()]}gotoFirstChild(){marshalTreeCursor(this);let e=C._ts_tree_cursor_goto_first_child_wasm(this.tree[0]);return unmarshalTreeCursor(this),e===1}gotoNextSibling(){marshalTreeCursor(this);let e=C._ts_tree_cursor_goto_next_sibling_wasm(this.tree[0]);return unmarshalTreeCursor(this),e===1}gotoParent(){marshalTreeCursor(this);let e=C._ts_tree_cursor_goto_parent_wasm(this.tree[0]);return unmarshalTreeCursor(this),e===1}}class Language{static{i(this,"Language")}constructor(e,n){assertInternal(e),this[0]=n,this.types=new Array(C._ts_language_symbol_count(this[0]));for(let r=0,o=this.types.length;r<o;r++)C._ts_language_symbol_type(this[0],r)<2&&(this.types[r]=UTF8ToString(C._ts_language_symbol_name(this[0],r)));this.fields=new Array(C._ts_language_field_count(this[0])+1);for(let r=0,o=this.fields.length;r<o;r++){let s=C._ts_language_field_name_for_id(this[0],r);this.fields[r]=s!==0?UTF8ToString(s):null}}get version(){return C._ts_language_version(this[0])}get fieldCount(){return this.fields.length-1}fieldIdForName(e){let n=this.fields.indexOf(e);return n!==-1?n:null}fieldNameForId(e){return this.fields[e]||null}idForNodeType(e,n){let r=lengthBytesUTF8(e),o=C._malloc(r+1);stringToUTF8(e,o,r+1);let s=C._ts_language_symbol_for_name(this[0],o,r,n);return C._free(o),s||null}get nodeTypeCount(){return C._ts_language_symbol_count(this[0])}nodeTypeForId(e){let n=C._ts_language_symbol_name(this[0],e);return n?UTF8ToString(n):null}nodeTypeIsNamed(e){return!!C._ts_language_type_is_named_wasm(this[0],e)}nodeTypeIsVisible(e){return!!C._ts_language_type_is_visible_wasm(this[0],e)}query(e){let n=lengthBytesUTF8(e),r=C._malloc(n+1);stringToUTF8(e,r,n+1);let o=C._ts_query_new(this[0],r,n,TRANSFER_BUFFER,TRANSFER_BUFFER+SIZE_OF_INT);if(!o){let F=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),P=UTF8ToString(r,getValue(TRANSFER_BUFFER,"i32")).length,z=e.substr(P,100).split(`
`)[0],L,H=z.match(QUERY_WORD_REGEX)[0];switch(F){case 2:L=new RangeError(`Bad node name '${H}'`);break;case 3:L=new RangeError(`Bad field name '${H}'`);break;case 4:L=new RangeError(`Bad capture name @${H}`);break;case 5:L=new TypeError(`Bad pattern structure at offset ${P}: '${z}'...`),H="";break;default:L=new SyntaxError(`Bad syntax at offset ${P}: '${z}'...`),H=""}throw L.index=P,L.length=H.length,C._free(r),L}let s=C._ts_query_string_count(o),u=C._ts_query_capture_count(o),l=C._ts_query_pattern_count(o),f=new Array(u),_=new Array(s);for(let F=0;F<u;F++){let P=C._ts_query_capture_name_for_id(o,F,TRANSFER_BUFFER),z=getValue(TRANSFER_BUFFER,"i32");f[F]=UTF8ToString(P,z)}for(let F=0;F<s;F++){let P=C._ts_query_string_value_for_id(o,F,TRANSFER_BUFFER),z=getValue(TRANSFER_BUFFER,"i32");_[F]=UTF8ToString(P,z)}let v=new Array(l),T=new Array(l),M=new Array(l),D=new Array(l),W=new Array(l);for(let F=0;F<l;F++){let P=C._ts_query_predicates_for_pattern(o,F,TRANSFER_BUFFER),z=getValue(TRANSFER_BUFFER,"i32");D[F]=[],W[F]=[];let L=[],H=P;for(let Y=0;Y<z;Y++){let K=getValue(H,"i32");H+=SIZE_OF_INT;let le=getValue(H,"i32");if(H+=SIZE_OF_INT,K===PREDICATE_STEP_TYPE_CAPTURE)L.push({type:"capture",name:f[le]});else if(K===PREDICATE_STEP_TYPE_STRING)L.push({type:"string",value:_[le]});else if(L.length>0){if(L[0].type!=="string")throw new Error("Predicates must begin with a literal value");let J=L[0].value,ie=!0;switch(J){case"not-eq?":ie=!1;case"eq?":if(L.length!==3)throw new Error("Wrong number of arguments to `#eq?` predicate. Expected 2, got "+(L.length-1));if(L[1].type!=="capture")throw new Error(`First argument of \`#eq?\` predicate must be a capture. Got "${L[1].value}"`);if(L[2].type==="capture"){let E=L[1].name,j=L[2].name;W[F].push(function(B){let S,I;for(let k of B)k.name===E&&(S=k.node),k.name===j&&(I=k.node);return S===void 0||I===void 0||S.text===I.text===ie})}else{let E=L[1].name,j=L[2].value;W[F].push(function(B){for(let S of B)if(S.name===E)return S.node.text===j===ie;return!0})}break;case"not-match?":ie=!1;case"match?":if(L.length!==3)throw new Error(`Wrong number of arguments to \`#match?\` predicate. Expected 2, got ${L.length-1}.`);if(L[1].type!=="capture")throw new Error(`First argument of \`#match?\` predicate must be a capture. Got "${L[1].value}".`);if(L[2].type!=="string")throw new Error(`Second argument of \`#match?\` predicate must be a string. Got @${L[2].value}.`);let Te=L[1].name,Ce=new RegExp(L[2].value);W[F].push(function(E){for(let j of E)if(j.name===Te)return Ce.test(j.node.text)===ie;return!0});break;case"set!":if(L.length<2||L.length>3)throw new Error(`Wrong number of arguments to \`#set!\` predicate. Expected 1 or 2. Got ${L.length-1}.`);if(L.some(E=>E.type!=="string"))throw new Error('Arguments to `#set!` predicate must be a strings.".');v[F]||(v[F]={}),v[F][L[1].value]=L[2]?L[2].value:null;break;case"is?":case"is-not?":if(L.length<2||L.length>3)throw new Error(`Wrong number of arguments to \`#${J}\` predicate. Expected 1 or 2. Got ${L.length-1}.`);if(L.some(E=>E.type!=="string"))throw new Error(`Arguments to \`#${J}\` predicate must be a strings.".`);let A=J==="is?"?T:M;A[F]||(A[F]={}),A[F][L[1].value]=L[2]?L[2].value:null;break;default:D[F].push({operator:J,operands:L.slice(1)})}L.length=0}}Object.freeze(v[F]),Object.freeze(T[F]),Object.freeze(M[F])}return C._free(r),new Query(INTERNAL,o,f,W,D,Object.freeze(v),Object.freeze(T),Object.freeze(M))}static load(e){let n;if(e instanceof Uint8Array)n=Promise.resolve(e);else{let o=e;if(typeof process<"u"&&process.versions&&process.versions.node){let s=require("fs");n=Promise.resolve(s.readFileSync(o))}else n=fetch(o).then(s=>s.arrayBuffer().then(u=>{if(s.ok)return new Uint8Array(u);{let l=new TextDecoder("utf-8").decode(u);throw new Error(`Language.load failed with status ${s.status}.

${l}`)}}))}let r=typeof loadSideModule=="function"?loadSideModule:loadWebAssemblyModule;return n.then(o=>r(o,{loadAsync:!0})).then(o=>{let s=Object.keys(o),u=s.find(f=>LANGUAGE_FUNCTION_REGEX.test(f)&&!f.includes("external_scanner_"));u||console.log(`Couldn't find language function in WASM file. Symbols:
${JSON.stringify(s,null,2)}`);let l=o[u]();return new Language(INTERNAL,l)})}}class Query{static{i(this,"Query")}constructor(e,n,r,o,s,u,l,f){assertInternal(e),this[0]=n,this.captureNames=r,this.textPredicates=o,this.predicates=s,this.setProperties=u,this.assertedProperties=l,this.refutedProperties=f,this.exceededMatchLimit=!1}delete(){C._ts_query_delete(this[0]),this[0]=0}matches(e,n,r,o){n||(n=ZERO_POINT),r||(r=ZERO_POINT),o||(o={});let s=o.matchLimit;if(s===void 0)s=0;else if(typeof s!="number")throw new Error("Arguments must be numbers");marshalNode(e),C._ts_query_matches_wasm(this[0],e.tree[0],n.row,n.column,r.row,r.column,s);let u=getValue(TRANSFER_BUFFER,"i32"),l=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),f=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32"),_=new Array(u);this.exceededMatchLimit=!!f;let v=0,T=l;for(let M=0;M<u;M++){let D=getValue(T,"i32");T+=SIZE_OF_INT;let W=getValue(T,"i32");T+=SIZE_OF_INT;let F=new Array(W);if(T=unmarshalCaptures(this,e.tree,T,F),this.textPredicates[D].every(P=>P(F))){_[v++]={pattern:D,captures:F};let P=this.setProperties[D];P&&(_[M].setProperties=P);let z=this.assertedProperties[D];z&&(_[M].assertedProperties=z);let L=this.refutedProperties[D];L&&(_[M].refutedProperties=L)}}return _.length=v,C._free(l),_}captures(e,n,r,o){n||(n=ZERO_POINT),r||(r=ZERO_POINT),o||(o={});let s=o.matchLimit;if(s===void 0)s=0;else if(typeof s!="number")throw new Error("Arguments must be numbers");marshalNode(e),C._ts_query_captures_wasm(this[0],e.tree[0],n.row,n.column,r.row,r.column,s);let u=getValue(TRANSFER_BUFFER,"i32"),l=getValue(TRANSFER_BUFFER+SIZE_OF_INT,"i32"),f=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32"),_=[];this.exceededMatchLimit=!!f;let v=[],T=l;for(let M=0;M<u;M++){let D=getValue(T,"i32");T+=SIZE_OF_INT;let W=getValue(T,"i32");T+=SIZE_OF_INT;let F=getValue(T,"i32");if(T+=SIZE_OF_INT,v.length=W,T=unmarshalCaptures(this,e.tree,T,v),this.textPredicates[D].every(P=>P(v))){let P=v[F],z=this.setProperties[D];z&&(P.setProperties=z);let L=this.assertedProperties[D];L&&(P.assertedProperties=L);let H=this.refutedProperties[D];H&&(P.refutedProperties=H),_.push(P)}}return C._free(l),_}predicatesForPattern(e){return this.predicates[e]}didExceedMatchLimit(){return this.exceededMatchLimit}}function getText(t,e,n){let r=n-e,o=t.textCallback(e,null,n);for(e+=o.length;e<n;){let s=t.textCallback(e,null,n);if(!(s&&s.length>0))break;e+=s.length,o+=s}return e>n&&(o=o.slice(0,r)),o}i(getText,"getText");function unmarshalCaptures(t,e,n,r){for(let o=0,s=r.length;o<s;o++){let u=getValue(n,"i32"),l=unmarshalNode(e,n+=SIZE_OF_INT);n+=SIZE_OF_NODE,r[o]={name:t.captureNames[u],node:l}}return n}i(unmarshalCaptures,"unmarshalCaptures");function assertInternal(t){if(t!==INTERNAL)throw new Error("Illegal constructor")}i(assertInternal,"assertInternal");function isPoint(t){return t&&typeof t.row=="number"&&typeof t.column=="number"}i(isPoint,"isPoint");function marshalNode(t){let e=TRANSFER_BUFFER;setValue(e,t.id,"i32"),e+=SIZE_OF_INT,setValue(e,t.startIndex,"i32"),e+=SIZE_OF_INT,setValue(e,t.startPosition.row,"i32"),e+=SIZE_OF_INT,setValue(e,t.startPosition.column,"i32"),e+=SIZE_OF_INT,setValue(e,t[0],"i32")}i(marshalNode,"marshalNode");function unmarshalNode(t,e=TRANSFER_BUFFER){let n=getValue(e,"i32");if(n===0)return null;let r=getValue(e+=SIZE_OF_INT,"i32"),o=getValue(e+=SIZE_OF_INT,"i32"),s=getValue(e+=SIZE_OF_INT,"i32"),u=getValue(e+=SIZE_OF_INT,"i32"),l=new Node(INTERNAL,t);return l.id=n,l.startIndex=r,l.startPosition={row:o,column:s},l[0]=u,l}i(unmarshalNode,"unmarshalNode");function marshalTreeCursor(t,e=TRANSFER_BUFFER){setValue(e+0*SIZE_OF_INT,t[0],"i32"),setValue(e+1*SIZE_OF_INT,t[1],"i32"),setValue(e+2*SIZE_OF_INT,t[2],"i32")}i(marshalTreeCursor,"marshalTreeCursor");function unmarshalTreeCursor(t){t[0]=getValue(TRANSFER_BUFFER+0*SIZE_OF_INT,"i32"),t[1]=getValue(TRANSFER_BUFFER+1*SIZE_OF_INT,"i32"),t[2]=getValue(TRANSFER_BUFFER+2*SIZE_OF_INT,"i32")}i(unmarshalTreeCursor,"unmarshalTreeCursor");function marshalPoint(t,e){setValue(t,e.row,"i32"),setValue(t+SIZE_OF_INT,e.column,"i32")}i(marshalPoint,"marshalPoint");function unmarshalPoint(t){return{row:getValue(t,"i32"),column:getValue(t+SIZE_OF_INT,"i32")}}i(unmarshalPoint,"unmarshalPoint");function marshalRange(t,e){marshalPoint(t,e.startPosition),marshalPoint(t+=SIZE_OF_POINT,e.endPosition),setValue(t+=SIZE_OF_POINT,e.startIndex,"i32"),setValue(t+=SIZE_OF_INT,e.endIndex,"i32"),t+=SIZE_OF_INT}i(marshalRange,"marshalRange");function unmarshalRange(t){let e={};return e.startPosition=unmarshalPoint(t),t+=SIZE_OF_POINT,e.endPosition=unmarshalPoint(t),t+=SIZE_OF_POINT,e.startIndex=getValue(t,"i32"),t+=SIZE_OF_INT,e.endIndex=getValue(t,"i32"),e}i(unmarshalRange,"unmarshalRange");function marshalEdit(t){let e=TRANSFER_BUFFER;marshalPoint(e,t.startPosition),e+=SIZE_OF_POINT,marshalPoint(e,t.oldEndPosition),e+=SIZE_OF_POINT,marshalPoint(e,t.newEndPosition),e+=SIZE_OF_POINT,setValue(e,t.startIndex,"i32"),e+=SIZE_OF_INT,setValue(e,t.oldEndIndex,"i32"),e+=SIZE_OF_INT,setValue(e,t.newEndIndex,"i32"),e+=SIZE_OF_INT}i(marshalEdit,"marshalEdit");for(let t of Object.getOwnPropertyNames(ParserImpl.prototype))Object.defineProperty(Parser.prototype,t,{value:ParserImpl.prototype[t],enumerable:!1,writable:!1});Parser.Language=Language,Module.onRuntimeInitialized=()=>{ParserImpl.init(),resolveInitPromise()}}))}}return Parser}();typeof exports=="object"&&(module.exports=TreeSitter)});var Wa=Z(pt=>{"use strict";y();Object.defineProperty(pt,"__esModule",{value:!0});pt.bytePairEncode=pt.BinaryMap=pt.binaryMapKey=void 0;var Vf=i((t,e,n)=>{let r=n-e,o=16777215>>>Math.max(0,(3-r)*8),s=(t[e+0]|t[e+1]<<8|t[e+2]<<16)&o,u=16777215>>>Math.min(31,Math.max(0,(6-r)*8)),l=(t[e+3]|t[e+4]<<8|t[e+5]<<16)&u;return s+16777216*l},"binaryMapKey");pt.binaryMapKey=Vf;var lo=class t{static{i(this,"BinaryMap")}constructor(){this.nested=new Map,this.final=new Map}get(e,n=0,r=e.length){let o=r<6+n,s=(0,pt.binaryMapKey)(e,n,r);return o?this.final.get(s):this.nested.get(s)?.get(e,6+n,r)}set(e,n){let r=(0,pt.binaryMapKey)(e,0,e.length);if(e.length<6){this.final.set(r,n);return}let s=this.nested.get(r);if(s instanceof t)s.set(e.subarray(6),n);else{let u=new t;u.set(e.subarray(6),n),this.nested.set(r,u)}}};pt.BinaryMap=lo;var gt=new Int32Array(128),Be=new Int32Array(128);function Gf(t,e,n){if(n===1)return[e.get(t)];let r=2147483647,o=-1;for(;gt.length<n*2;)Be=new Int32Array(Be.length*2),gt=new Int32Array(gt.length*2);for(let f=0;f<n-1;f++){let _=e.get(t,f,f+2)??2147483647;_<r&&(r=_,o=f),Be[f]=f,gt[f]=_}Be[n-1]=n-1,gt[n-1]=2147483647,Be[n]=n,gt[n]=2147483647;let s=n+1;function u(f,_=0){if(f+_+2<s){let v=e.get(t,Be[f],Be[f+_+2]);if(v!==void 0)return v}return 2147483647}for(i(u,"getRank");r!==2147483647;){gt[Be[o]]=u(o,1),o>0&&(gt[Be[o-1]]=u(o-1,1));for(let f=o+1;f<s-1;f++)Be[f]=Be[f+1];s--,o=-1,r=2147483647;for(let f=0;f<s-1;f++){let _=gt[Be[f]];gt[Be[f]]<r&&(r=_,o=f)}}let l=[];for(let f=0;f<s-1;f++)l.push(e.get(t,Be[f],Be[f+1]));return l}i(Gf,"bytePairEncode");pt.bytePairEncode=Gf});var Ba=Z(Ur=>{"use strict";y();Object.defineProperty(Ur,"__esModule",{value:!0});Ur.makeTextEncoder=void 0;var co=class{static{i(this,"UniversalTextEncoder")}constructor(){this.length=0,this.encoder=new TextEncoder}encode(e){let n=this.encoder.encode(e);return this.length=n.length,n}},fo=class{static{i(this,"NodeTextEncoder")}constructor(){this.buffer=Buffer.alloc(256),this.length=0}encode(e){for(;;){if(this.length=this.buffer.write(e,"utf8"),this.length<this.buffer.length-4)return this.buffer;this.buffer=Buffer.alloc(this.length*2),this.length=this.buffer.write(e)}}},Zf=i(()=>typeof Buffer<"u"?new fo:new co,"makeTextEncoder");Ur.makeTextEncoder=Zf});var $a=Z(Wr=>{"use strict";y();Object.defineProperty(Wr,"__esModule",{value:!0});Wr.LRUCache=void 0;var mo=class{static{i(this,"LRUCache")}constructor(e){this.size=e,this.nodes=new Map}get(e){let n=this.nodes.get(e);if(n)return this.moveToHead(n),n.value}set(e,n){let r=this.nodes.get(e);if(r)r.value=n,this.moveToHead(r);else{let o=new _o(e,n);this.nodes.set(e,o),this.addNode(o),this.nodes.size>this.size&&(this.nodes.delete(this.tail.key),this.removeNode(this.tail))}}moveToHead(e){this.removeNode(e),e.next=void 0,e.prev=void 0,this.addNode(e)}addNode(e){this.head&&(this.head.prev=e,e.next=this.head),this.tail||(this.tail=e),this.head=e}removeNode(e){e.prev?e.prev.next=e.next:this.head=e.next,e.next?e.next.prev=e.prev:this.tail=e.prev}};Wr.LRUCache=mo;var _o=class{static{i(this,"Node")}constructor(e,n){this.key=e,this.value=n}}});var po=Z($r=>{"use strict";y();Object.defineProperty($r,"__esModule",{value:!0});$r.TikTokenizer=void 0;var Br=Wa(),Qf=Ba(),Jf=$a();function Xf(t){let e=new Map;try{let o=require("fs").readFileSync(t,"utf-8");return n(o),e}catch(r){throw new Error(`Failed to load from BPE encoder file stream: ${r}`)}function n(r){for(let o of r.split(/[\r\n]+/)){if(o.trim()==="")continue;let s=o.split(" ");if(s.length!==2)throw new Error("Invalid format in the BPE encoder file stream");let u=new Uint8Array(Buffer.from(s[0],"base64")),l=parseInt(s[1]);if(!isNaN(l))e.set(u,l);else throw new Error(`Can't parse ${s[1]} to integer`)}}i(n,"processBpeRanks")}i(Xf,"loadTikTokenBpe");function Yf(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}i(Yf,"escapeRegExp");var go=class{static{i(this,"TikTokenizer")}constructor(e,n,r,o=8192){this.textEncoder=(0,Qf.makeTextEncoder)(),this.textDecoder=new TextDecoder("utf-8"),this.cache=new Jf.LRUCache(o);let s=typeof e=="string"?Xf(e):e;this.init(s,n,r)}init(e,n,r){this.encoder=new Br.BinaryMap;for(let[o,s]of e)this.encoder.set(o,s);this.regex=new RegExp(r,"gu"),this.specialTokensRegex=new RegExp(Array.from(n.keys()).map(o=>Yf(o)).join("|")),this.specialTokensEncoder=n,this.decoder=new Map;for(let[o,s]of e)this.decoder.set(s,o);if(e.size!==this.decoder.size)throw new Error("Encoder and decoder sizes do not match");this.specialTokensDecoder=new Map;for(let[o,s]of n)this.specialTokensDecoder.set(s,o)}findNextSpecialToken(e,n,r){let o=n,s=null;if(r&&this.specialTokensRegex)for(;s=e.slice(o).match(this.specialTokensRegex),!(!s||r&&r.includes(s[0]));)o+=s.index+1;let u=s?o+s.index:e.length;return[s,u]}encode(e,n){let r=[],o=0;for(;;){let s,u;if([s,u]=this.findNextSpecialToken(e,o,n),u>o&&this.encodeByIndex(e,r,o,u),s){if(o=o+this.encodeSpecialToken(r,s),o>=e.length)break}else break}return r}encodeSpecialToken(e,n){let r=this.specialTokensEncoder?.get(n[0]);return e.push(r),n.index+n[0].length}encodeByIndex(e,n,r,o){let s,u=e.substring(r,o);for(this.regex.lastIndex=0;s=this.regex.exec(u);){let l=this.cache.get(s[0]);if(l)for(let f of l)n.push(f);else{let f=this.textEncoder.encode(s[0]),_=this.encoder.get(f,0,this.textEncoder.length);if(_!==void 0)n.push(_),this.cache.set(s[0],[_]);else{let v=(0,Br.bytePairEncode)(f,this.encoder,this.textEncoder.length);for(let T of v)n.push(T);this.cache.set(s[0],v)}}}}encodeTrimSuffixByIndex(e,n,r,o,s,u,l){let f,_=e.substring(r,o);for(this.regex.lastIndex=0;f=this.regex.exec(_);){let v=f[0],T=this.cache.get(v);if(T)if(u+T.length<=s)u+=T.length,l+=v.length,n.push(...T);else{let M=s-u;u+=M,l+=v.length,n.push(...T.slice(0,M));break}else{let M=this.textEncoder.encode(v),D=this.encoder.get(M,0,M.length);if(D!==void 0)if(this.cache.set(v,[D]),u+1<=s)u++,l+=v.length,n.push(D);else break;else{let W=(0,Br.bytePairEncode)(M,this.encoder,this.textEncoder.length);if(this.cache.set(v,W),u+W.length<=s){u+=W.length,l+=v.length;for(let F of W)n.push(F)}else{let F=s-u;u+=F,l+=v.length;for(let P=0;P<F;P++)n.push(W[P]);break}}}if(u>=s)break}return{tokenCount:u,encodeLength:l}}encodeTrimSuffix(e,n,r){let o=[],s=0,u=0,l=0;for(;;){let _,v;if([_,v]=this.findNextSpecialToken(e,s,r),v>s){let{tokenCount:T,encodeLength:M}=this.encodeTrimSuffixByIndex(e,o,s,v,n,u,l);if(u=T,l=M,u>=n)break}if(_!==null){if(u++,u<=n&&(s=s+this.encodeSpecialToken(o,_),l+=_[0].length,s>=e.length)||u>=n)break}else break}let f=l===e.length?e:e.slice(0,l);return{tokenIds:o,text:f}}encodeTrimPrefix(e,n,r){let o=[],s=0,u=0,l=0,f=new Map;for(f.set(u,l);;){let M,D;if([M,D]=this.findNextSpecialToken(e,s,r),D>s){let W,F=e.substring(s,D);for(this.regex.lastIndex=0;W=this.regex.exec(F);){let P=W[0],z=this.cache.get(P);if(z)u+=z.length,l+=P.length,o.push(...z),f.set(u,l);else{let L=this.textEncoder.encode(P),H=this.encoder.get(L);if(H!==void 0)this.cache.set(P,[H]),u++,l+=P.length,o.push(H),f.set(u,l);else{let Y=(0,Br.bytePairEncode)(L,this.encoder,this.textEncoder.length);this.cache.set(P,Y),u+=Y.length,l+=P.length;for(let K of Y)o.push(K);f.set(u,l)}}}}if(M!==null){if(s=s+this.encodeSpecialToken(o,M),u++,l+=M[0].length,f.set(u,l),s>=e.length)break}else break}if(u<=n)return{tokenIds:o,text:e};let _=u-n,v=0,T=0;for(let[M,D]of f)if(M>=_){v=M,T=D;break}if(v>n){let M=this.encode(e,r),D=M.slice(M.length-n);return{tokenIds:D,text:this.decode(D)}}return{tokenIds:o.slice(v),text:e.slice(T)}}decode(e){let n=[];for(let r of e){let o=[],s=this.decoder?.get(r);if(s!==void 0)o=Array.from(s);else{let u=this.specialTokensDecoder?.get(r);if(u!==void 0){let l=this.textEncoder.encode(u);o=Array.from(l.subarray(0,this.textEncoder.length))}}n.push(...o)}return this.textDecoder.decode(new Uint8Array(n))}};$r.TikTokenizer=go});var Ka=Z(Ne=>{"use strict";y();Object.defineProperty(Ne,"__esModule",{value:!0});Ne.createTokenizer=Ne.createByEncoderName=Ne.createByModelName=Ne.getRegexByModel=Ne.getRegexByEncoder=Ne.getSpecialTokensByModel=Ne.getSpecialTokensByEncoder=Ne.MODEL_TO_ENCODING=void 0;var Kf=po(),em=new Map([["gpt-4o-","o200k_base"],["gpt-4-","cl100k_base"],["gpt-3.5-turbo-","cl100k_base"],["gpt-35-turbo-","cl100k_base"]]);Ne.MODEL_TO_ENCODING=new Map([["gpt-4o","o200k_base"],["gpt-4","cl100k_base"],["gpt-3.5-turbo","cl100k_base"],["text-davinci-003","p50k_base"],["text-davinci-002","p50k_base"],["text-davinci-001","r50k_base"],["text-curie-001","r50k_base"],["text-babbage-001","r50k_base"],["text-ada-001","r50k_base"],["davinci","r50k_base"],["curie","r50k_base"],["babbage","r50k_base"],["ada","r50k_base"],["code-davinci-002","p50k_base"],["code-davinci-001","p50k_base"],["code-cushman-002","p50k_base"],["code-cushman-001","p50k_base"],["davinci-codex","p50k_base"],["cushman-codex","p50k_base"],["text-davinci-edit-001","p50k_edit"],["code-davinci-edit-001","p50k_edit"],["text-embedding-ada-002","cl100k_base"],["text-similarity-davinci-001","r50k_base"],["text-similarity-curie-001","r50k_base"],["text-similarity-babbage-001","r50k_base"],["text-similarity-ada-001","r50k_base"],["text-search-davinci-doc-001","r50k_base"],["text-search-curie-doc-001","r50k_base"],["text-search-babbage-doc-001","r50k_base"],["text-search-ada-doc-001","r50k_base"],["code-search-babbage-code-001","r50k_base"],["code-search-ada-code-001","r50k_base"],["gpt2","gpt2"]]);var zr="<|endoftext|>",za="<|fim_prefix|>",Ha="<|fim_middle|>",Va="<|fim_suffix|>",Ga="<|endofprompt|>",zn="'s|'t|'re|'ve|'m|'ll|'d| ?\\p{L}+| ?\\p{N}+| ?[^\\s\\p{L}\\p{N}]+|\\s+(?!\\S)|\\s+",Za="(?:'s|'S|'t|'T|'re|'RE|'Re|'eR|'ve|'VE|'vE|'Ve|'m|'M|'ll|'lL|'Ll|'LL|'d|'D)|[^\\r\\n\\p{L}\\p{N}]?\\p{L}+|\\p{N}{1,3}| ?[^\\s\\p{L}\\p{N}]+[\\r\\n]*|\\s*[\\r\\n]+|\\s+(?!\\S)|\\s+",tm=[`[^\r
\\p{L}\\p{N}]?[\\p{Lu}\\p{Lt}\\p{Lm}\\p{Lo}\\p{M}]*[\\p{Ll}\\p{Lm}\\p{Lo}\\p{M}]+(?:'s|'S|'t|'T|'re|'RE|'Re|'eR|'ve|'VE|'vE|'Ve|'m|'M|'ll|'lL|'Ll|'LL|'d|'D)?`,`[^\r
\\p{L}\\p{N}]?[\\p{Lu}\\p{Lt}\\p{Lm}\\p{Lo}\\p{M}]+[\\p{Ll}\\p{Lm}\\p{Lo}\\p{M}]*(?:'s|'S|'t|'T|'re|'RE|'Re|'eR|'ve|'VE|'vE|'Ve|'m|'M|'ll|'lL|'Ll|'LL|'d|'D)?`,"\\p{N}{1,3}"," ?[^\\s\\p{L}\\p{N}]+[\\r\\n/]*","\\s*[\\r\\n]+","\\s+(?!\\S)","\\s+"],Qa=tm.join("|");function ho(t){let e="";if(Ne.MODEL_TO_ENCODING.has(t))e=Ne.MODEL_TO_ENCODING.get(t);else for(let[n,r]of em)if(t.startsWith(n)){e=r;break}return e}i(ho,"getEncoderFromModelName");async function nm(t,e){let n=require("fs"),r=await fetch(t);if(!r.ok)throw new Error(`Failed to fetch file from ${t}. Status code: ${r.status}`);let o=await r.text();n.writeFileSync(e,o)}i(nm,"fetchAndSaveFile");function yo(t){let e=new Map([[zr,50256]]);switch(t){case"o200k_base":e=new Map([[zr,199999],[Ga,200018]]);break;case"cl100k_base":e=new Map([[zr,100257],[za,100258],[Ha,100259],[Va,100260],[Ga,100276]]);break;case"p50k_edit":e=new Map([[zr,50256],[za,50281],[Ha,50282],[Va,50283]]);break;default:break}return e}i(yo,"getSpecialTokensByEncoder");Ne.getSpecialTokensByEncoder=yo;function rm(t){let e=ho(t);return yo(e)}i(rm,"getSpecialTokensByModel");Ne.getSpecialTokensByModel=rm;function Ja(t){switch(t){case"o200k_base":return Qa;case"cl100k_base":return Za;default:break}return zn}i(Ja,"getRegexByEncoder");Ne.getRegexByEncoder=Ja;function im(t){let e=ho(t);return Ja(e)}i(im,"getRegexByModel");Ne.getRegexByModel=im;async function om(t,e=null){return Xa(ho(t),e)}i(om,"createByModelName");Ne.createByModelName=om;async function Xa(t,e=null){let n,r,o=yo(t);switch(t){case"o200k_base":n=Qa,r="https://openaipublic.blob.core.windows.net/encodings/o200k_base.tiktoken";break;case"cl100k_base":n=Za,r="https://openaipublic.blob.core.windows.net/encodings/cl100k_base.tiktoken";break;case"p50k_base":n=zn,r="https://openaipublic.blob.core.windows.net/encodings/p50k_base.tiktoken";break;case"p50k_edit":n=zn,r="https://openaipublic.blob.core.windows.net/encodings/p50k_base.tiktoken";break;case"r50k_base":n=zn,r="https://openaipublic.blob.core.windows.net/encodings/r50k_base.tiktoken";break;case"gpt2":n=zn,r="https://raw.githubusercontent.com/microsoft/Tokenizer/main/model/gpt2.tiktoken";break;default:throw new Error(`Doesn't support this encoder [${t}]`)}e!==null&&(o=new Map([...o,...e]));let s=require("fs"),u=require("path"),l=u.basename(r),f=u.resolve(__dirname,"..","model");s.existsSync(f)||s.mkdirSync(f,{recursive:!0});let _=u.resolve(f,l);return s.existsSync(_)||(console.log(`Downloading file from ${r}`),await nm(r,_),console.log(`Saved file to ${_}`)),Ya(_,o,n)}i(Xa,"createByEncoderName");Ne.createByEncoderName=Xa;function Ya(t,e,n,r=8192){return new Kf.TikTokenizer(t,e,n,r)}i(Ya,"createTokenizer");Ne.createTokenizer=Ya});var eu=Z(Me=>{"use strict";y();Object.defineProperty(Me,"__esModule",{value:!0});Me.createTokenizer=Me.createByEncoderName=Me.createByModelName=Me.getSpecialTokensByModel=Me.getSpecialTokensByEncoder=Me.getRegexByModel=Me.getRegexByEncoder=Me.MODEL_TO_ENCODING=Me.TikTokenizer=void 0;var sm=po();Object.defineProperty(Me,"TikTokenizer",{enumerable:!0,get:i(function(){return sm.TikTokenizer},"get")});var Ct=Ka();Object.defineProperty(Me,"MODEL_TO_ENCODING",{enumerable:!0,get:i(function(){return Ct.MODEL_TO_ENCODING},"get")});Object.defineProperty(Me,"getRegexByEncoder",{enumerable:!0,get:i(function(){return Ct.getRegexByEncoder},"get")});Object.defineProperty(Me,"getRegexByModel",{enumerable:!0,get:i(function(){return Ct.getRegexByModel},"get")});Object.defineProperty(Me,"getSpecialTokensByEncoder",{enumerable:!0,get:i(function(){return Ct.getSpecialTokensByEncoder},"get")});Object.defineProperty(Me,"getSpecialTokensByModel",{enumerable:!0,get:i(function(){return Ct.getSpecialTokensByModel},"get")});Object.defineProperty(Me,"createByModelName",{enumerable:!0,get:i(function(){return Ct.createByModelName},"get")});Object.defineProperty(Me,"createByEncoderName",{enumerable:!0,get:i(function(){return Ct.createByEncoderName},"get")});Object.defineProperty(Me,"createTokenizer",{enumerable:!0,get:i(function(){return Ct.createTokenizer},"get")})});var _u=Z((cb,mu)=>{y();var Po=require("fs"),Yr=require("path"),Kn=Yr.join,ym=Yr.dirname,du=Po.accessSync&&function(t){try{Po.accessSync(t)}catch{return!1}return!0}||Po.existsSync||Yr.existsSync,fu={arrow:process.env.NODE_BINDINGS_ARROW||" \u2192 ",compiled:process.env.NODE_BINDINGS_COMPILED_DIR||"compiled",platform:process.platform,arch:process.arch,nodePreGyp:"node-v"+process.versions.modules+"-"+process.platform+"-"+process.arch,version:process.versions.node,bindings:"bindings.node",try:[["module_root","build","bindings"],["module_root","build","Debug","bindings"],["module_root","build","Release","bindings"],["module_root","out","Debug","bindings"],["module_root","Debug","bindings"],["module_root","out","Release","bindings"],["module_root","Release","bindings"],["module_root","build","default","bindings"],["module_root","compiled","version","platform","arch","bindings"],["module_root","compiled","platform","arch","bindings"]]};function bm(t){typeof t=="string"?t={bindings:t}:t||(t={}),Object.keys(fu).map(function(f){f in t||(t[f]=fu[f])}),t.module_root||(t.module_root=wm(__filename)),Yr.extname(t.bindings)!=".node"&&(t.bindings+=".node");for(var e=typeof __webpack_require__=="function"?__non_webpack_require__:require,n=[],r=0,o=t.try.length,s,u,l;r<o;r++){s=Kn.apply(null,t.try[r].map(function(f){return t[f]||f})),n.push(s);try{return u=t.path?e.resolve(s):e(s),t.path||(u.path=s),u}catch(f){if(f.code!=="MODULE_NOT_FOUND"&&f.code!=="QUALIFIED_PATH_RESOLUTION_FAILED"&&!/not find/i.test(f.message))throw f}}throw l=new Error(`Could not locate the bindings file. Tried:
`+n.map(function(f){return t.arrow+f}).join(`
`)),l.tries=n,l}i(bm,"bindings");mu.exports=bm;function wm(t){for(var e=ym(t),n;;){if(e==="."&&(e=process.cwd()),du(Kn(e,"dist")))return Kn(e,"dist");if(du(Kn(e,"compiled")))return e;if(n===e)throw new Error('Could not find module root given file: "'+t+'". Do you have a `dist` or `compiled` directory? ');n=e,e=Kn(e,"..")}}i(wm,"getRoot")});var pu=Z((mb,gu)=>{y();gu.exports=_u()("node_sqlite3.node")});var bu=Z(yu=>{y();var vm=require("util");function Em(t,e,n){let r=t[e];t[e]=function(){let o=new Error,s=t.constructor.name+"#"+e+"("+Array.prototype.slice.call(arguments).map(function(l){return vm.inspect(l,!1,0)}).join(", ")+")";typeof n>"u"&&(n=-1),n<0&&(n+=arguments.length);let u=arguments[n];return typeof arguments[n]=="function"&&(arguments[n]=i(function(){let f=arguments[0];return f&&f.stack&&!f.__augmented&&(f.stack=hu(f).join(`
`),f.stack+=`
--> in `+s,f.stack+=`
`+hu(o).slice(1).join(`
`),f.__augmented=!0),u.apply(this,arguments)},"replacement")),r.apply(this,arguments)}}i(Em,"extendTrace");yu.extendTrace=Em;function hu(t){return t.stack.split(`
`).filter(function(e){return e.indexOf(__filename)<0})}i(hu,"filter")});var Tu=Z((vu,Eu)=>{y();var Tm=require("path"),rt=pu(),gn=require("events").EventEmitter;Eu.exports=vu=rt;function pn(t){return function(e){let n,r=Array.prototype.slice.call(arguments,1);if(typeof r[r.length-1]=="function"){let s=r[r.length-1];n=i(function(u){u&&s(u)},"errBack")}let o=new Kr(this,e,n);return t.call(this,o,r)}}i(pn,"normalizeMethod");function Lo(t,e){for(let n in e.prototype)t.prototype[n]=e.prototype[n]}i(Lo,"inherits");rt.cached={Database:i(function(t,e,n){if(t===""||t===":memory:")return new $e(t,e,n);let r;if(t=Tm.resolve(t),!rt.cached.objects[t])r=rt.cached.objects[t]=new $e(t,e,n);else{r=rt.cached.objects[t];let s=typeof e=="number"?n:e;if(typeof s=="function"){let u=function(){s.call(r,null)};var o=u;i(u,"cb"),r.open?process.nextTick(u):r.once("open",u)}}return r},"Database"),objects:{}};var $e=rt.Database,Kr=rt.Statement,Co=rt.Backup;Lo($e,gn);Lo(Kr,gn);Lo(Co,gn);$e.prototype.prepare=pn(function(t,e){return e.length?t.bind.apply(t,e):t});$e.prototype.run=pn(function(t,e){return t.run.apply(t,e).finalize(),this});$e.prototype.get=pn(function(t,e){return t.get.apply(t,e).finalize(),this});$e.prototype.all=pn(function(t,e){return t.all.apply(t,e).finalize(),this});$e.prototype.each=pn(function(t,e){return t.each.apply(t,e).finalize(),this});$e.prototype.map=pn(function(t,e){return t.map.apply(t,e).finalize(),this});$e.prototype.backup=function(){let t;return arguments.length<=2?t=new Co(this,arguments[0],"main","main",!0,arguments[1]):t=new Co(this,arguments[0],arguments[1],arguments[2],arguments[3],arguments[4]),t.retryErrors=[rt.BUSY,rt.LOCKED],t};Kr.prototype.map=function(){let t=Array.prototype.slice.call(arguments),e=t.pop();return t.push(function(n,r){if(n)return e(n);let o={};if(r.length){let s=Object.keys(r[0]),u=s[0];if(s.length>2)for(let l=0;l<r.length;l++)o[r[l][u]]=r[l];else{let l=s[1];for(let f=0;f<r.length;f++)o[r[f][u]]=r[f][l]}}e(n,o)}),this.all.apply(this,t)};var wu=!1,Do=["trace","profile","change"];$e.prototype.addListener=$e.prototype.on=function(t){let e=gn.prototype.addListener.apply(this,arguments);return Do.indexOf(t)>=0&&this.configure(t,!0),e};$e.prototype.removeListener=function(t){let e=gn.prototype.removeListener.apply(this,arguments);return Do.indexOf(t)>=0&&!this._events[t]&&this.configure(t,!1),e};$e.prototype.removeAllListeners=function(t){let e=gn.prototype.removeAllListeners.apply(this,arguments);return Do.indexOf(t)>=0&&this.configure(t,!1),e};rt.verbose=function(){if(!wu){let t=bu();["prepare","get","run","all","each","map","close","exec"].forEach(function(e){t.extendTrace($e.prototype,e)}),["bind","get","run","all","each","map","reset","finalize"].forEach(function(e){t.extendTrace(Kr.prototype,e)}),wu=!0}return rt}});var yn=Z(qe=>{"use strict";y();Object.defineProperty(qe,"__esModule",{value:!0});qe.stringArray=qe.array=qe.func=qe.error=qe.number=qe.string=qe.boolean=void 0;function Rm(t){return t===!0||t===!1}i(Rm,"boolean");qe.boolean=Rm;function Su(t){return typeof t=="string"||t instanceof String}i(Su,"string");qe.string=Su;function xm(t){return typeof t=="number"||t instanceof Number}i(xm,"number");qe.number=xm;function Im(t){return t instanceof Error}i(Im,"error");qe.error=Im;function Nm(t){return typeof t=="function"}i(Nm,"func");qe.func=Nm;function Ru(t){return Array.isArray(t)}i(Ru,"array");qe.array=Ru;function Mm(t){return Ru(t)&&t.every(e=>Su(e))}i(Mm,"stringArray");qe.stringArray=Mm});var as=Z(Q=>{"use strict";y();Object.defineProperty(Q,"__esModule",{value:!0});Q.Message=Q.NotificationType9=Q.NotificationType8=Q.NotificationType7=Q.NotificationType6=Q.NotificationType5=Q.NotificationType4=Q.NotificationType3=Q.NotificationType2=Q.NotificationType1=Q.NotificationType0=Q.NotificationType=Q.RequestType9=Q.RequestType8=Q.RequestType7=Q.RequestType6=Q.RequestType5=Q.RequestType4=Q.RequestType3=Q.RequestType2=Q.RequestType1=Q.RequestType=Q.RequestType0=Q.AbstractMessageSignature=Q.ParameterStructures=Q.ResponseError=Q.ErrorCodes=void 0;var Yt=yn(),Fo;(function(t){t.ParseError=-32700,t.InvalidRequest=-32600,t.MethodNotFound=-32601,t.InvalidParams=-32602,t.InternalError=-32603,t.jsonrpcReservedErrorRangeStart=-32099,t.serverErrorStart=-32099,t.MessageWriteError=-32099,t.MessageReadError=-32098,t.PendingResponseRejected=-32097,t.ConnectionInactive=-32096,t.ServerNotInitialized=-32002,t.UnknownErrorCode=-32001,t.jsonrpcReservedErrorRangeEnd=-32e3,t.serverErrorEnd=-32e3})(Fo||(Q.ErrorCodes=Fo={}));var qo=class t extends Error{static{i(this,"ResponseError")}constructor(e,n,r){super(n),this.code=Yt.number(e)?e:Fo.UnknownErrorCode,this.data=r,Object.setPrototypeOf(this,t.prototype)}toJson(){let e={code:this.code,message:this.message};return this.data!==void 0&&(e.data=this.data),e}};Q.ResponseError=qo;var Ge=class t{static{i(this,"ParameterStructures")}constructor(e){this.kind=e}static is(e){return e===t.auto||e===t.byName||e===t.byPosition}toString(){return this.kind}};Q.ParameterStructures=Ge;Ge.auto=new Ge("auto");Ge.byPosition=new Ge("byPosition");Ge.byName=new Ge("byName");var be=class{static{i(this,"AbstractMessageSignature")}constructor(e,n){this.method=e,this.numberOfParams=n}get parameterStructures(){return Ge.auto}};Q.AbstractMessageSignature=be;var jo=class extends be{static{i(this,"RequestType0")}constructor(e){super(e,0)}};Q.RequestType0=jo;var Uo=class extends be{static{i(this,"RequestType")}constructor(e,n=Ge.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};Q.RequestType=Uo;var Wo=class extends be{static{i(this,"RequestType1")}constructor(e,n=Ge.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};Q.RequestType1=Wo;var Bo=class extends be{static{i(this,"RequestType2")}constructor(e){super(e,2)}};Q.RequestType2=Bo;var $o=class extends be{static{i(this,"RequestType3")}constructor(e){super(e,3)}};Q.RequestType3=$o;var zo=class extends be{static{i(this,"RequestType4")}constructor(e){super(e,4)}};Q.RequestType4=zo;var Ho=class extends be{static{i(this,"RequestType5")}constructor(e){super(e,5)}};Q.RequestType5=Ho;var Vo=class extends be{static{i(this,"RequestType6")}constructor(e){super(e,6)}};Q.RequestType6=Vo;var Go=class extends be{static{i(this,"RequestType7")}constructor(e){super(e,7)}};Q.RequestType7=Go;var Zo=class extends be{static{i(this,"RequestType8")}constructor(e){super(e,8)}};Q.RequestType8=Zo;var Qo=class extends be{static{i(this,"RequestType9")}constructor(e){super(e,9)}};Q.RequestType9=Qo;var Jo=class extends be{static{i(this,"NotificationType")}constructor(e,n=Ge.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};Q.NotificationType=Jo;var Xo=class extends be{static{i(this,"NotificationType0")}constructor(e){super(e,0)}};Q.NotificationType0=Xo;var Yo=class extends be{static{i(this,"NotificationType1")}constructor(e,n=Ge.auto){super(e,1),this._parameterStructures=n}get parameterStructures(){return this._parameterStructures}};Q.NotificationType1=Yo;var Ko=class extends be{static{i(this,"NotificationType2")}constructor(e){super(e,2)}};Q.NotificationType2=Ko;var es=class extends be{static{i(this,"NotificationType3")}constructor(e){super(e,3)}};Q.NotificationType3=es;var ts=class extends be{static{i(this,"NotificationType4")}constructor(e){super(e,4)}};Q.NotificationType4=ts;var ns=class extends be{static{i(this,"NotificationType5")}constructor(e){super(e,5)}};Q.NotificationType5=ns;var rs=class extends be{static{i(this,"NotificationType6")}constructor(e){super(e,6)}};Q.NotificationType6=rs;var is=class extends be{static{i(this,"NotificationType7")}constructor(e){super(e,7)}};Q.NotificationType7=is;var os=class extends be{static{i(this,"NotificationType8")}constructor(e){super(e,8)}};Q.NotificationType8=os;var ss=class extends be{static{i(this,"NotificationType9")}constructor(e){super(e,9)}};Q.NotificationType9=ss;var xu;(function(t){function e(o){let s=o;return s&&Yt.string(s.method)&&(Yt.string(s.id)||Yt.number(s.id))}i(e,"isRequest"),t.isRequest=e;function n(o){let s=o;return s&&Yt.string(s.method)&&o.id===void 0}i(n,"isNotification"),t.isNotification=n;function r(o){let s=o;return s&&(s.result!==void 0||!!s.error)&&(Yt.string(s.id)||Yt.number(s.id)||s.id===null)}i(r,"isResponse"),t.isResponse=r})(xu||(Q.Message=xu={}))});var ls=Z(Dt=>{"use strict";y();var Iu;Object.defineProperty(Dt,"__esModule",{value:!0});Dt.LRUCache=Dt.LinkedMap=Dt.Touch=void 0;var je;(function(t){t.None=0,t.First=1,t.AsOld=t.First,t.Last=2,t.AsNew=t.Last})(je||(Dt.Touch=je={}));var ii=class{static{i(this,"LinkedMap")}constructor(){this[Iu]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,n=je.None){let r=this._map.get(e);if(r)return n!==je.None&&this.touch(r,n),r.value}set(e,n,r=je.None){let o=this._map.get(e);if(o)o.value=n,r!==je.None&&this.touch(o,r);else{switch(o={key:e,value:n,next:void 0,previous:void 0},r){case je.None:this.addItemLast(o);break;case je.First:this.addItemFirst(o);break;case je.Last:this.addItemLast(o);break;default:this.addItemLast(o);break}this._map.set(e,o),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){let n=this._map.get(e);if(n)return this._map.delete(e),this.removeItem(n),this._size--,n.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");let e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,n){let r=this._state,o=this._head;for(;o;){if(n?e.bind(n)(o.value,o.key,this):e(o.value,o.key,this),this._state!==r)throw new Error("LinkedMap got modified during iteration.");o=o.next}}keys(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:i(()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let o={value:n.key,done:!1};return n=n.next,o}else return{value:void 0,done:!0}},"next")};return r}values(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:i(()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let o={value:n.value,done:!1};return n=n.next,o}else return{value:void 0,done:!0}},"next")};return r}entries(){let e=this._state,n=this._head,r={[Symbol.iterator]:()=>r,next:i(()=>{if(this._state!==e)throw new Error("LinkedMap got modified during iteration.");if(n){let o={value:[n.key,n.value],done:!1};return n=n.next,o}else return{value:void 0,done:!0}},"next")};return r}[(Iu=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let n=this._head,r=this.size;for(;n&&r>e;)this._map.delete(n.key),n=n.next,r--;this._head=n,this._size=r,n&&(n.previous=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw new Error("Invalid list");this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw new Error("Invalid list");this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{let n=e.next,r=e.previous;if(!n||!r)throw new Error("Invalid list");n.previous=r,r.next=n}e.next=void 0,e.previous=void 0,this._state++}touch(e,n){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(n!==je.First&&n!==je.Last)){if(n===je.First){if(e===this._head)return;let r=e.next,o=e.previous;e===this._tail?(o.next=void 0,this._tail=o):(r.previous=o,o.next=r),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(n===je.Last){if(e===this._tail)return;let r=e.next,o=e.previous;e===this._head?(r.previous=void 0,this._head=r):(r.previous=o,o.next=r),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){let e=[];return this.forEach((n,r)=>{e.push([r,n])}),e}fromJSON(e){this.clear();for(let[n,r]of e)this.set(n,r)}};Dt.LinkedMap=ii;var us=class extends ii{static{i(this,"LRUCache")}constructor(e,n=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,n),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get ratio(){return this._ratio}set ratio(e){this._ratio=Math.min(Math.max(0,e),1),this.checkTrim()}get(e,n=je.AsNew){return super.get(e,n)}peek(e){return super.get(e,je.None)}set(e,n){return super.set(e,n,je.Last),this.checkTrim(),this}checkTrim(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))}};Dt.LRUCache=us});var Mu=Z(oi=>{"use strict";y();Object.defineProperty(oi,"__esModule",{value:!0});oi.Disposable=void 0;var Nu;(function(t){function e(n){return{dispose:n}}i(e,"create"),t.create=e})(Nu||(oi.Disposable=Nu={}))});var At=Z(ms=>{"use strict";y();Object.defineProperty(ms,"__esModule",{value:!0});var cs;function ds(){if(cs===void 0)throw new Error("No runtime abstraction layer installed");return cs}i(ds,"RAL");(function(t){function e(n){if(n===void 0)throw new Error("No runtime abstraction layer provided");cs=n}i(e,"install"),t.install=e})(ds||(ds={}));ms.default=ds});var wn=Z(bn=>{"use strict";y();Object.defineProperty(bn,"__esModule",{value:!0});bn.Emitter=bn.Event=void 0;var km=At(),ku;(function(t){let e={dispose(){}};t.None=function(){return e}})(ku||(bn.Event=ku={}));var _s=class{static{i(this,"CallbackList")}add(e,n=null,r){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(n),Array.isArray(r)&&r.push({dispose:i(()=>this.remove(e,n),"dispose")})}remove(e,n=null){if(!this._callbacks)return;let r=!1;for(let o=0,s=this._callbacks.length;o<s;o++)if(this._callbacks[o]===e)if(this._contexts[o]===n){this._callbacks.splice(o,1),this._contexts.splice(o,1);return}else r=!0;if(r)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...e){if(!this._callbacks)return[];let n=[],r=this._callbacks.slice(0),o=this._contexts.slice(0);for(let s=0,u=r.length;s<u;s++)try{n.push(r[s].apply(o[s],e))}catch(l){(0,km.default)().console.error(l)}return n}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}},si=class t{static{i(this,"Emitter")}constructor(e){this._options=e}get event(){return this._event||(this._event=(e,n,r)=>{this._callbacks||(this._callbacks=new _s),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,n);let o={dispose:i(()=>{this._callbacks&&(this._callbacks.remove(e,n),o.dispose=t._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))},"dispose")};return Array.isArray(r)&&r.push(o),o}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}};bn.Emitter=si;si._noop=function(){}});var li=Z(vn=>{"use strict";y();Object.defineProperty(vn,"__esModule",{value:!0});vn.CancellationTokenSource=vn.CancellationToken=void 0;var Pm=At(),Cm=yn(),gs=wn(),ai;(function(t){t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:gs.Event.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:gs.Event.None});function e(n){let r=n;return r&&(r===t.None||r===t.Cancelled||Cm.boolean(r.isCancellationRequested)&&!!r.onCancellationRequested)}i(e,"is"),t.is=e})(ai||(vn.CancellationToken=ai={}));var Lm=Object.freeze(function(t,e){let n=(0,Pm.default)().timer.setTimeout(t.bind(e),0);return{dispose(){n.dispose()}}}),ui=class{static{i(this,"MutableToken")}constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Lm:(this._emitter||(this._emitter=new gs.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}},ps=class{static{i(this,"CancellationTokenSource")}get token(){return this._token||(this._token=new ui),this._token}cancel(){this._token?this._token.cancel():this._token=ai.Cancelled}dispose(){this._token?this._token instanceof ui&&this._token.dispose():this._token=ai.None}};vn.CancellationTokenSource=ps});var Pu=Z(En=>{"use strict";y();Object.defineProperty(En,"__esModule",{value:!0});En.SharedArrayReceiverStrategy=En.SharedArraySenderStrategy=void 0;var Dm=li(),er;(function(t){t.Continue=0,t.Cancelled=1})(er||(er={}));var hs=class{static{i(this,"SharedArraySenderStrategy")}constructor(){this.buffers=new Map}enableCancellation(e){if(e.id===null)return;let n=new SharedArrayBuffer(4),r=new Int32Array(n,0,1);r[0]=er.Continue,this.buffers.set(e.id,n),e.$cancellationData=n}async sendCancellation(e,n){let r=this.buffers.get(n);if(r===void 0)return;let o=new Int32Array(r,0,1);Atomics.store(o,0,er.Cancelled)}cleanup(e){this.buffers.delete(e)}dispose(){this.buffers.clear()}};En.SharedArraySenderStrategy=hs;var ys=class{static{i(this,"SharedArrayBufferCancellationToken")}constructor(e){this.data=new Int32Array(e,0,1)}get isCancellationRequested(){return Atomics.load(this.data,0)===er.Cancelled}get onCancellationRequested(){throw new Error("Cancellation over SharedArrayBuffer doesn't support cancellation events")}},bs=class{static{i(this,"SharedArrayBufferCancellationTokenSource")}constructor(e){this.token=new ys(e)}cancel(){}dispose(){}},ws=class{static{i(this,"SharedArrayReceiverStrategy")}constructor(){this.kind="request"}createCancellationTokenSource(e){let n=e.$cancellationData;return n===void 0?new Dm.CancellationTokenSource:new bs(n)}};En.SharedArrayReceiverStrategy=ws});var Es=Z(ci=>{"use strict";y();Object.defineProperty(ci,"__esModule",{value:!0});ci.Semaphore=void 0;var Am=At(),vs=class{static{i(this,"Semaphore")}constructor(e=1){if(e<=0)throw new Error("Capacity must be greater than 0");this._capacity=e,this._active=0,this._waiting=[]}lock(e){return new Promise((n,r)=>{this._waiting.push({thunk:e,resolve:n,reject:r}),this.runNext()})}get active(){return this._active}runNext(){this._waiting.length===0||this._active===this._capacity||(0,Am.default)().timer.setImmediate(()=>this.doRunNext())}doRunNext(){if(this._waiting.length===0||this._active===this._capacity)return;let e=this._waiting.shift();if(this._active++,this._active>this._capacity)throw new Error("To many thunks active");try{let n=e.thunk();n instanceof Promise?n.then(r=>{this._active--,e.resolve(r),this.runNext()},r=>{this._active--,e.reject(r),this.runNext()}):(this._active--,e.resolve(n),this.runNext())}catch(n){this._active--,e.reject(n),this.runNext()}}};ci.Semaphore=vs});var Lu=Z(Ot=>{"use strict";y();Object.defineProperty(Ot,"__esModule",{value:!0});Ot.ReadableStreamMessageReader=Ot.AbstractMessageReader=Ot.MessageReader=void 0;var Ss=At(),Tn=yn(),Ts=wn(),Om=Es(),Cu;(function(t){function e(n){let r=n;return r&&Tn.func(r.listen)&&Tn.func(r.dispose)&&Tn.func(r.onError)&&Tn.func(r.onClose)&&Tn.func(r.onPartialMessage)}i(e,"is"),t.is=e})(Cu||(Ot.MessageReader=Cu={}));var di=class{static{i(this,"AbstractMessageReader")}constructor(){this.errorEmitter=new Ts.Emitter,this.closeEmitter=new Ts.Emitter,this.partialMessageEmitter=new Ts.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error(`Reader received error. Reason: ${Tn.string(e.message)?e.message:"unknown"}`)}};Ot.AbstractMessageReader=di;var Rs;(function(t){function e(n){let r,o,s,u=new Map,l,f=new Map;if(n===void 0||typeof n=="string")r=n??"utf-8";else{if(r=n.charset??"utf-8",n.contentDecoder!==void 0&&(s=n.contentDecoder,u.set(s.name,s)),n.contentDecoders!==void 0)for(let _ of n.contentDecoders)u.set(_.name,_);if(n.contentTypeDecoder!==void 0&&(l=n.contentTypeDecoder,f.set(l.name,l)),n.contentTypeDecoders!==void 0)for(let _ of n.contentTypeDecoders)f.set(_.name,_)}return l===void 0&&(l=(0,Ss.default)().applicationJson.decoder,f.set(l.name,l)),{charset:r,contentDecoder:s,contentDecoders:u,contentTypeDecoder:l,contentTypeDecoders:f}}i(e,"fromOptions"),t.fromOptions=e})(Rs||(Rs={}));var xs=class extends di{static{i(this,"ReadableStreamMessageReader")}constructor(e,n){super(),this.readable=e,this.options=Rs.fromOptions(n),this.buffer=(0,Ss.default)().messageBuffer.create(this.options.charset),this._partialMessageTimeout=1e4,this.nextMessageLength=-1,this.messageToken=0,this.readSemaphore=new Om.Semaphore(1)}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e;let n=this.readable.onData(r=>{this.onData(r)});return this.readable.onError(r=>this.fireError(r)),this.readable.onClose(()=>this.fireClose()),n}onData(e){try{for(this.buffer.append(e);;){if(this.nextMessageLength===-1){let r=this.buffer.tryReadHeaders(!0);if(!r)return;let o=r.get("content-length");if(!o){this.fireError(new Error(`Header must provide a Content-Length property.
${JSON.stringify(Object.fromEntries(r))}`));return}let s=parseInt(o);if(isNaN(s)){this.fireError(new Error(`Content-Length value must be a number. Got ${o}`));return}this.nextMessageLength=s}let n=this.buffer.tryReadBody(this.nextMessageLength);if(n===void 0){this.setPartialMessageTimer();return}this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.readSemaphore.lock(async()=>{let r=this.options.contentDecoder!==void 0?await this.options.contentDecoder.decode(n):n,o=await this.options.contentTypeDecoder.decode(r,this.options);this.callback(o)}).catch(r=>{this.fireError(r)})}}catch(n){this.fireError(n)}}clearPartialMessageTimer(){this.partialMessageTimer&&(this.partialMessageTimer.dispose(),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),!(this._partialMessageTimeout<=0)&&(this.partialMessageTimer=(0,Ss.default)().timer.setTimeout((e,n)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:n}),this.setPartialMessageTimer())},this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}};Ot.ReadableStreamMessageReader=xs});var qu=Z(Ft=>{"use strict";y();Object.defineProperty(Ft,"__esModule",{value:!0});Ft.WriteableStreamMessageWriter=Ft.AbstractMessageWriter=Ft.MessageWriter=void 0;var Du=At(),tr=yn(),Fm=Es(),Au=wn(),qm="Content-Length: ",Ou=`\r
`,Fu;(function(t){function e(n){let r=n;return r&&tr.func(r.dispose)&&tr.func(r.onClose)&&tr.func(r.onError)&&tr.func(r.write)}i(e,"is"),t.is=e})(Fu||(Ft.MessageWriter=Fu={}));var fi=class{static{i(this,"AbstractMessageWriter")}constructor(){this.errorEmitter=new Au.Emitter,this.closeEmitter=new Au.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,n,r){this.errorEmitter.fire([this.asError(e),n,r])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error(`Writer received error. Reason: ${tr.string(e.message)?e.message:"unknown"}`)}};Ft.AbstractMessageWriter=fi;var Is;(function(t){function e(n){return n===void 0||typeof n=="string"?{charset:n??"utf-8",contentTypeEncoder:(0,Du.default)().applicationJson.encoder}:{charset:n.charset??"utf-8",contentEncoder:n.contentEncoder,contentTypeEncoder:n.contentTypeEncoder??(0,Du.default)().applicationJson.encoder}}i(e,"fromOptions"),t.fromOptions=e})(Is||(Is={}));var Ns=class extends fi{static{i(this,"WriteableStreamMessageWriter")}constructor(e,n){super(),this.writable=e,this.options=Is.fromOptions(n),this.errorCount=0,this.writeSemaphore=new Fm.Semaphore(1),this.writable.onError(r=>this.fireError(r)),this.writable.onClose(()=>this.fireClose())}async write(e){return this.writeSemaphore.lock(async()=>this.options.contentTypeEncoder.encode(e,this.options).then(r=>this.options.contentEncoder!==void 0?this.options.contentEncoder.encode(r):r).then(r=>{let o=[];return o.push(qm,r.byteLength.toString(),Ou),o.push(Ou),this.doWrite(e,o,r)},r=>{throw this.fireError(r),r}))}async doWrite(e,n,r){try{return await this.writable.write(n.join(""),"ascii"),this.writable.write(r)}catch(o){return this.handleError(o,e),Promise.reject(o)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){this.writable.end()}};Ft.WriteableStreamMessageWriter=Ns});var ju=Z(mi=>{"use strict";y();Object.defineProperty(mi,"__esModule",{value:!0});mi.AbstractMessageBuffer=void 0;var jm=13,Um=10,Wm=`\r
`,Ms=class{static{i(this,"AbstractMessageBuffer")}constructor(e="utf-8"){this._encoding=e,this._chunks=[],this._totalLength=0}get encoding(){return this._encoding}append(e){let n=typeof e=="string"?this.fromString(e,this._encoding):e;this._chunks.push(n),this._totalLength+=n.byteLength}tryReadHeaders(e=!1){if(this._chunks.length===0)return;let n=0,r=0,o=0,s=0;e:for(;r<this._chunks.length;){let _=this._chunks[r];for(o=0;o<_.length;){switch(_[o]){case jm:switch(n){case 0:n=1;break;case 2:n=3;break;default:n=0}break;case Um:switch(n){case 1:n=2;break;case 3:n=4,o++;break e;default:n=0}break;default:n=0}o++}s+=_.byteLength,r++}if(n!==4)return;let u=this._read(s+o),l=new Map,f=this.toString(u,"ascii").split(Wm);if(f.length<2)return l;for(let _=0;_<f.length-2;_++){let v=f[_],T=v.indexOf(":");if(T===-1)throw new Error(`Message header must separate key and value using ':'
${v}`);let M=v.substr(0,T),D=v.substr(T+1).trim();l.set(e?M.toLowerCase():M,D)}return l}tryReadBody(e){if(!(this._totalLength<e))return this._read(e)}get numberOfBytes(){return this._totalLength}_read(e){if(e===0)return this.emptyBuffer();if(e>this._totalLength)throw new Error("Cannot read so many bytes!");if(this._chunks[0].byteLength===e){let s=this._chunks[0];return this._chunks.shift(),this._totalLength-=e,this.asNative(s)}if(this._chunks[0].byteLength>e){let s=this._chunks[0],u=this.asNative(s,e);return this._chunks[0]=s.slice(e),this._totalLength-=e,u}let n=this.allocNative(e),r=0,o=0;for(;e>0;){let s=this._chunks[o];if(s.byteLength>e){let u=s.slice(0,e);n.set(u,r),r+=e,this._chunks[o]=s.slice(e),this._totalLength-=e,e-=e}else n.set(s,r),r+=s.byteLength,this._chunks.shift(),this._totalLength-=s.byteLength,e-=s.byteLength}return n}};mi.AbstractMessageBuffer=Ms});var zu=Z(re=>{"use strict";y();Object.defineProperty(re,"__esModule",{value:!0});re.createMessageConnection=re.ConnectionOptions=re.MessageStrategy=re.CancellationStrategy=re.CancellationSenderStrategy=re.CancellationReceiverStrategy=re.RequestCancellationReceiverStrategy=re.IdCancellationReceiverStrategy=re.ConnectionStrategy=re.ConnectionError=re.ConnectionErrors=re.LogTraceNotification=re.SetTraceNotification=re.TraceFormat=re.TraceValues=re.Trace=re.NullLogger=re.ProgressType=re.ProgressToken=void 0;var Uu=At(),Ee=yn(),X=as(),Wu=ls(),nr=wn(),ks=li(),or;(function(t){t.type=new X.NotificationType("$/cancelRequest")})(or||(or={}));var Ps;(function(t){function e(n){return typeof n=="string"||typeof n=="number"}i(e,"is"),t.is=e})(Ps||(re.ProgressToken=Ps={}));var rr;(function(t){t.type=new X.NotificationType("$/progress")})(rr||(rr={}));var Cs=class{static{i(this,"ProgressType")}constructor(){}};re.ProgressType=Cs;var Ls;(function(t){function e(n){return Ee.func(n)}i(e,"is"),t.is=e})(Ls||(Ls={}));re.NullLogger=Object.freeze({error:i(()=>{},"error"),warn:i(()=>{},"warn"),info:i(()=>{},"info"),log:i(()=>{},"log")});var fe;(function(t){t[t.Off=0]="Off",t[t.Messages=1]="Messages",t[t.Compact=2]="Compact",t[t.Verbose=3]="Verbose"})(fe||(re.Trace=fe={}));var Bu;(function(t){t.Off="off",t.Messages="messages",t.Compact="compact",t.Verbose="verbose"})(Bu||(re.TraceValues=Bu={}));(function(t){function e(r){if(!Ee.string(r))return t.Off;switch(r=r.toLowerCase(),r){case"off":return t.Off;case"messages":return t.Messages;case"compact":return t.Compact;case"verbose":return t.Verbose;default:return t.Off}}i(e,"fromString"),t.fromString=e;function n(r){switch(r){case t.Off:return"off";case t.Messages:return"messages";case t.Compact:return"compact";case t.Verbose:return"verbose";default:return"off"}}i(n,"toString"),t.toString=n})(fe||(re.Trace=fe={}));var Ke;(function(t){t.Text="text",t.JSON="json"})(Ke||(re.TraceFormat=Ke={}));(function(t){function e(n){return Ee.string(n)?(n=n.toLowerCase(),n==="json"?t.JSON:t.Text):t.Text}i(e,"fromString"),t.fromString=e})(Ke||(re.TraceFormat=Ke={}));var Ds;(function(t){t.type=new X.NotificationType("$/setTrace")})(Ds||(re.SetTraceNotification=Ds={}));var _i;(function(t){t.type=new X.NotificationType("$/logTrace")})(_i||(re.LogTraceNotification=_i={}));var ir;(function(t){t[t.Closed=1]="Closed",t[t.Disposed=2]="Disposed",t[t.AlreadyListening=3]="AlreadyListening"})(ir||(re.ConnectionErrors=ir={}));var Sn=class t extends Error{static{i(this,"ConnectionError")}constructor(e,n){super(n),this.code=e,Object.setPrototypeOf(this,t.prototype)}};re.ConnectionError=Sn;var As;(function(t){function e(n){let r=n;return r&&Ee.func(r.cancelUndispatched)}i(e,"is"),t.is=e})(As||(re.ConnectionStrategy=As={}));var gi;(function(t){function e(n){let r=n;return r&&(r.kind===void 0||r.kind==="id")&&Ee.func(r.createCancellationTokenSource)&&(r.dispose===void 0||Ee.func(r.dispose))}i(e,"is"),t.is=e})(gi||(re.IdCancellationReceiverStrategy=gi={}));var Os;(function(t){function e(n){let r=n;return r&&r.kind==="request"&&Ee.func(r.createCancellationTokenSource)&&(r.dispose===void 0||Ee.func(r.dispose))}i(e,"is"),t.is=e})(Os||(re.RequestCancellationReceiverStrategy=Os={}));var pi;(function(t){t.Message=Object.freeze({createCancellationTokenSource(n){return new ks.CancellationTokenSource}});function e(n){return gi.is(n)||Os.is(n)}i(e,"is"),t.is=e})(pi||(re.CancellationReceiverStrategy=pi={}));var hi;(function(t){t.Message=Object.freeze({sendCancellation(n,r){return n.sendNotification(or.type,{id:r})},cleanup(n){}});function e(n){let r=n;return r&&Ee.func(r.sendCancellation)&&Ee.func(r.cleanup)}i(e,"is"),t.is=e})(hi||(re.CancellationSenderStrategy=hi={}));var yi;(function(t){t.Message=Object.freeze({receiver:pi.Message,sender:hi.Message});function e(n){let r=n;return r&&pi.is(r.receiver)&&hi.is(r.sender)}i(e,"is"),t.is=e})(yi||(re.CancellationStrategy=yi={}));var bi;(function(t){function e(n){let r=n;return r&&Ee.func(r.handleMessage)}i(e,"is"),t.is=e})(bi||(re.MessageStrategy=bi={}));var $u;(function(t){function e(n){let r=n;return r&&(yi.is(r.cancellationStrategy)||As.is(r.connectionStrategy)||bi.is(r.messageStrategy))}i(e,"is"),t.is=e})($u||(re.ConnectionOptions=$u={}));var dt;(function(t){t[t.New=1]="New",t[t.Listening=2]="Listening",t[t.Closed=3]="Closed",t[t.Disposed=4]="Disposed"})(dt||(dt={}));function Bm(t,e,n,r){let o=n!==void 0?n:re.NullLogger,s=0,u=0,l=0,f="2.0",_,v=new Map,T,M=new Map,D=new Map,W,F=new Wu.LinkedMap,P=new Map,z=new Set,L=new Map,H=fe.Off,Y=Ke.Text,K,le=dt.New,J=new nr.Emitter,ie=new nr.Emitter,Te=new nr.Emitter,Ce=new nr.Emitter,A=new nr.Emitter,E=r&&r.cancellationStrategy?r.cancellationStrategy:yi.Message;function j(g){if(g===null)throw new Error("Can't send requests with id null since the response can't be correlated.");return"req-"+g.toString()}i(j,"createRequestQueueKey");function B(g){return g===null?"res-unknown-"+(++l).toString():"res-"+g.toString()}i(B,"createResponseQueueKey");function S(){return"not-"+(++u).toString()}i(S,"createNotificationQueueKey");function I(g,x){X.Message.isRequest(x)?g.set(j(x.id),x):X.Message.isResponse(x)?g.set(B(x.id),x):g.set(S(),x)}i(I,"addMessageToQueue");function k(g){}i(k,"cancelUndispatched");function $(){return le===dt.Listening}i($,"isListening");function V(){return le===dt.Closed}i(V,"isClosed");function U(){return le===dt.Disposed}i(U,"isDisposed");function ee(){(le===dt.New||le===dt.Listening)&&(le=dt.Closed,ie.fire(void 0))}i(ee,"closeHandler");function te(g){J.fire([g,void 0,void 0])}i(te,"readErrorHandler");function Ie(g){J.fire(g)}i(Ie,"writeErrorHandler"),t.onClose(ee),t.onError(te),e.onClose(ee),e.onError(Ie);function de(){W||F.size===0||(W=(0,Uu.default)().timer.setImmediate(()=>{W=void 0,mt()}))}i(de,"triggerMessageQueue");function ze(g){X.Message.isRequest(g)?cr(g):X.Message.isNotification(g)?fr(g):X.Message.isResponse(g)?dr(g):on(g)}i(ze,"handleMessage");function mt(){if(F.size===0)return;let g=F.shift();try{let x=r?.messageStrategy;bi.is(x)?x.handleMessage(g,ze):ze(g)}finally{de()}}i(mt,"processMessageQueue");let lr=i(g=>{try{if(X.Message.isNotification(g)&&g.method===or.type.method){let x=g.params.id,q=j(x),G=F.get(q);if(X.Message.isRequest(G)){let me=r?.connectionStrategy,ye=me&&me.cancelUndispatched?me.cancelUndispatched(G,k):void 0;if(ye&&(ye.error!==void 0||ye.result!==void 0)){F.delete(q),L.delete(x),ye.id=G.id,$t(ye,g.method,Date.now()),e.write(ye).catch(()=>o.error("Sending response for canceled message failed."));return}}let _e=L.get(x);if(_e!==void 0){_e.cancel(),sn(g);return}else z.add(x)}I(F,g)}finally{de()}},"callback");function cr(g){if(U())return;function x(oe,ge,ce){let Se={jsonrpc:f,id:g.id};oe instanceof X.ResponseError?Se.error=oe.toJson():Se.result=oe===void 0?null:oe,$t(Se,ge,ce),e.write(Se).catch(()=>o.error("Sending response failed."))}i(x,"reply");function q(oe,ge,ce){let Se={jsonrpc:f,id:g.id,error:oe.toJson()};$t(Se,ge,ce),e.write(Se).catch(()=>o.error("Sending response failed."))}i(q,"replyError");function G(oe,ge,ce){oe===void 0&&(oe=null);let Se={jsonrpc:f,id:g.id,result:oe};$t(Se,ge,ce),e.write(Se).catch(()=>o.error("Sending response failed."))}i(G,"replySuccess"),gr(g);let _e=v.get(g.method),me,ye;_e&&(me=_e.type,ye=_e.handler);let we=Date.now();if(ye||_){let oe=g.id??String(Date.now()),ge=gi.is(E.receiver)?E.receiver.createCancellationTokenSource(oe):E.receiver.createCancellationTokenSource(g);g.id!==null&&z.has(g.id)&&ge.cancel(),g.id!==null&&L.set(oe,ge);try{let ce;if(ye)if(g.params===void 0){if(me!==void 0&&me.numberOfParams!==0){q(new X.ResponseError(X.ErrorCodes.InvalidParams,`Request ${g.method} defines ${me.numberOfParams} params but received none.`),g.method,we);return}ce=ye(ge.token)}else if(Array.isArray(g.params)){if(me!==void 0&&me.parameterStructures===X.ParameterStructures.byName){q(new X.ResponseError(X.ErrorCodes.InvalidParams,`Request ${g.method} defines parameters by name but received parameters by position`),g.method,we);return}ce=ye(...g.params,ge.token)}else{if(me!==void 0&&me.parameterStructures===X.ParameterStructures.byPosition){q(new X.ResponseError(X.ErrorCodes.InvalidParams,`Request ${g.method} defines parameters by position but received parameters by name`),g.method,we);return}ce=ye(g.params,ge.token)}else _&&(ce=_(g.method,g.params,ge.token));let Se=ce;ce?Se.then?Se.then(Le=>{L.delete(oe),x(Le,g.method,we)},Le=>{L.delete(oe),Le instanceof X.ResponseError?q(Le,g.method,we):Le&&Ee.string(Le.message)?q(new X.ResponseError(X.ErrorCodes.InternalError,`Request ${g.method} failed with message: ${Le.message}`),g.method,we):q(new X.ResponseError(X.ErrorCodes.InternalError,`Request ${g.method} failed unexpectedly without providing any details.`),g.method,we)}):(L.delete(oe),x(ce,g.method,we)):(L.delete(oe),G(ce,g.method,we))}catch(ce){L.delete(oe),ce instanceof X.ResponseError?x(ce,g.method,we):ce&&Ee.string(ce.message)?q(new X.ResponseError(X.ErrorCodes.InternalError,`Request ${g.method} failed with message: ${ce.message}`),g.method,we):q(new X.ResponseError(X.ErrorCodes.InternalError,`Request ${g.method} failed unexpectedly without providing any details.`),g.method,we)}}else q(new X.ResponseError(X.ErrorCodes.MethodNotFound,`Unhandled method ${g.method}`),g.method,we)}i(cr,"handleRequest");function dr(g){if(!U())if(g.id===null)g.error?o.error(`Received response message without id: Error is: 
${JSON.stringify(g.error,void 0,4)}`):o.error("Received response message without id. No further error information provided.");else{let x=g.id,q=P.get(x);if(pr(g,q),q!==void 0){P.delete(x);try{if(g.error){let G=g.error;q.reject(new X.ResponseError(G.code,G.message,G.data))}else if(g.result!==void 0)q.resolve(g.result);else throw new Error("Should never happen.")}catch(G){G.message?o.error(`Response handler '${q.method}' failed with message: ${G.message}`):o.error(`Response handler '${q.method}' failed unexpectedly.`)}}}}i(dr,"handleResponse");function fr(g){if(U())return;let x,q;if(g.method===or.type.method){let G=g.params.id;z.delete(G),sn(g);return}else{let G=M.get(g.method);G&&(q=G.handler,x=G.type)}if(q||T)try{if(sn(g),q)if(g.params===void 0)x!==void 0&&x.numberOfParams!==0&&x.parameterStructures!==X.ParameterStructures.byName&&o.error(`Notification ${g.method} defines ${x.numberOfParams} params but received none.`),q();else if(Array.isArray(g.params)){let G=g.params;g.method===rr.type.method&&G.length===2&&Ps.is(G[0])?q({token:G[0],value:G[1]}):(x!==void 0&&(x.parameterStructures===X.ParameterStructures.byName&&o.error(`Notification ${g.method} defines parameters by name but received parameters by position`),x.numberOfParams!==g.params.length&&o.error(`Notification ${g.method} defines ${x.numberOfParams} params but received ${G.length} arguments`)),q(...G))}else x!==void 0&&x.parameterStructures===X.ParameterStructures.byPosition&&o.error(`Notification ${g.method} defines parameters by position but received parameters by name`),q(g.params);else T&&T(g.method,g.params)}catch(G){G.message?o.error(`Notification handler '${g.method}' failed with message: ${G.message}`):o.error(`Notification handler '${g.method}' failed unexpectedly.`)}else Te.fire(g)}i(fr,"handleNotification");function on(g){if(!g){o.error("Received empty message.");return}o.error(`Received message which is neither a response nor a notification message:
${JSON.stringify(g,null,4)}`);let x=g;if(Ee.string(x.id)||Ee.number(x.id)){let q=x.id,G=P.get(q);G&&G.reject(new Error("The received response has neither a result nor an error property."))}}i(on,"handleInvalidMessage");function st(g){if(g!=null)switch(H){case fe.Verbose:return JSON.stringify(g,null,4);case fe.Compact:return JSON.stringify(g);default:return}}i(st,"stringifyTrace");function mr(g){if(!(H===fe.Off||!K))if(Y===Ke.Text){let x;(H===fe.Verbose||H===fe.Compact)&&g.params&&(x=`Params: ${st(g.params)}

`),K.log(`Sending request '${g.method} - (${g.id})'.`,x)}else wt("send-request",g)}i(mr,"traceSendingRequest");function _r(g){if(!(H===fe.Off||!K))if(Y===Ke.Text){let x;(H===fe.Verbose||H===fe.Compact)&&(g.params?x=`Params: ${st(g.params)}

`:x=`No parameters provided.

`),K.log(`Sending notification '${g.method}'.`,x)}else wt("send-notification",g)}i(_r,"traceSendingNotification");function $t(g,x,q){if(!(H===fe.Off||!K))if(Y===Ke.Text){let G;(H===fe.Verbose||H===fe.Compact)&&(g.error&&g.error.data?G=`Error data: ${st(g.error.data)}

`:g.result?G=`Result: ${st(g.result)}

`:g.error===void 0&&(G=`No result returned.

`)),K.log(`Sending response '${x} - (${g.id})'. Processing request took ${Date.now()-q}ms`,G)}else wt("send-response",g)}i($t,"traceSendingResponse");function gr(g){if(!(H===fe.Off||!K))if(Y===Ke.Text){let x;(H===fe.Verbose||H===fe.Compact)&&g.params&&(x=`Params: ${st(g.params)}

`),K.log(`Received request '${g.method} - (${g.id})'.`,x)}else wt("receive-request",g)}i(gr,"traceReceivedRequest");function sn(g){if(!(H===fe.Off||!K||g.method===_i.type.method))if(Y===Ke.Text){let x;(H===fe.Verbose||H===fe.Compact)&&(g.params?x=`Params: ${st(g.params)}

`:x=`No parameters provided.

`),K.log(`Received notification '${g.method}'.`,x)}else wt("receive-notification",g)}i(sn,"traceReceivedNotification");function pr(g,x){if(!(H===fe.Off||!K))if(Y===Ke.Text){let q;if((H===fe.Verbose||H===fe.Compact)&&(g.error&&g.error.data?q=`Error data: ${st(g.error.data)}

`:g.result?q=`Result: ${st(g.result)}

`:g.error===void 0&&(q=`No result returned.

`)),x){let G=g.error?` Request failed: ${g.error.message} (${g.error.code}).`:"";K.log(`Received response '${x.method} - (${g.id})' in ${Date.now()-x.timerStart}ms.${G}`,q)}else K.log(`Received response ${g.id} without active response promise.`,q)}else wt("receive-response",g)}i(pr,"traceReceivedResponse");function wt(g,x){if(!K||H===fe.Off)return;let q={isLSPMessage:!0,type:g,message:x,timestamp:Date.now()};K.log(q)}i(wt,"logLSPMessage");function Nt(){if(V())throw new Sn(ir.Closed,"Connection is closed.");if(U())throw new Sn(ir.Disposed,"Connection is disposed.")}i(Nt,"throwIfClosedOrDisposed");function hr(){if($())throw new Sn(ir.AlreadyListening,"Connection is already listening")}i(hr,"throwIfListening");function yr(){if(!$())throw new Error("Call listen() first.")}i(yr,"throwIfNotListening");function _t(g){return g===void 0?null:g}i(_t,"undefinedToNull");function Fn(g){if(g!==null)return g}i(Fn,"nullToUndefined");function qn(g){return g!=null&&!Array.isArray(g)&&typeof g=="object"}i(qn,"isNamedParam");function an(g,x){switch(g){case X.ParameterStructures.auto:return qn(x)?Fn(x):[_t(x)];case X.ParameterStructures.byName:if(!qn(x))throw new Error("Received parameters by name but param is not an object literal.");return Fn(x);case X.ParameterStructures.byPosition:return[_t(x)];default:throw new Error(`Unknown parameter structure ${g.toString()}`)}}i(an,"computeSingleParam");function jn(g,x){let q,G=g.numberOfParams;switch(G){case 0:q=void 0;break;case 1:q=an(g.parameterStructures,x[0]);break;default:q=[];for(let _e=0;_e<x.length&&_e<G;_e++)q.push(_t(x[_e]));if(x.length<G)for(let _e=x.length;_e<G;_e++)q.push(null);break}return q}i(jn,"computeMessageParams");let vt={sendNotification:i((g,...x)=>{Nt();let q,G;if(Ee.string(g)){q=g;let me=x[0],ye=0,we=X.ParameterStructures.auto;X.ParameterStructures.is(me)&&(ye=1,we=me);let oe=x.length,ge=oe-ye;switch(ge){case 0:G=void 0;break;case 1:G=an(we,x[ye]);break;default:if(we===X.ParameterStructures.byName)throw new Error(`Received ${ge} parameters for 'by Name' notification parameter structure.`);G=x.slice(ye,oe).map(ce=>_t(ce));break}}else{let me=x;q=g.method,G=jn(g,me)}let _e={jsonrpc:f,method:q,params:G};return _r(_e),e.write(_e).catch(me=>{throw o.error("Sending notification failed."),me})},"sendNotification"),onNotification:i((g,x)=>{Nt();let q;return Ee.func(g)?T=g:x&&(Ee.string(g)?(q=g,M.set(g,{type:void 0,handler:x})):(q=g.method,M.set(g.method,{type:g,handler:x}))),{dispose:i(()=>{q!==void 0?M.delete(q):T=void 0},"dispose")}},"onNotification"),onProgress:i((g,x,q)=>{if(D.has(x))throw new Error(`Progress handler for token ${x} already registered`);return D.set(x,q),{dispose:i(()=>{D.delete(x)},"dispose")}},"onProgress"),sendProgress:i((g,x,q)=>vt.sendNotification(rr.type,{token:x,value:q}),"sendProgress"),onUnhandledProgress:Ce.event,sendRequest:i((g,...x)=>{Nt(),yr();let q,G,_e;if(Ee.string(g)){q=g;let oe=x[0],ge=x[x.length-1],ce=0,Se=X.ParameterStructures.auto;X.ParameterStructures.is(oe)&&(ce=1,Se=oe);let Le=x.length;ks.CancellationToken.is(ge)&&(Le=Le-1,_e=ge);let Xe=Le-ce;switch(Xe){case 0:G=void 0;break;case 1:G=an(Se,x[ce]);break;default:if(Se===X.ParameterStructures.byName)throw new Error(`Received ${Xe} parameters for 'by Name' request parameter structure.`);G=x.slice(ce,Le).map(br=>_t(br));break}}else{let oe=x;q=g.method,G=jn(g,oe);let ge=g.numberOfParams;_e=ks.CancellationToken.is(oe[ge])?oe[ge]:void 0}let me=s++,ye;_e&&(ye=_e.onCancellationRequested(()=>{let oe=E.sender.sendCancellation(vt,me);return oe===void 0?(o.log(`Received no promise from cancellation strategy when cancelling id ${me}`),Promise.resolve()):oe.catch(()=>{o.log(`Sending cancellation messages for id ${me} failed`)})}));let we={jsonrpc:f,id:me,method:q,params:G};return mr(we),typeof E.sender.enableCancellation=="function"&&E.sender.enableCancellation(we),new Promise(async(oe,ge)=>{let ce=i(Xe=>{oe(Xe),E.sender.cleanup(me),ye?.dispose()},"resolveWithCleanup"),Se=i(Xe=>{ge(Xe),E.sender.cleanup(me),ye?.dispose()},"rejectWithCleanup"),Le={method:q,timerStart:Date.now(),resolve:ce,reject:Se};try{await e.write(we),P.set(me,Le)}catch(Xe){throw o.error("Sending request failed."),Le.reject(new X.ResponseError(X.ErrorCodes.MessageWriteError,Xe.message?Xe.message:"Unknown reason")),Xe}})},"sendRequest"),onRequest:i((g,x)=>{Nt();let q=null;return Ls.is(g)?(q=void 0,_=g):Ee.string(g)?(q=null,x!==void 0&&(q=g,v.set(g,{handler:x,type:void 0}))):x!==void 0&&(q=g.method,v.set(g.method,{type:g,handler:x})),{dispose:i(()=>{q!==null&&(q!==void 0?v.delete(q):_=void 0)},"dispose")}},"onRequest"),hasPendingResponse:i(()=>P.size>0,"hasPendingResponse"),trace:i(async(g,x,q)=>{let G=!1,_e=Ke.Text;q!==void 0&&(Ee.boolean(q)?G=q:(G=q.sendNotification||!1,_e=q.traceFormat||Ke.Text)),H=g,Y=_e,H===fe.Off?K=void 0:K=x,G&&!V()&&!U()&&await vt.sendNotification(Ds.type,{value:fe.toString(g)})},"trace"),onError:J.event,onClose:ie.event,onUnhandledNotification:Te.event,onDispose:A.event,end:i(()=>{e.end()},"end"),dispose:i(()=>{if(U())return;le=dt.Disposed,A.fire(void 0);let g=new X.ResponseError(X.ErrorCodes.PendingResponseRejected,"Pending response rejected since connection got disposed");for(let x of P.values())x.reject(g);P=new Map,L=new Map,z=new Set,F=new Wu.LinkedMap,Ee.func(e.dispose)&&e.dispose(),Ee.func(t.dispose)&&t.dispose()},"dispose"),listen:i(()=>{Nt(),hr(),le=dt.Listening,t.listen(lr)},"listen"),inspect:i(()=>{(0,Uu.default)().console.log("inspect")},"inspect")};return vt.onNotification(_i.type,g=>{if(H===fe.Off||!K)return;let x=H===fe.Verbose||H===fe.Compact;K.log(g.message,x?g.verbose:void 0)}),vt.onNotification(rr.type,g=>{let x=D.get(g.token);x?x(g.value):Ce.fire(g)}),vt}i(Bm,"createMessageConnection");re.createMessageConnection=Bm});var wi=Z(R=>{"use strict";y();Object.defineProperty(R,"__esModule",{value:!0});R.ProgressType=R.ProgressToken=R.createMessageConnection=R.NullLogger=R.ConnectionOptions=R.ConnectionStrategy=R.AbstractMessageBuffer=R.WriteableStreamMessageWriter=R.AbstractMessageWriter=R.MessageWriter=R.ReadableStreamMessageReader=R.AbstractMessageReader=R.MessageReader=R.SharedArrayReceiverStrategy=R.SharedArraySenderStrategy=R.CancellationToken=R.CancellationTokenSource=R.Emitter=R.Event=R.Disposable=R.LRUCache=R.Touch=R.LinkedMap=R.ParameterStructures=R.NotificationType9=R.NotificationType8=R.NotificationType7=R.NotificationType6=R.NotificationType5=R.NotificationType4=R.NotificationType3=R.NotificationType2=R.NotificationType1=R.NotificationType0=R.NotificationType=R.ErrorCodes=R.ResponseError=R.RequestType9=R.RequestType8=R.RequestType7=R.RequestType6=R.RequestType5=R.RequestType4=R.RequestType3=R.RequestType2=R.RequestType1=R.RequestType0=R.RequestType=R.Message=R.RAL=void 0;R.MessageStrategy=R.CancellationStrategy=R.CancellationSenderStrategy=R.CancellationReceiverStrategy=R.ConnectionError=R.ConnectionErrors=R.LogTraceNotification=R.SetTraceNotification=R.TraceFormat=R.TraceValues=R.Trace=void 0;var he=as();Object.defineProperty(R,"Message",{enumerable:!0,get:i(function(){return he.Message},"get")});Object.defineProperty(R,"RequestType",{enumerable:!0,get:i(function(){return he.RequestType},"get")});Object.defineProperty(R,"RequestType0",{enumerable:!0,get:i(function(){return he.RequestType0},"get")});Object.defineProperty(R,"RequestType1",{enumerable:!0,get:i(function(){return he.RequestType1},"get")});Object.defineProperty(R,"RequestType2",{enumerable:!0,get:i(function(){return he.RequestType2},"get")});Object.defineProperty(R,"RequestType3",{enumerable:!0,get:i(function(){return he.RequestType3},"get")});Object.defineProperty(R,"RequestType4",{enumerable:!0,get:i(function(){return he.RequestType4},"get")});Object.defineProperty(R,"RequestType5",{enumerable:!0,get:i(function(){return he.RequestType5},"get")});Object.defineProperty(R,"RequestType6",{enumerable:!0,get:i(function(){return he.RequestType6},"get")});Object.defineProperty(R,"RequestType7",{enumerable:!0,get:i(function(){return he.RequestType7},"get")});Object.defineProperty(R,"RequestType8",{enumerable:!0,get:i(function(){return he.RequestType8},"get")});Object.defineProperty(R,"RequestType9",{enumerable:!0,get:i(function(){return he.RequestType9},"get")});Object.defineProperty(R,"ResponseError",{enumerable:!0,get:i(function(){return he.ResponseError},"get")});Object.defineProperty(R,"ErrorCodes",{enumerable:!0,get:i(function(){return he.ErrorCodes},"get")});Object.defineProperty(R,"NotificationType",{enumerable:!0,get:i(function(){return he.NotificationType},"get")});Object.defineProperty(R,"NotificationType0",{enumerable:!0,get:i(function(){return he.NotificationType0},"get")});Object.defineProperty(R,"NotificationType1",{enumerable:!0,get:i(function(){return he.NotificationType1},"get")});Object.defineProperty(R,"NotificationType2",{enumerable:!0,get:i(function(){return he.NotificationType2},"get")});Object.defineProperty(R,"NotificationType3",{enumerable:!0,get:i(function(){return he.NotificationType3},"get")});Object.defineProperty(R,"NotificationType4",{enumerable:!0,get:i(function(){return he.NotificationType4},"get")});Object.defineProperty(R,"NotificationType5",{enumerable:!0,get:i(function(){return he.NotificationType5},"get")});Object.defineProperty(R,"NotificationType6",{enumerable:!0,get:i(function(){return he.NotificationType6},"get")});Object.defineProperty(R,"NotificationType7",{enumerable:!0,get:i(function(){return he.NotificationType7},"get")});Object.defineProperty(R,"NotificationType8",{enumerable:!0,get:i(function(){return he.NotificationType8},"get")});Object.defineProperty(R,"NotificationType9",{enumerable:!0,get:i(function(){return he.NotificationType9},"get")});Object.defineProperty(R,"ParameterStructures",{enumerable:!0,get:i(function(){return he.ParameterStructures},"get")});var Fs=ls();Object.defineProperty(R,"LinkedMap",{enumerable:!0,get:i(function(){return Fs.LinkedMap},"get")});Object.defineProperty(R,"LRUCache",{enumerable:!0,get:i(function(){return Fs.LRUCache},"get")});Object.defineProperty(R,"Touch",{enumerable:!0,get:i(function(){return Fs.Touch},"get")});var $m=Mu();Object.defineProperty(R,"Disposable",{enumerable:!0,get:i(function(){return $m.Disposable},"get")});var Hu=wn();Object.defineProperty(R,"Event",{enumerable:!0,get:i(function(){return Hu.Event},"get")});Object.defineProperty(R,"Emitter",{enumerable:!0,get:i(function(){return Hu.Emitter},"get")});var Vu=li();Object.defineProperty(R,"CancellationTokenSource",{enumerable:!0,get:i(function(){return Vu.CancellationTokenSource},"get")});Object.defineProperty(R,"CancellationToken",{enumerable:!0,get:i(function(){return Vu.CancellationToken},"get")});var Gu=Pu();Object.defineProperty(R,"SharedArraySenderStrategy",{enumerable:!0,get:i(function(){return Gu.SharedArraySenderStrategy},"get")});Object.defineProperty(R,"SharedArrayReceiverStrategy",{enumerable:!0,get:i(function(){return Gu.SharedArrayReceiverStrategy},"get")});var qs=Lu();Object.defineProperty(R,"MessageReader",{enumerable:!0,get:i(function(){return qs.MessageReader},"get")});Object.defineProperty(R,"AbstractMessageReader",{enumerable:!0,get:i(function(){return qs.AbstractMessageReader},"get")});Object.defineProperty(R,"ReadableStreamMessageReader",{enumerable:!0,get:i(function(){return qs.ReadableStreamMessageReader},"get")});var js=qu();Object.defineProperty(R,"MessageWriter",{enumerable:!0,get:i(function(){return js.MessageWriter},"get")});Object.defineProperty(R,"AbstractMessageWriter",{enumerable:!0,get:i(function(){return js.AbstractMessageWriter},"get")});Object.defineProperty(R,"WriteableStreamMessageWriter",{enumerable:!0,get:i(function(){return js.WriteableStreamMessageWriter},"get")});var zm=ju();Object.defineProperty(R,"AbstractMessageBuffer",{enumerable:!0,get:i(function(){return zm.AbstractMessageBuffer},"get")});var Fe=zu();Object.defineProperty(R,"ConnectionStrategy",{enumerable:!0,get:i(function(){return Fe.ConnectionStrategy},"get")});Object.defineProperty(R,"ConnectionOptions",{enumerable:!0,get:i(function(){return Fe.ConnectionOptions},"get")});Object.defineProperty(R,"NullLogger",{enumerable:!0,get:i(function(){return Fe.NullLogger},"get")});Object.defineProperty(R,"createMessageConnection",{enumerable:!0,get:i(function(){return Fe.createMessageConnection},"get")});Object.defineProperty(R,"ProgressToken",{enumerable:!0,get:i(function(){return Fe.ProgressToken},"get")});Object.defineProperty(R,"ProgressType",{enumerable:!0,get:i(function(){return Fe.ProgressType},"get")});Object.defineProperty(R,"Trace",{enumerable:!0,get:i(function(){return Fe.Trace},"get")});Object.defineProperty(R,"TraceValues",{enumerable:!0,get:i(function(){return Fe.TraceValues},"get")});Object.defineProperty(R,"TraceFormat",{enumerable:!0,get:i(function(){return Fe.TraceFormat},"get")});Object.defineProperty(R,"SetTraceNotification",{enumerable:!0,get:i(function(){return Fe.SetTraceNotification},"get")});Object.defineProperty(R,"LogTraceNotification",{enumerable:!0,get:i(function(){return Fe.LogTraceNotification},"get")});Object.defineProperty(R,"ConnectionErrors",{enumerable:!0,get:i(function(){return Fe.ConnectionErrors},"get")});Object.defineProperty(R,"ConnectionError",{enumerable:!0,get:i(function(){return Fe.ConnectionError},"get")});Object.defineProperty(R,"CancellationReceiverStrategy",{enumerable:!0,get:i(function(){return Fe.CancellationReceiverStrategy},"get")});Object.defineProperty(R,"CancellationSenderStrategy",{enumerable:!0,get:i(function(){return Fe.CancellationSenderStrategy},"get")});Object.defineProperty(R,"CancellationStrategy",{enumerable:!0,get:i(function(){return Fe.CancellationStrategy},"get")});Object.defineProperty(R,"MessageStrategy",{enumerable:!0,get:i(function(){return Fe.MessageStrategy},"get")});var Hm=At();R.RAL=Hm.default});var Ju=Z($s=>{"use strict";y();Object.defineProperty($s,"__esModule",{value:!0});var Zu=require("util"),xt=wi(),vi=class t extends xt.AbstractMessageBuffer{static{i(this,"MessageBuffer")}constructor(e="utf-8"){super(e)}emptyBuffer(){return t.emptyBuffer}fromString(e,n){return Buffer.from(e,n)}toString(e,n){return e instanceof Buffer?e.toString(n):new Zu.TextDecoder(n).decode(e)}asNative(e,n){return n===void 0?e instanceof Buffer?e:Buffer.from(e):e instanceof Buffer?e.slice(0,n):Buffer.from(e,0,n)}allocNative(e){return Buffer.allocUnsafe(e)}};vi.emptyBuffer=Buffer.allocUnsafe(0);var Us=class{static{i(this,"ReadableStreamWrapper")}constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),xt.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),xt.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),xt.Disposable.create(()=>this.stream.off("end",e))}onData(e){return this.stream.on("data",e),xt.Disposable.create(()=>this.stream.off("data",e))}},Ws=class{static{i(this,"WritableStreamWrapper")}constructor(e){this.stream=e}onClose(e){return this.stream.on("close",e),xt.Disposable.create(()=>this.stream.off("close",e))}onError(e){return this.stream.on("error",e),xt.Disposable.create(()=>this.stream.off("error",e))}onEnd(e){return this.stream.on("end",e),xt.Disposable.create(()=>this.stream.off("end",e))}write(e,n){return new Promise((r,o)=>{let s=i(u=>{u==null?r():o(u)},"callback");typeof e=="string"?this.stream.write(e,n,s):this.stream.write(e,s)})}end(){this.stream.end()}},Qu=Object.freeze({messageBuffer:Object.freeze({create:i(t=>new vi(t),"create")}),applicationJson:Object.freeze({encoder:Object.freeze({name:"application/json",encode:i((t,e)=>{try{return Promise.resolve(Buffer.from(JSON.stringify(t,void 0,0),e.charset))}catch(n){return Promise.reject(n)}},"encode")}),decoder:Object.freeze({name:"application/json",decode:i((t,e)=>{try{return t instanceof Buffer?Promise.resolve(JSON.parse(t.toString(e.charset))):Promise.resolve(JSON.parse(new Zu.TextDecoder(e.charset).decode(t)))}catch(n){return Promise.reject(n)}},"decode")})}),stream:Object.freeze({asReadableStream:i(t=>new Us(t),"asReadableStream"),asWritableStream:i(t=>new Ws(t),"asWritableStream")}),console,timer:Object.freeze({setTimeout(t,e,...n){let r=setTimeout(t,e,...n);return{dispose:i(()=>clearTimeout(r),"dispose")}},setImmediate(t,...e){let n=setImmediate(t,...e);return{dispose:i(()=>clearImmediate(n),"dispose")}},setInterval(t,e,...n){let r=setInterval(t,e,...n);return{dispose:i(()=>clearInterval(r),"dispose")}}})});function Bs(){return Qu}i(Bs,"RIL");(function(t){function e(){xt.RAL.install(Qu)}i(e,"install"),t.install=e})(Bs||(Bs={}));$s.default=Bs});var tn=Z(ue=>{"use strict";y();var Vm=ue&&ue.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var o=Object.getOwnPropertyDescriptor(e,n);(!o||("get"in o?!e.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:i(function(){return e[n]},"get")}),Object.defineProperty(t,r,o)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Gm=ue&&ue.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&Vm(e,t,n)};Object.defineProperty(ue,"__esModule",{value:!0});ue.createMessageConnection=ue.createServerSocketTransport=ue.createClientSocketTransport=ue.createServerPipeTransport=ue.createClientPipeTransport=ue.generateRandomPipeName=ue.StreamMessageWriter=ue.StreamMessageReader=ue.SocketMessageWriter=ue.SocketMessageReader=ue.PortMessageWriter=ue.PortMessageReader=ue.IPCMessageWriter=ue.IPCMessageReader=void 0;var Rn=Ju();Rn.default.install();var Xu=require("path"),Zm=require("os"),Qm=require("crypto"),Si=require("net"),et=wi();Gm(wi(),ue);var zs=class extends et.AbstractMessageReader{static{i(this,"IPCMessageReader")}constructor(e){super(),this.process=e;let n=this.process;n.on("error",r=>this.fireError(r)),n.on("close",()=>this.fireClose())}listen(e){return this.process.on("message",e),et.Disposable.create(()=>this.process.off("message",e))}};ue.IPCMessageReader=zs;var Hs=class extends et.AbstractMessageWriter{static{i(this,"IPCMessageWriter")}constructor(e){super(),this.process=e,this.errorCount=0;let n=this.process;n.on("error",r=>this.fireError(r)),n.on("close",()=>this.fireClose)}write(e){try{return typeof this.process.send=="function"&&this.process.send(e,void 0,void 0,n=>{n?(this.errorCount++,this.handleError(n,e)):this.errorCount=0}),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};ue.IPCMessageWriter=Hs;var Vs=class extends et.AbstractMessageReader{static{i(this,"PortMessageReader")}constructor(e){super(),this.onData=new et.Emitter,e.on("close",()=>this.fireClose),e.on("error",n=>this.fireError(n)),e.on("message",n=>{this.onData.fire(n)})}listen(e){return this.onData.event(e)}};ue.PortMessageReader=Vs;var Gs=class extends et.AbstractMessageWriter{static{i(this,"PortMessageWriter")}constructor(e){super(),this.port=e,this.errorCount=0,e.on("close",()=>this.fireClose()),e.on("error",n=>this.fireError(n))}write(e){try{return this.port.postMessage(e),Promise.resolve()}catch(n){return this.handleError(n,e),Promise.reject(n)}}handleError(e,n){this.errorCount++,this.fireError(e,n,this.errorCount)}end(){}};ue.PortMessageWriter=Gs;var Kt=class extends et.ReadableStreamMessageReader{static{i(this,"SocketMessageReader")}constructor(e,n="utf-8"){super((0,Rn.default)().stream.asReadableStream(e),n)}};ue.SocketMessageReader=Kt;var en=class extends et.WriteableStreamMessageWriter{static{i(this,"SocketMessageWriter")}constructor(e,n){super((0,Rn.default)().stream.asWritableStream(e),n),this.socket=e}dispose(){super.dispose(),this.socket.destroy()}};ue.SocketMessageWriter=en;var Ei=class extends et.ReadableStreamMessageReader{static{i(this,"StreamMessageReader")}constructor(e,n){super((0,Rn.default)().stream.asReadableStream(e),n)}};ue.StreamMessageReader=Ei;var Ti=class extends et.WriteableStreamMessageWriter{static{i(this,"StreamMessageWriter")}constructor(e,n){super((0,Rn.default)().stream.asWritableStream(e),n)}};ue.StreamMessageWriter=Ti;var Yu=process.env.XDG_RUNTIME_DIR,Jm=new Map([["linux",107],["darwin",103]]);function Xm(){let t=(0,Qm.randomBytes)(21).toString("hex");if(process.platform==="win32")return`\\\\.\\pipe\\vscode-jsonrpc-${t}-sock`;let e;Yu?e=Xu.join(Yu,`vscode-ipc-${t}.sock`):e=Xu.join(Zm.tmpdir(),`vscode-${t}.sock`);let n=Jm.get(process.platform);return n!==void 0&&e.length>n&&(0,Rn.default)().console.warn(`WARNING: IPC handle "${e}" is longer than ${n} characters.`),e}i(Xm,"generateRandomPipeName");ue.generateRandomPipeName=Xm;function Ym(t,e="utf-8"){let n,r=new Promise((o,s)=>{n=o});return new Promise((o,s)=>{let u=(0,Si.createServer)(l=>{u.close(),n([new Kt(l,e),new en(l,e)])});u.on("error",s),u.listen(t,()=>{u.removeListener("error",s),o({onConnected:i(()=>r,"onConnected")})})})}i(Ym,"createClientPipeTransport");ue.createClientPipeTransport=Ym;function Km(t,e="utf-8"){let n=(0,Si.createConnection)(t);return[new Kt(n,e),new en(n,e)]}i(Km,"createServerPipeTransport");ue.createServerPipeTransport=Km;function e_(t,e="utf-8"){let n,r=new Promise((o,s)=>{n=o});return new Promise((o,s)=>{let u=(0,Si.createServer)(l=>{u.close(),n([new Kt(l,e),new en(l,e)])});u.on("error",s),u.listen(t,"127.0.0.1",()=>{u.removeListener("error",s),o({onConnected:i(()=>r,"onConnected")})})})}i(e_,"createClientSocketTransport");ue.createClientSocketTransport=e_;function t_(t,e="utf-8"){let n=(0,Si.createConnection)(t,"127.0.0.1");return[new Kt(n,e),new en(n,e)]}i(t_,"createServerSocketTransport");ue.createServerSocketTransport=t_;function n_(t){let e=t;return e.read!==void 0&&e.addListener!==void 0}i(n_,"isReadableStream");function r_(t){let e=t;return e.write!==void 0&&e.addListener!==void 0}i(r_,"isWritableStream");function i_(t,e,n,r){n||(n=et.NullLogger);let o=n_(t)?new Ei(t):t,s=r_(e)?new Ti(e):e;return et.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,et.createMessageConnection)(o,s,n,r)}i(i_,"createMessageConnection");ue.createMessageConnection=i_});var Zs=Z((Lw,Ku)=>{"use strict";y();Ku.exports=tn()});var xi=Z((el,Ri)=>{y();(function(t){if(typeof Ri=="object"&&typeof Ri.exports=="object"){var e=t(require,el);e!==void 0&&(Ri.exports=e)}else typeof define=="function"&&define.amd&&define(["require","exports"],t)})(function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TextDocument=e.EOL=e.WorkspaceFolder=e.InlineCompletionContext=e.SelectedCompletionInfo=e.InlineCompletionTriggerKind=e.InlineCompletionList=e.InlineCompletionItem=e.StringValue=e.InlayHint=e.InlayHintLabelPart=e.InlayHintKind=e.InlineValueContext=e.InlineValueEvaluatableExpression=e.InlineValueVariableLookup=e.InlineValueText=e.SemanticTokens=e.SemanticTokenModifiers=e.SemanticTokenTypes=e.SelectionRange=e.DocumentLink=e.FormattingOptions=e.CodeLens=e.CodeAction=e.CodeActionContext=e.CodeActionTriggerKind=e.CodeActionKind=e.DocumentSymbol=e.WorkspaceSymbol=e.SymbolInformation=e.SymbolTag=e.SymbolKind=e.DocumentHighlight=e.DocumentHighlightKind=e.SignatureInformation=e.ParameterInformation=e.Hover=e.MarkedString=e.CompletionList=e.CompletionItem=e.CompletionItemLabelDetails=e.InsertTextMode=e.InsertReplaceEdit=e.CompletionItemTag=e.InsertTextFormat=e.CompletionItemKind=e.MarkupContent=e.MarkupKind=e.TextDocumentItem=e.OptionalVersionedTextDocumentIdentifier=e.VersionedTextDocumentIdentifier=e.TextDocumentIdentifier=e.WorkspaceChange=e.WorkspaceEdit=e.DeleteFile=e.RenameFile=e.CreateFile=e.TextDocumentEdit=e.AnnotatedTextEdit=e.ChangeAnnotationIdentifier=e.ChangeAnnotation=e.TextEdit=e.Command=e.Diagnostic=e.CodeDescription=e.DiagnosticTag=e.DiagnosticSeverity=e.DiagnosticRelatedInformation=e.FoldingRange=e.FoldingRangeKind=e.ColorPresentation=e.ColorInformation=e.Color=e.LocationLink=e.Location=e.Range=e.Position=e.uinteger=e.integer=e.URI=e.DocumentUri=void 0;var n;(function(c){function p(h){return typeof h=="string"}i(p,"is"),c.is=p})(n||(e.DocumentUri=n={}));var r;(function(c){function p(h){return typeof h=="string"}i(p,"is"),c.is=p})(r||(e.URI=r={}));var o;(function(c){c.MIN_VALUE=-2147483648,c.MAX_VALUE=2147483647;function p(h){return typeof h=="number"&&c.MIN_VALUE<=h&&h<=c.MAX_VALUE}i(p,"is"),c.is=p})(o||(e.integer=o={}));var s;(function(c){c.MIN_VALUE=0,c.MAX_VALUE=2147483647;function p(h){return typeof h=="number"&&c.MIN_VALUE<=h&&h<=c.MAX_VALUE}i(p,"is"),c.is=p})(s||(e.uinteger=s={}));var u;(function(c){function p(d,a){return d===Number.MAX_VALUE&&(d=s.MAX_VALUE),a===Number.MAX_VALUE&&(a=s.MAX_VALUE),{line:d,character:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&w.uinteger(a.line)&&w.uinteger(a.character)}i(h,"is"),c.is=h})(u||(e.Position=u={}));var l;(function(c){function p(d,a,b,N){if(w.uinteger(d)&&w.uinteger(a)&&w.uinteger(b)&&w.uinteger(N))return{start:u.create(d,a),end:u.create(b,N)};if(u.is(d)&&u.is(a))return{start:d,end:a};throw new Error("Range#create called with invalid arguments[".concat(d,", ").concat(a,", ").concat(b,", ").concat(N,"]"))}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&u.is(a.start)&&u.is(a.end)}i(h,"is"),c.is=h})(l||(e.Range=l={}));var f;(function(c){function p(d,a){return{uri:d,range:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&l.is(a.range)&&(w.string(a.uri)||w.undefined(a.uri))}i(h,"is"),c.is=h})(f||(e.Location=f={}));var _;(function(c){function p(d,a,b,N){return{targetUri:d,targetRange:a,targetSelectionRange:b,originSelectionRange:N}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&l.is(a.targetRange)&&w.string(a.targetUri)&&l.is(a.targetSelectionRange)&&(l.is(a.originSelectionRange)||w.undefined(a.originSelectionRange))}i(h,"is"),c.is=h})(_||(e.LocationLink=_={}));var v;(function(c){function p(d,a,b,N){return{red:d,green:a,blue:b,alpha:N}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&w.numberRange(a.red,0,1)&&w.numberRange(a.green,0,1)&&w.numberRange(a.blue,0,1)&&w.numberRange(a.alpha,0,1)}i(h,"is"),c.is=h})(v||(e.Color=v={}));var T;(function(c){function p(d,a){return{range:d,color:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&l.is(a.range)&&v.is(a.color)}i(h,"is"),c.is=h})(T||(e.ColorInformation=T={}));var M;(function(c){function p(d,a,b){return{label:d,textEdit:a,additionalTextEdits:b}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&w.string(a.label)&&(w.undefined(a.textEdit)||K.is(a))&&(w.undefined(a.additionalTextEdits)||w.typedArray(a.additionalTextEdits,K.is))}i(h,"is"),c.is=h})(M||(e.ColorPresentation=M={}));var D;(function(c){c.Comment="comment",c.Imports="imports",c.Region="region"})(D||(e.FoldingRangeKind=D={}));var W;(function(c){function p(d,a,b,N,ne,Re){var pe={startLine:d,endLine:a};return w.defined(b)&&(pe.startCharacter=b),w.defined(N)&&(pe.endCharacter=N),w.defined(ne)&&(pe.kind=ne),w.defined(Re)&&(pe.collapsedText=Re),pe}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&w.uinteger(a.startLine)&&w.uinteger(a.startLine)&&(w.undefined(a.startCharacter)||w.uinteger(a.startCharacter))&&(w.undefined(a.endCharacter)||w.uinteger(a.endCharacter))&&(w.undefined(a.kind)||w.string(a.kind))}i(h,"is"),c.is=h})(W||(e.FoldingRange=W={}));var F;(function(c){function p(d,a){return{location:d,message:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&f.is(a.location)&&w.string(a.message)}i(h,"is"),c.is=h})(F||(e.DiagnosticRelatedInformation=F={}));var P;(function(c){c.Error=1,c.Warning=2,c.Information=3,c.Hint=4})(P||(e.DiagnosticSeverity=P={}));var z;(function(c){c.Unnecessary=1,c.Deprecated=2})(z||(e.DiagnosticTag=z={}));var L;(function(c){function p(h){var d=h;return w.objectLiteral(d)&&w.string(d.href)}i(p,"is"),c.is=p})(L||(e.CodeDescription=L={}));var H;(function(c){function p(d,a,b,N,ne,Re){var pe={range:d,message:a};return w.defined(b)&&(pe.severity=b),w.defined(N)&&(pe.code=N),w.defined(ne)&&(pe.source=ne),w.defined(Re)&&(pe.relatedInformation=Re),pe}i(p,"create"),c.create=p;function h(d){var a,b=d;return w.defined(b)&&l.is(b.range)&&w.string(b.message)&&(w.number(b.severity)||w.undefined(b.severity))&&(w.integer(b.code)||w.string(b.code)||w.undefined(b.code))&&(w.undefined(b.codeDescription)||w.string((a=b.codeDescription)===null||a===void 0?void 0:a.href))&&(w.string(b.source)||w.undefined(b.source))&&(w.undefined(b.relatedInformation)||w.typedArray(b.relatedInformation,F.is))}i(h,"is"),c.is=h})(H||(e.Diagnostic=H={}));var Y;(function(c){function p(d,a){for(var b=[],N=2;N<arguments.length;N++)b[N-2]=arguments[N];var ne={title:d,command:a};return w.defined(b)&&b.length>0&&(ne.arguments=b),ne}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.string(a.title)&&w.string(a.command)}i(h,"is"),c.is=h})(Y||(e.Command=Y={}));var K;(function(c){function p(b,N){return{range:b,newText:N}}i(p,"replace"),c.replace=p;function h(b,N){return{range:{start:b,end:b},newText:N}}i(h,"insert"),c.insert=h;function d(b){return{range:b,newText:""}}i(d,"del"),c.del=d;function a(b){var N=b;return w.objectLiteral(N)&&w.string(N.newText)&&l.is(N.range)}i(a,"is"),c.is=a})(K||(e.TextEdit=K={}));var le;(function(c){function p(d,a,b){var N={label:d};return a!==void 0&&(N.needsConfirmation=a),b!==void 0&&(N.description=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&w.string(a.label)&&(w.boolean(a.needsConfirmation)||a.needsConfirmation===void 0)&&(w.string(a.description)||a.description===void 0)}i(h,"is"),c.is=h})(le||(e.ChangeAnnotation=le={}));var J;(function(c){function p(h){var d=h;return w.string(d)}i(p,"is"),c.is=p})(J||(e.ChangeAnnotationIdentifier=J={}));var ie;(function(c){function p(b,N,ne){return{range:b,newText:N,annotationId:ne}}i(p,"replace"),c.replace=p;function h(b,N,ne){return{range:{start:b,end:b},newText:N,annotationId:ne}}i(h,"insert"),c.insert=h;function d(b,N){return{range:b,newText:"",annotationId:N}}i(d,"del"),c.del=d;function a(b){var N=b;return K.is(N)&&(le.is(N.annotationId)||J.is(N.annotationId))}i(a,"is"),c.is=a})(ie||(e.AnnotatedTextEdit=ie={}));var Te;(function(c){function p(d,a){return{textDocument:d,edits:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&V.is(a.textDocument)&&Array.isArray(a.edits)}i(h,"is"),c.is=h})(Te||(e.TextDocumentEdit=Te={}));var Ce;(function(c){function p(d,a,b){var N={kind:"create",uri:d};return a!==void 0&&(a.overwrite!==void 0||a.ignoreIfExists!==void 0)&&(N.options=a),b!==void 0&&(N.annotationId=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return a&&a.kind==="create"&&w.string(a.uri)&&(a.options===void 0||(a.options.overwrite===void 0||w.boolean(a.options.overwrite))&&(a.options.ignoreIfExists===void 0||w.boolean(a.options.ignoreIfExists)))&&(a.annotationId===void 0||J.is(a.annotationId))}i(h,"is"),c.is=h})(Ce||(e.CreateFile=Ce={}));var A;(function(c){function p(d,a,b,N){var ne={kind:"rename",oldUri:d,newUri:a};return b!==void 0&&(b.overwrite!==void 0||b.ignoreIfExists!==void 0)&&(ne.options=b),N!==void 0&&(ne.annotationId=N),ne}i(p,"create"),c.create=p;function h(d){var a=d;return a&&a.kind==="rename"&&w.string(a.oldUri)&&w.string(a.newUri)&&(a.options===void 0||(a.options.overwrite===void 0||w.boolean(a.options.overwrite))&&(a.options.ignoreIfExists===void 0||w.boolean(a.options.ignoreIfExists)))&&(a.annotationId===void 0||J.is(a.annotationId))}i(h,"is"),c.is=h})(A||(e.RenameFile=A={}));var E;(function(c){function p(d,a,b){var N={kind:"delete",uri:d};return a!==void 0&&(a.recursive!==void 0||a.ignoreIfNotExists!==void 0)&&(N.options=a),b!==void 0&&(N.annotationId=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return a&&a.kind==="delete"&&w.string(a.uri)&&(a.options===void 0||(a.options.recursive===void 0||w.boolean(a.options.recursive))&&(a.options.ignoreIfNotExists===void 0||w.boolean(a.options.ignoreIfNotExists)))&&(a.annotationId===void 0||J.is(a.annotationId))}i(h,"is"),c.is=h})(E||(e.DeleteFile=E={}));var j;(function(c){function p(h){var d=h;return d&&(d.changes!==void 0||d.documentChanges!==void 0)&&(d.documentChanges===void 0||d.documentChanges.every(function(a){return w.string(a.kind)?Ce.is(a)||A.is(a)||E.is(a):Te.is(a)}))}i(p,"is"),c.is=p})(j||(e.WorkspaceEdit=j={}));var B=function(){function c(p,h){this.edits=p,this.changeAnnotations=h}return i(c,"TextEditChangeImpl"),c.prototype.insert=function(p,h,d){var a,b;if(d===void 0?a=K.insert(p,h):J.is(d)?(b=d,a=ie.insert(p,h,d)):(this.assertChangeAnnotations(this.changeAnnotations),b=this.changeAnnotations.manage(d),a=ie.insert(p,h,b)),this.edits.push(a),b!==void 0)return b},c.prototype.replace=function(p,h,d){var a,b;if(d===void 0?a=K.replace(p,h):J.is(d)?(b=d,a=ie.replace(p,h,d)):(this.assertChangeAnnotations(this.changeAnnotations),b=this.changeAnnotations.manage(d),a=ie.replace(p,h,b)),this.edits.push(a),b!==void 0)return b},c.prototype.delete=function(p,h){var d,a;if(h===void 0?d=K.del(p):J.is(h)?(a=h,d=ie.del(p,h)):(this.assertChangeAnnotations(this.changeAnnotations),a=this.changeAnnotations.manage(h),d=ie.del(p,a)),this.edits.push(d),a!==void 0)return a},c.prototype.add=function(p){this.edits.push(p)},c.prototype.all=function(){return this.edits},c.prototype.clear=function(){this.edits.splice(0,this.edits.length)},c.prototype.assertChangeAnnotations=function(p){if(p===void 0)throw new Error("Text edit change is not configured to manage change annotations.")},c}(),S=function(){function c(p){this._annotations=p===void 0?Object.create(null):p,this._counter=0,this._size=0}return i(c,"ChangeAnnotations"),c.prototype.all=function(){return this._annotations},Object.defineProperty(c.prototype,"size",{get:i(function(){return this._size},"get"),enumerable:!1,configurable:!0}),c.prototype.manage=function(p,h){var d;if(J.is(p)?d=p:(d=this.nextId(),h=p),this._annotations[d]!==void 0)throw new Error("Id ".concat(d," is already in use."));if(h===void 0)throw new Error("No annotation provided for id ".concat(d));return this._annotations[d]=h,this._size++,d},c.prototype.nextId=function(){return this._counter++,this._counter.toString()},c}(),I=function(){function c(p){var h=this;this._textEditChanges=Object.create(null),p!==void 0?(this._workspaceEdit=p,p.documentChanges?(this._changeAnnotations=new S(p.changeAnnotations),p.changeAnnotations=this._changeAnnotations.all(),p.documentChanges.forEach(function(d){if(Te.is(d)){var a=new B(d.edits,h._changeAnnotations);h._textEditChanges[d.textDocument.uri]=a}})):p.changes&&Object.keys(p.changes).forEach(function(d){var a=new B(p.changes[d]);h._textEditChanges[d]=a})):this._workspaceEdit={}}return i(c,"WorkspaceChange"),Object.defineProperty(c.prototype,"edit",{get:i(function(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},"get"),enumerable:!1,configurable:!0}),c.prototype.getTextEditChange=function(p){if(V.is(p)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var h={uri:p.uri,version:p.version},d=this._textEditChanges[h.uri];if(!d){var a=[],b={textDocument:h,edits:a};this._workspaceEdit.documentChanges.push(b),d=new B(a,this._changeAnnotations),this._textEditChanges[h.uri]=d}return d}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");var d=this._textEditChanges[p];if(!d){var a=[];this._workspaceEdit.changes[p]=a,d=new B(a),this._textEditChanges[p]=d}return d}},c.prototype.initDocumentChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new S,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},c.prototype.initChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))},c.prototype.createFile=function(p,h,d){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var a;le.is(h)||J.is(h)?a=h:d=h;var b,N;if(a===void 0?b=Ce.create(p,d):(N=J.is(a)?a:this._changeAnnotations.manage(a),b=Ce.create(p,d,N)),this._workspaceEdit.documentChanges.push(b),N!==void 0)return N},c.prototype.renameFile=function(p,h,d,a){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var b;le.is(d)||J.is(d)?b=d:a=d;var N,ne;if(b===void 0?N=A.create(p,h,a):(ne=J.is(b)?b:this._changeAnnotations.manage(b),N=A.create(p,h,a,ne)),this._workspaceEdit.documentChanges.push(N),ne!==void 0)return ne},c.prototype.deleteFile=function(p,h,d){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var a;le.is(h)||J.is(h)?a=h:d=h;var b,N;if(a===void 0?b=E.create(p,d):(N=J.is(a)?a:this._changeAnnotations.manage(a),b=E.create(p,d,N)),this._workspaceEdit.documentChanges.push(b),N!==void 0)return N},c}();e.WorkspaceChange=I;var k;(function(c){function p(d){return{uri:d}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.string(a.uri)}i(h,"is"),c.is=h})(k||(e.TextDocumentIdentifier=k={}));var $;(function(c){function p(d,a){return{uri:d,version:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.string(a.uri)&&w.integer(a.version)}i(h,"is"),c.is=h})($||(e.VersionedTextDocumentIdentifier=$={}));var V;(function(c){function p(d,a){return{uri:d,version:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.string(a.uri)&&(a.version===null||w.integer(a.version))}i(h,"is"),c.is=h})(V||(e.OptionalVersionedTextDocumentIdentifier=V={}));var U;(function(c){function p(d,a,b,N){return{uri:d,languageId:a,version:b,text:N}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.string(a.uri)&&w.string(a.languageId)&&w.integer(a.version)&&w.string(a.text)}i(h,"is"),c.is=h})(U||(e.TextDocumentItem=U={}));var ee;(function(c){c.PlainText="plaintext",c.Markdown="markdown";function p(h){var d=h;return d===c.PlainText||d===c.Markdown}i(p,"is"),c.is=p})(ee||(e.MarkupKind=ee={}));var te;(function(c){function p(h){var d=h;return w.objectLiteral(h)&&ee.is(d.kind)&&w.string(d.value)}i(p,"is"),c.is=p})(te||(e.MarkupContent=te={}));var Ie;(function(c){c.Text=1,c.Method=2,c.Function=3,c.Constructor=4,c.Field=5,c.Variable=6,c.Class=7,c.Interface=8,c.Module=9,c.Property=10,c.Unit=11,c.Value=12,c.Enum=13,c.Keyword=14,c.Snippet=15,c.Color=16,c.File=17,c.Reference=18,c.Folder=19,c.EnumMember=20,c.Constant=21,c.Struct=22,c.Event=23,c.Operator=24,c.TypeParameter=25})(Ie||(e.CompletionItemKind=Ie={}));var de;(function(c){c.PlainText=1,c.Snippet=2})(de||(e.InsertTextFormat=de={}));var ze;(function(c){c.Deprecated=1})(ze||(e.CompletionItemTag=ze={}));var mt;(function(c){function p(d,a,b){return{newText:d,insert:a,replace:b}}i(p,"create"),c.create=p;function h(d){var a=d;return a&&w.string(a.newText)&&l.is(a.insert)&&l.is(a.replace)}i(h,"is"),c.is=h})(mt||(e.InsertReplaceEdit=mt={}));var lr;(function(c){c.asIs=1,c.adjustIndentation=2})(lr||(e.InsertTextMode=lr={}));var cr;(function(c){function p(h){var d=h;return d&&(w.string(d.detail)||d.detail===void 0)&&(w.string(d.description)||d.description===void 0)}i(p,"is"),c.is=p})(cr||(e.CompletionItemLabelDetails=cr={}));var dr;(function(c){function p(h){return{label:h}}i(p,"create"),c.create=p})(dr||(e.CompletionItem=dr={}));var fr;(function(c){function p(h,d){return{items:h||[],isIncomplete:!!d}}i(p,"create"),c.create=p})(fr||(e.CompletionList=fr={}));var on;(function(c){function p(d){return d.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}i(p,"fromPlainText"),c.fromPlainText=p;function h(d){var a=d;return w.string(a)||w.objectLiteral(a)&&w.string(a.language)&&w.string(a.value)}i(h,"is"),c.is=h})(on||(e.MarkedString=on={}));var st;(function(c){function p(h){var d=h;return!!d&&w.objectLiteral(d)&&(te.is(d.contents)||on.is(d.contents)||w.typedArray(d.contents,on.is))&&(h.range===void 0||l.is(h.range))}i(p,"is"),c.is=p})(st||(e.Hover=st={}));var mr;(function(c){function p(h,d){return d?{label:h,documentation:d}:{label:h}}i(p,"create"),c.create=p})(mr||(e.ParameterInformation=mr={}));var _r;(function(c){function p(h,d){for(var a=[],b=2;b<arguments.length;b++)a[b-2]=arguments[b];var N={label:h};return w.defined(d)&&(N.documentation=d),w.defined(a)?N.parameters=a:N.parameters=[],N}i(p,"create"),c.create=p})(_r||(e.SignatureInformation=_r={}));var $t;(function(c){c.Text=1,c.Read=2,c.Write=3})($t||(e.DocumentHighlightKind=$t={}));var gr;(function(c){function p(h,d){var a={range:h};return w.number(d)&&(a.kind=d),a}i(p,"create"),c.create=p})(gr||(e.DocumentHighlight=gr={}));var sn;(function(c){c.File=1,c.Module=2,c.Namespace=3,c.Package=4,c.Class=5,c.Method=6,c.Property=7,c.Field=8,c.Constructor=9,c.Enum=10,c.Interface=11,c.Function=12,c.Variable=13,c.Constant=14,c.String=15,c.Number=16,c.Boolean=17,c.Array=18,c.Object=19,c.Key=20,c.Null=21,c.EnumMember=22,c.Struct=23,c.Event=24,c.Operator=25,c.TypeParameter=26})(sn||(e.SymbolKind=sn={}));var pr;(function(c){c.Deprecated=1})(pr||(e.SymbolTag=pr={}));var wt;(function(c){function p(h,d,a,b,N){var ne={name:h,kind:d,location:{uri:b,range:a}};return N&&(ne.containerName=N),ne}i(p,"create"),c.create=p})(wt||(e.SymbolInformation=wt={}));var Nt;(function(c){function p(h,d,a,b){return b!==void 0?{name:h,kind:d,location:{uri:a,range:b}}:{name:h,kind:d,location:{uri:a}}}i(p,"create"),c.create=p})(Nt||(e.WorkspaceSymbol=Nt={}));var hr;(function(c){function p(d,a,b,N,ne,Re){var pe={name:d,detail:a,kind:b,range:N,selectionRange:ne};return Re!==void 0&&(pe.children=Re),pe}i(p,"create"),c.create=p;function h(d){var a=d;return a&&w.string(a.name)&&w.number(a.kind)&&l.is(a.range)&&l.is(a.selectionRange)&&(a.detail===void 0||w.string(a.detail))&&(a.deprecated===void 0||w.boolean(a.deprecated))&&(a.children===void 0||Array.isArray(a.children))&&(a.tags===void 0||Array.isArray(a.tags))}i(h,"is"),c.is=h})(hr||(e.DocumentSymbol=hr={}));var yr;(function(c){c.Empty="",c.QuickFix="quickfix",c.Refactor="refactor",c.RefactorExtract="refactor.extract",c.RefactorInline="refactor.inline",c.RefactorRewrite="refactor.rewrite",c.Source="source",c.SourceOrganizeImports="source.organizeImports",c.SourceFixAll="source.fixAll"})(yr||(e.CodeActionKind=yr={}));var _t;(function(c){c.Invoked=1,c.Automatic=2})(_t||(e.CodeActionTriggerKind=_t={}));var Fn;(function(c){function p(d,a,b){var N={diagnostics:d};return a!=null&&(N.only=a),b!=null&&(N.triggerKind=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.typedArray(a.diagnostics,H.is)&&(a.only===void 0||w.typedArray(a.only,w.string))&&(a.triggerKind===void 0||a.triggerKind===_t.Invoked||a.triggerKind===_t.Automatic)}i(h,"is"),c.is=h})(Fn||(e.CodeActionContext=Fn={}));var qn;(function(c){function p(d,a,b){var N={title:d},ne=!0;return typeof a=="string"?(ne=!1,N.kind=a):Y.is(a)?N.command=a:N.edit=a,ne&&b!==void 0&&(N.kind=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return a&&w.string(a.title)&&(a.diagnostics===void 0||w.typedArray(a.diagnostics,H.is))&&(a.kind===void 0||w.string(a.kind))&&(a.edit!==void 0||a.command!==void 0)&&(a.command===void 0||Y.is(a.command))&&(a.isPreferred===void 0||w.boolean(a.isPreferred))&&(a.edit===void 0||j.is(a.edit))}i(h,"is"),c.is=h})(qn||(e.CodeAction=qn={}));var an;(function(c){function p(d,a){var b={range:d};return w.defined(a)&&(b.data=a),b}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&l.is(a.range)&&(w.undefined(a.command)||Y.is(a.command))}i(h,"is"),c.is=h})(an||(e.CodeLens=an={}));var jn;(function(c){function p(d,a){return{tabSize:d,insertSpaces:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&w.uinteger(a.tabSize)&&w.boolean(a.insertSpaces)}i(h,"is"),c.is=h})(jn||(e.FormattingOptions=jn={}));var vt;(function(c){function p(d,a,b){return{range:d,target:a,data:b}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&l.is(a.range)&&(w.undefined(a.target)||w.string(a.target))}i(h,"is"),c.is=h})(vt||(e.DocumentLink=vt={}));var g;(function(c){function p(d,a){return{range:d,parent:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&l.is(a.range)&&(a.parent===void 0||c.is(a.parent))}i(h,"is"),c.is=h})(g||(e.SelectionRange=g={}));var x;(function(c){c.namespace="namespace",c.type="type",c.class="class",c.enum="enum",c.interface="interface",c.struct="struct",c.typeParameter="typeParameter",c.parameter="parameter",c.variable="variable",c.property="property",c.enumMember="enumMember",c.event="event",c.function="function",c.method="method",c.macro="macro",c.keyword="keyword",c.modifier="modifier",c.comment="comment",c.string="string",c.number="number",c.regexp="regexp",c.operator="operator",c.decorator="decorator"})(x||(e.SemanticTokenTypes=x={}));var q;(function(c){c.declaration="declaration",c.definition="definition",c.readonly="readonly",c.static="static",c.deprecated="deprecated",c.abstract="abstract",c.async="async",c.modification="modification",c.documentation="documentation",c.defaultLibrary="defaultLibrary"})(q||(e.SemanticTokenModifiers=q={}));var G;(function(c){function p(h){var d=h;return w.objectLiteral(d)&&(d.resultId===void 0||typeof d.resultId=="string")&&Array.isArray(d.data)&&(d.data.length===0||typeof d.data[0]=="number")}i(p,"is"),c.is=p})(G||(e.SemanticTokens=G={}));var _e;(function(c){function p(d,a){return{range:d,text:a}}i(p,"create"),c.create=p;function h(d){var a=d;return a!=null&&l.is(a.range)&&w.string(a.text)}i(h,"is"),c.is=h})(_e||(e.InlineValueText=_e={}));var me;(function(c){function p(d,a,b){return{range:d,variableName:a,caseSensitiveLookup:b}}i(p,"create"),c.create=p;function h(d){var a=d;return a!=null&&l.is(a.range)&&w.boolean(a.caseSensitiveLookup)&&(w.string(a.variableName)||a.variableName===void 0)}i(h,"is"),c.is=h})(me||(e.InlineValueVariableLookup=me={}));var ye;(function(c){function p(d,a){return{range:d,expression:a}}i(p,"create"),c.create=p;function h(d){var a=d;return a!=null&&l.is(a.range)&&(w.string(a.expression)||a.expression===void 0)}i(h,"is"),c.is=h})(ye||(e.InlineValueEvaluatableExpression=ye={}));var we;(function(c){function p(d,a){return{frameId:d,stoppedLocation:a}}i(p,"create"),c.create=p;function h(d){var a=d;return w.defined(a)&&l.is(d.stoppedLocation)}i(h,"is"),c.is=h})(we||(e.InlineValueContext=we={}));var oe;(function(c){c.Type=1,c.Parameter=2;function p(h){return h===1||h===2}i(p,"is"),c.is=p})(oe||(e.InlayHintKind=oe={}));var ge;(function(c){function p(d){return{value:d}}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&(a.tooltip===void 0||w.string(a.tooltip)||te.is(a.tooltip))&&(a.location===void 0||f.is(a.location))&&(a.command===void 0||Y.is(a.command))}i(h,"is"),c.is=h})(ge||(e.InlayHintLabelPart=ge={}));var ce;(function(c){function p(d,a,b){var N={position:d,label:a};return b!==void 0&&(N.kind=b),N}i(p,"create"),c.create=p;function h(d){var a=d;return w.objectLiteral(a)&&u.is(a.position)&&(w.string(a.label)||w.typedArray(a.label,ge.is))&&(a.kind===void 0||oe.is(a.kind))&&a.textEdits===void 0||w.typedArray(a.textEdits,K.is)&&(a.tooltip===void 0||w.string(a.tooltip)||te.is(a.tooltip))&&(a.paddingLeft===void 0||w.boolean(a.paddingLeft))&&(a.paddingRight===void 0||w.boolean(a.paddingRight))}i(h,"is"),c.is=h})(ce||(e.InlayHint=ce={}));var Se;(function(c){function p(h){return{kind:"snippet",value:h}}i(p,"createSnippet"),c.createSnippet=p})(Se||(e.StringValue=Se={}));var Le;(function(c){function p(h,d,a,b){return{insertText:h,filterText:d,range:a,command:b}}i(p,"create"),c.create=p})(Le||(e.InlineCompletionItem=Le={}));var Xe;(function(c){function p(h){return{items:h}}i(p,"create"),c.create=p})(Xe||(e.InlineCompletionList=Xe={}));var br;(function(c){c.Invoked=0,c.Automatic=1})(br||(e.InlineCompletionTriggerKind=br={}));var da;(function(c){function p(h,d){return{range:h,text:d}}i(p,"create"),c.create=p})(da||(e.SelectedCompletionInfo=da={}));var fa;(function(c){function p(h,d){return{triggerKind:h,selectedCompletionInfo:d}}i(p,"create"),c.create=p})(fa||(e.InlineCompletionContext=fa={}));var ma;(function(c){function p(h){var d=h;return w.objectLiteral(d)&&r.is(d.uri)&&w.string(d.name)}i(p,"is"),c.is=p})(ma||(e.WorkspaceFolder=ma={})),e.EOL=[`
`,`\r
`,"\r"];var _a;(function(c){function p(b,N,ne,Re){return new tf(b,N,ne,Re)}i(p,"create"),c.create=p;function h(b){var N=b;return!!(w.defined(N)&&w.string(N.uri)&&(w.undefined(N.languageId)||w.string(N.languageId))&&w.uinteger(N.lineCount)&&w.func(N.getText)&&w.func(N.positionAt)&&w.func(N.offsetAt))}i(h,"is"),c.is=h;function d(b,N){for(var ne=b.getText(),Re=a(N,function(un,wr){var ga=un.range.start.line-wr.range.start.line;return ga===0?un.range.start.character-wr.range.start.character:ga}),pe=ne.length,at=Re.length-1;at>=0;at--){var ut=Re[at],Et=b.offsetAt(ut.range.start),ae=b.offsetAt(ut.range.end);if(ae<=pe)ne=ne.substring(0,Et)+ut.newText+ne.substring(ae,ne.length);else throw new Error("Overlapping edit");pe=Et}return ne}i(d,"applyEdits"),c.applyEdits=d;function a(b,N){if(b.length<=1)return b;var ne=b.length/2|0,Re=b.slice(0,ne),pe=b.slice(ne);a(Re,N),a(pe,N);for(var at=0,ut=0,Et=0;at<Re.length&&ut<pe.length;){var ae=N(Re[at],pe[ut]);ae<=0?b[Et++]=Re[at++]:b[Et++]=pe[ut++]}for(;at<Re.length;)b[Et++]=Re[at++];for(;ut<pe.length;)b[Et++]=pe[ut++];return b}i(a,"mergeSort")})(_a||(e.TextDocument=_a={}));var tf=function(){function c(p,h,d,a){this._uri=p,this._languageId=h,this._version=d,this._content=a,this._lineOffsets=void 0}return i(c,"FullTextDocument"),Object.defineProperty(c.prototype,"uri",{get:i(function(){return this._uri},"get"),enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"languageId",{get:i(function(){return this._languageId},"get"),enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"version",{get:i(function(){return this._version},"get"),enumerable:!1,configurable:!0}),c.prototype.getText=function(p){if(p){var h=this.offsetAt(p.start),d=this.offsetAt(p.end);return this._content.substring(h,d)}return this._content},c.prototype.update=function(p,h){this._content=p.text,this._version=h,this._lineOffsets=void 0},c.prototype.getLineOffsets=function(){if(this._lineOffsets===void 0){for(var p=[],h=this._content,d=!0,a=0;a<h.length;a++){d&&(p.push(a),d=!1);var b=h.charAt(a);d=b==="\r"||b===`
`,b==="\r"&&a+1<h.length&&h.charAt(a+1)===`
`&&a++}d&&h.length>0&&p.push(h.length),this._lineOffsets=p}return this._lineOffsets},c.prototype.positionAt=function(p){p=Math.max(Math.min(p,this._content.length),0);var h=this.getLineOffsets(),d=0,a=h.length;if(a===0)return u.create(0,p);for(;d<a;){var b=Math.floor((d+a)/2);h[b]>p?a=b:d=b+1}var N=d-1;return u.create(N,p-h[N])},c.prototype.offsetAt=function(p){var h=this.getLineOffsets();if(p.line>=h.length)return this._content.length;if(p.line<0)return 0;var d=h[p.line],a=p.line+1<h.length?h[p.line+1]:this._content.length;return Math.max(Math.min(d+p.character,a),d)},Object.defineProperty(c.prototype,"lineCount",{get:i(function(){return this.getLineOffsets().length},"get"),enumerable:!1,configurable:!0}),c}(),w;(function(c){var p=Object.prototype.toString;function h(ae){return typeof ae<"u"}i(h,"defined"),c.defined=h;function d(ae){return typeof ae>"u"}i(d,"undefined"),c.undefined=d;function a(ae){return ae===!0||ae===!1}i(a,"boolean"),c.boolean=a;function b(ae){return p.call(ae)==="[object String]"}i(b,"string"),c.string=b;function N(ae){return p.call(ae)==="[object Number]"}i(N,"number"),c.number=N;function ne(ae,un,wr){return p.call(ae)==="[object Number]"&&un<=ae&&ae<=wr}i(ne,"numberRange"),c.numberRange=ne;function Re(ae){return p.call(ae)==="[object Number]"&&-2147483648<=ae&&ae<=2147483647}i(Re,"integer"),c.integer=Re;function pe(ae){return p.call(ae)==="[object Number]"&&0<=ae&&ae<=2147483647}i(pe,"uinteger"),c.uinteger=pe;function at(ae){return p.call(ae)==="[object Function]"}i(at,"func"),c.func=at;function ut(ae){return ae!==null&&typeof ae=="object"}i(ut,"objectLiteral"),c.objectLiteral=ut;function Et(ae,un){return Array.isArray(ae)&&ae.every(un)}i(Et,"typedArray"),c.typedArray=Et})(w||(w={}))})});var ve=Z(Ze=>{"use strict";y();Object.defineProperty(Ze,"__esModule",{value:!0});Ze.ProtocolNotificationType=Ze.ProtocolNotificationType0=Ze.ProtocolRequestType=Ze.ProtocolRequestType0=Ze.RegistrationType=Ze.MessageDirection=void 0;var xn=tn(),tl;(function(t){t.clientToServer="clientToServer",t.serverToClient="serverToClient",t.both="both"})(tl||(Ze.MessageDirection=tl={}));var Qs=class{static{i(this,"RegistrationType")}constructor(e){this.method=e}};Ze.RegistrationType=Qs;var Js=class extends xn.RequestType0{static{i(this,"ProtocolRequestType0")}constructor(e){super(e)}};Ze.ProtocolRequestType0=Js;var Xs=class extends xn.RequestType{static{i(this,"ProtocolRequestType")}constructor(e){super(e,xn.ParameterStructures.byName)}};Ze.ProtocolRequestType=Xs;var Ys=class extends xn.NotificationType0{static{i(this,"ProtocolNotificationType0")}constructor(e){super(e)}};Ze.ProtocolNotificationType0=Ys;var Ks=class extends xn.NotificationType{static{i(this,"ProtocolNotificationType")}constructor(e){super(e,xn.ParameterStructures.byName)}};Ze.ProtocolNotificationType=Ks});var Ii=Z(Pe=>{"use strict";y();Object.defineProperty(Pe,"__esModule",{value:!0});Pe.objectLiteral=Pe.typedArray=Pe.stringArray=Pe.array=Pe.func=Pe.error=Pe.number=Pe.string=Pe.boolean=void 0;function o_(t){return t===!0||t===!1}i(o_,"boolean");Pe.boolean=o_;function nl(t){return typeof t=="string"||t instanceof String}i(nl,"string");Pe.string=nl;function s_(t){return typeof t=="number"||t instanceof Number}i(s_,"number");Pe.number=s_;function a_(t){return t instanceof Error}i(a_,"error");Pe.error=a_;function u_(t){return typeof t=="function"}i(u_,"func");Pe.func=u_;function rl(t){return Array.isArray(t)}i(rl,"array");Pe.array=rl;function l_(t){return rl(t)&&t.every(e=>nl(e))}i(l_,"stringArray");Pe.stringArray=l_;function c_(t,e){return Array.isArray(t)&&t.every(e)}i(c_,"typedArray");Pe.typedArray=c_;function d_(t){return t!==null&&typeof t=="object"}i(d_,"objectLiteral");Pe.objectLiteral=d_});var sl=Z(Ni=>{"use strict";y();Object.defineProperty(Ni,"__esModule",{value:!0});Ni.ImplementationRequest=void 0;var il=ve(),ol;(function(t){t.method="textDocument/implementation",t.messageDirection=il.MessageDirection.clientToServer,t.type=new il.ProtocolRequestType(t.method)})(ol||(Ni.ImplementationRequest=ol={}))});var ll=Z(Mi=>{"use strict";y();Object.defineProperty(Mi,"__esModule",{value:!0});Mi.TypeDefinitionRequest=void 0;var al=ve(),ul;(function(t){t.method="textDocument/typeDefinition",t.messageDirection=al.MessageDirection.clientToServer,t.type=new al.ProtocolRequestType(t.method)})(ul||(Mi.TypeDefinitionRequest=ul={}))});var fl=Z(In=>{"use strict";y();Object.defineProperty(In,"__esModule",{value:!0});In.DidChangeWorkspaceFoldersNotification=In.WorkspaceFoldersRequest=void 0;var ki=ve(),cl;(function(t){t.method="workspace/workspaceFolders",t.messageDirection=ki.MessageDirection.serverToClient,t.type=new ki.ProtocolRequestType0(t.method)})(cl||(In.WorkspaceFoldersRequest=cl={}));var dl;(function(t){t.method="workspace/didChangeWorkspaceFolders",t.messageDirection=ki.MessageDirection.clientToServer,t.type=new ki.ProtocolNotificationType(t.method)})(dl||(In.DidChangeWorkspaceFoldersNotification=dl={}))});var gl=Z(Pi=>{"use strict";y();Object.defineProperty(Pi,"__esModule",{value:!0});Pi.ConfigurationRequest=void 0;var ml=ve(),_l;(function(t){t.method="workspace/configuration",t.messageDirection=ml.MessageDirection.serverToClient,t.type=new ml.ProtocolRequestType(t.method)})(_l||(Pi.ConfigurationRequest=_l={}))});var yl=Z(Nn=>{"use strict";y();Object.defineProperty(Nn,"__esModule",{value:!0});Nn.ColorPresentationRequest=Nn.DocumentColorRequest=void 0;var Ci=ve(),pl;(function(t){t.method="textDocument/documentColor",t.messageDirection=Ci.MessageDirection.clientToServer,t.type=new Ci.ProtocolRequestType(t.method)})(pl||(Nn.DocumentColorRequest=pl={}));var hl;(function(t){t.method="textDocument/colorPresentation",t.messageDirection=Ci.MessageDirection.clientToServer,t.type=new Ci.ProtocolRequestType(t.method)})(hl||(Nn.ColorPresentationRequest=hl={}))});var vl=Z(Mn=>{"use strict";y();Object.defineProperty(Mn,"__esModule",{value:!0});Mn.FoldingRangeRefreshRequest=Mn.FoldingRangeRequest=void 0;var Li=ve(),bl;(function(t){t.method="textDocument/foldingRange",t.messageDirection=Li.MessageDirection.clientToServer,t.type=new Li.ProtocolRequestType(t.method)})(bl||(Mn.FoldingRangeRequest=bl={}));var wl;(function(t){t.method="workspace/foldingRange/refresh",t.messageDirection=Li.MessageDirection.serverToClient,t.type=new Li.ProtocolRequestType0(t.method)})(wl||(Mn.FoldingRangeRefreshRequest=wl={}))});var Sl=Z(Di=>{"use strict";y();Object.defineProperty(Di,"__esModule",{value:!0});Di.DeclarationRequest=void 0;var El=ve(),Tl;(function(t){t.method="textDocument/declaration",t.messageDirection=El.MessageDirection.clientToServer,t.type=new El.ProtocolRequestType(t.method)})(Tl||(Di.DeclarationRequest=Tl={}))});var Il=Z(Ai=>{"use strict";y();Object.defineProperty(Ai,"__esModule",{value:!0});Ai.SelectionRangeRequest=void 0;var Rl=ve(),xl;(function(t){t.method="textDocument/selectionRange",t.messageDirection=Rl.MessageDirection.clientToServer,t.type=new Rl.ProtocolRequestType(t.method)})(xl||(Ai.SelectionRangeRequest=xl={}))});var Pl=Z(qt=>{"use strict";y();Object.defineProperty(qt,"__esModule",{value:!0});qt.WorkDoneProgressCancelNotification=qt.WorkDoneProgressCreateRequest=qt.WorkDoneProgress=void 0;var f_=tn(),Oi=ve(),Nl;(function(t){t.type=new f_.ProgressType;function e(n){return n===t.type}i(e,"is"),t.is=e})(Nl||(qt.WorkDoneProgress=Nl={}));var Ml;(function(t){t.method="window/workDoneProgress/create",t.messageDirection=Oi.MessageDirection.serverToClient,t.type=new Oi.ProtocolRequestType(t.method)})(Ml||(qt.WorkDoneProgressCreateRequest=Ml={}));var kl;(function(t){t.method="window/workDoneProgress/cancel",t.messageDirection=Oi.MessageDirection.clientToServer,t.type=new Oi.ProtocolNotificationType(t.method)})(kl||(qt.WorkDoneProgressCancelNotification=kl={}))});var Al=Z(jt=>{"use strict";y();Object.defineProperty(jt,"__esModule",{value:!0});jt.CallHierarchyOutgoingCallsRequest=jt.CallHierarchyIncomingCallsRequest=jt.CallHierarchyPrepareRequest=void 0;var kn=ve(),Cl;(function(t){t.method="textDocument/prepareCallHierarchy",t.messageDirection=kn.MessageDirection.clientToServer,t.type=new kn.ProtocolRequestType(t.method)})(Cl||(jt.CallHierarchyPrepareRequest=Cl={}));var Ll;(function(t){t.method="callHierarchy/incomingCalls",t.messageDirection=kn.MessageDirection.clientToServer,t.type=new kn.ProtocolRequestType(t.method)})(Ll||(jt.CallHierarchyIncomingCallsRequest=Ll={}));var Dl;(function(t){t.method="callHierarchy/outgoingCalls",t.messageDirection=kn.MessageDirection.clientToServer,t.type=new kn.ProtocolRequestType(t.method)})(Dl||(jt.CallHierarchyOutgoingCallsRequest=Dl={}))});var Wl=Z(Qe=>{"use strict";y();Object.defineProperty(Qe,"__esModule",{value:!0});Qe.SemanticTokensRefreshRequest=Qe.SemanticTokensRangeRequest=Qe.SemanticTokensDeltaRequest=Qe.SemanticTokensRequest=Qe.SemanticTokensRegistrationType=Qe.TokenFormat=void 0;var It=ve(),Ol;(function(t){t.Relative="relative"})(Ol||(Qe.TokenFormat=Ol={}));var sr;(function(t){t.method="textDocument/semanticTokens",t.type=new It.RegistrationType(t.method)})(sr||(Qe.SemanticTokensRegistrationType=sr={}));var Fl;(function(t){t.method="textDocument/semanticTokens/full",t.messageDirection=It.MessageDirection.clientToServer,t.type=new It.ProtocolRequestType(t.method),t.registrationMethod=sr.method})(Fl||(Qe.SemanticTokensRequest=Fl={}));var ql;(function(t){t.method="textDocument/semanticTokens/full/delta",t.messageDirection=It.MessageDirection.clientToServer,t.type=new It.ProtocolRequestType(t.method),t.registrationMethod=sr.method})(ql||(Qe.SemanticTokensDeltaRequest=ql={}));var jl;(function(t){t.method="textDocument/semanticTokens/range",t.messageDirection=It.MessageDirection.clientToServer,t.type=new It.ProtocolRequestType(t.method),t.registrationMethod=sr.method})(jl||(Qe.SemanticTokensRangeRequest=jl={}));var Ul;(function(t){t.method="workspace/semanticTokens/refresh",t.messageDirection=It.MessageDirection.serverToClient,t.type=new It.ProtocolRequestType0(t.method)})(Ul||(Qe.SemanticTokensRefreshRequest=Ul={}))});var zl=Z(Fi=>{"use strict";y();Object.defineProperty(Fi,"__esModule",{value:!0});Fi.ShowDocumentRequest=void 0;var Bl=ve(),$l;(function(t){t.method="window/showDocument",t.messageDirection=Bl.MessageDirection.serverToClient,t.type=new Bl.ProtocolRequestType(t.method)})($l||(Fi.ShowDocumentRequest=$l={}))});var Gl=Z(qi=>{"use strict";y();Object.defineProperty(qi,"__esModule",{value:!0});qi.LinkedEditingRangeRequest=void 0;var Hl=ve(),Vl;(function(t){t.method="textDocument/linkedEditingRange",t.messageDirection=Hl.MessageDirection.clientToServer,t.type=new Hl.ProtocolRequestType(t.method)})(Vl||(qi.LinkedEditingRangeRequest=Vl={}))});var tc=Z(Ue=>{"use strict";y();Object.defineProperty(Ue,"__esModule",{value:!0});Ue.WillDeleteFilesRequest=Ue.DidDeleteFilesNotification=Ue.DidRenameFilesNotification=Ue.WillRenameFilesRequest=Ue.DidCreateFilesNotification=Ue.WillCreateFilesRequest=Ue.FileOperationPatternKind=void 0;var it=ve(),Zl;(function(t){t.file="file",t.folder="folder"})(Zl||(Ue.FileOperationPatternKind=Zl={}));var Ql;(function(t){t.method="workspace/willCreateFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolRequestType(t.method)})(Ql||(Ue.WillCreateFilesRequest=Ql={}));var Jl;(function(t){t.method="workspace/didCreateFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolNotificationType(t.method)})(Jl||(Ue.DidCreateFilesNotification=Jl={}));var Xl;(function(t){t.method="workspace/willRenameFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolRequestType(t.method)})(Xl||(Ue.WillRenameFilesRequest=Xl={}));var Yl;(function(t){t.method="workspace/didRenameFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolNotificationType(t.method)})(Yl||(Ue.DidRenameFilesNotification=Yl={}));var Kl;(function(t){t.method="workspace/didDeleteFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolNotificationType(t.method)})(Kl||(Ue.DidDeleteFilesNotification=Kl={}));var ec;(function(t){t.method="workspace/willDeleteFiles",t.messageDirection=it.MessageDirection.clientToServer,t.type=new it.ProtocolRequestType(t.method)})(ec||(Ue.WillDeleteFilesRequest=ec={}))});var sc=Z(Ut=>{"use strict";y();Object.defineProperty(Ut,"__esModule",{value:!0});Ut.MonikerRequest=Ut.MonikerKind=Ut.UniquenessLevel=void 0;var nc=ve(),rc;(function(t){t.document="document",t.project="project",t.group="group",t.scheme="scheme",t.global="global"})(rc||(Ut.UniquenessLevel=rc={}));var ic;(function(t){t.$import="import",t.$export="export",t.local="local"})(ic||(Ut.MonikerKind=ic={}));var oc;(function(t){t.method="textDocument/moniker",t.messageDirection=nc.MessageDirection.clientToServer,t.type=new nc.ProtocolRequestType(t.method)})(oc||(Ut.MonikerRequest=oc={}))});var cc=Z(Wt=>{"use strict";y();Object.defineProperty(Wt,"__esModule",{value:!0});Wt.TypeHierarchySubtypesRequest=Wt.TypeHierarchySupertypesRequest=Wt.TypeHierarchyPrepareRequest=void 0;var Pn=ve(),ac;(function(t){t.method="textDocument/prepareTypeHierarchy",t.messageDirection=Pn.MessageDirection.clientToServer,t.type=new Pn.ProtocolRequestType(t.method)})(ac||(Wt.TypeHierarchyPrepareRequest=ac={}));var uc;(function(t){t.method="typeHierarchy/supertypes",t.messageDirection=Pn.MessageDirection.clientToServer,t.type=new Pn.ProtocolRequestType(t.method)})(uc||(Wt.TypeHierarchySupertypesRequest=uc={}));var lc;(function(t){t.method="typeHierarchy/subtypes",t.messageDirection=Pn.MessageDirection.clientToServer,t.type=new Pn.ProtocolRequestType(t.method)})(lc||(Wt.TypeHierarchySubtypesRequest=lc={}))});var mc=Z(Cn=>{"use strict";y();Object.defineProperty(Cn,"__esModule",{value:!0});Cn.InlineValueRefreshRequest=Cn.InlineValueRequest=void 0;var ji=ve(),dc;(function(t){t.method="textDocument/inlineValue",t.messageDirection=ji.MessageDirection.clientToServer,t.type=new ji.ProtocolRequestType(t.method)})(dc||(Cn.InlineValueRequest=dc={}));var fc;(function(t){t.method="workspace/inlineValue/refresh",t.messageDirection=ji.MessageDirection.serverToClient,t.type=new ji.ProtocolRequestType0(t.method)})(fc||(Cn.InlineValueRefreshRequest=fc={}))});var hc=Z(Bt=>{"use strict";y();Object.defineProperty(Bt,"__esModule",{value:!0});Bt.InlayHintRefreshRequest=Bt.InlayHintResolveRequest=Bt.InlayHintRequest=void 0;var Ln=ve(),_c;(function(t){t.method="textDocument/inlayHint",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolRequestType(t.method)})(_c||(Bt.InlayHintRequest=_c={}));var gc;(function(t){t.method="inlayHint/resolve",t.messageDirection=Ln.MessageDirection.clientToServer,t.type=new Ln.ProtocolRequestType(t.method)})(gc||(Bt.InlayHintResolveRequest=gc={}));var pc;(function(t){t.method="workspace/inlayHint/refresh",t.messageDirection=Ln.MessageDirection.serverToClient,t.type=new Ln.ProtocolRequestType0(t.method)})(pc||(Bt.InlayHintRefreshRequest=pc={}))});var Sc=Z(ot=>{"use strict";y();Object.defineProperty(ot,"__esModule",{value:!0});ot.DiagnosticRefreshRequest=ot.WorkspaceDiagnosticRequest=ot.DocumentDiagnosticRequest=ot.DocumentDiagnosticReportKind=ot.DiagnosticServerCancellationData=void 0;var Tc=tn(),m_=Ii(),Dn=ve(),yc;(function(t){function e(n){let r=n;return r&&m_.boolean(r.retriggerRequest)}i(e,"is"),t.is=e})(yc||(ot.DiagnosticServerCancellationData=yc={}));var bc;(function(t){t.Full="full",t.Unchanged="unchanged"})(bc||(ot.DocumentDiagnosticReportKind=bc={}));var wc;(function(t){t.method="textDocument/diagnostic",t.messageDirection=Dn.MessageDirection.clientToServer,t.type=new Dn.ProtocolRequestType(t.method),t.partialResult=new Tc.ProgressType})(wc||(ot.DocumentDiagnosticRequest=wc={}));var vc;(function(t){t.method="workspace/diagnostic",t.messageDirection=Dn.MessageDirection.clientToServer,t.type=new Dn.ProtocolRequestType(t.method),t.partialResult=new Tc.ProgressType})(vc||(ot.WorkspaceDiagnosticRequest=vc={}));var Ec;(function(t){t.method="workspace/diagnostic/refresh",t.messageDirection=Dn.MessageDirection.serverToClient,t.type=new Dn.ProtocolRequestType0(t.method)})(Ec||(ot.DiagnosticRefreshRequest=Ec={}))});var Pc=Z(xe=>{"use strict";y();Object.defineProperty(xe,"__esModule",{value:!0});xe.DidCloseNotebookDocumentNotification=xe.DidSaveNotebookDocumentNotification=xe.DidChangeNotebookDocumentNotification=xe.NotebookCellArrayChange=xe.DidOpenNotebookDocumentNotification=xe.NotebookDocumentSyncRegistrationType=xe.NotebookDocument=xe.NotebookCell=xe.ExecutionSummary=xe.NotebookCellKind=void 0;var ar=xi(),ft=Ii(),ht=ve(),ea;(function(t){t.Markup=1,t.Code=2;function e(n){return n===1||n===2}i(e,"is"),t.is=e})(ea||(xe.NotebookCellKind=ea={}));var ta;(function(t){function e(o,s){let u={executionOrder:o};return(s===!0||s===!1)&&(u.success=s),u}i(e,"create"),t.create=e;function n(o){let s=o;return ft.objectLiteral(s)&&ar.uinteger.is(s.executionOrder)&&(s.success===void 0||ft.boolean(s.success))}i(n,"is"),t.is=n;function r(o,s){return o===s?!0:o==null||s===null||s===void 0?!1:o.executionOrder===s.executionOrder&&o.success===s.success}i(r,"equals"),t.equals=r})(ta||(xe.ExecutionSummary=ta={}));var Ui;(function(t){function e(s,u){return{kind:s,document:u}}i(e,"create"),t.create=e;function n(s){let u=s;return ft.objectLiteral(u)&&ea.is(u.kind)&&ar.DocumentUri.is(u.document)&&(u.metadata===void 0||ft.objectLiteral(u.metadata))}i(n,"is"),t.is=n;function r(s,u){let l=new Set;return s.document!==u.document&&l.add("document"),s.kind!==u.kind&&l.add("kind"),s.executionSummary!==u.executionSummary&&l.add("executionSummary"),(s.metadata!==void 0||u.metadata!==void 0)&&!o(s.metadata,u.metadata)&&l.add("metadata"),(s.executionSummary!==void 0||u.executionSummary!==void 0)&&!ta.equals(s.executionSummary,u.executionSummary)&&l.add("executionSummary"),l}i(r,"diff"),t.diff=r;function o(s,u){if(s===u)return!0;if(s==null||u===null||u===void 0||typeof s!=typeof u||typeof s!="object")return!1;let l=Array.isArray(s),f=Array.isArray(u);if(l!==f)return!1;if(l&&f){if(s.length!==u.length)return!1;for(let _=0;_<s.length;_++)if(!o(s[_],u[_]))return!1}if(ft.objectLiteral(s)&&ft.objectLiteral(u)){let _=Object.keys(s),v=Object.keys(u);if(_.length!==v.length||(_.sort(),v.sort(),!o(_,v)))return!1;for(let T=0;T<_.length;T++){let M=_[T];if(!o(s[M],u[M]))return!1}}return!0}i(o,"equalsMetadata")})(Ui||(xe.NotebookCell=Ui={}));var Rc;(function(t){function e(r,o,s,u){return{uri:r,notebookType:o,version:s,cells:u}}i(e,"create"),t.create=e;function n(r){let o=r;return ft.objectLiteral(o)&&ft.string(o.uri)&&ar.integer.is(o.version)&&ft.typedArray(o.cells,Ui.is)}i(n,"is"),t.is=n})(Rc||(xe.NotebookDocument=Rc={}));var An;(function(t){t.method="notebookDocument/sync",t.messageDirection=ht.MessageDirection.clientToServer,t.type=new ht.RegistrationType(t.method)})(An||(xe.NotebookDocumentSyncRegistrationType=An={}));var xc;(function(t){t.method="notebookDocument/didOpen",t.messageDirection=ht.MessageDirection.clientToServer,t.type=new ht.ProtocolNotificationType(t.method),t.registrationMethod=An.method})(xc||(xe.DidOpenNotebookDocumentNotification=xc={}));var Ic;(function(t){function e(r){let o=r;return ft.objectLiteral(o)&&ar.uinteger.is(o.start)&&ar.uinteger.is(o.deleteCount)&&(o.cells===void 0||ft.typedArray(o.cells,Ui.is))}i(e,"is"),t.is=e;function n(r,o,s){let u={start:r,deleteCount:o};return s!==void 0&&(u.cells=s),u}i(n,"create"),t.create=n})(Ic||(xe.NotebookCellArrayChange=Ic={}));var Nc;(function(t){t.method="notebookDocument/didChange",t.messageDirection=ht.MessageDirection.clientToServer,t.type=new ht.ProtocolNotificationType(t.method),t.registrationMethod=An.method})(Nc||(xe.DidChangeNotebookDocumentNotification=Nc={}));var Mc;(function(t){t.method="notebookDocument/didSave",t.messageDirection=ht.MessageDirection.clientToServer,t.type=new ht.ProtocolNotificationType(t.method),t.registrationMethod=An.method})(Mc||(xe.DidSaveNotebookDocumentNotification=Mc={}));var kc;(function(t){t.method="notebookDocument/didClose",t.messageDirection=ht.MessageDirection.clientToServer,t.type=new ht.ProtocolNotificationType(t.method),t.registrationMethod=An.method})(kc||(xe.DidCloseNotebookDocumentNotification=kc={}))});var Dc=Z(Wi=>{"use strict";y();Object.defineProperty(Wi,"__esModule",{value:!0});Wi.InlineCompletionRequest=void 0;var Cc=ve(),Lc;(function(t){t.method="textDocument/inlineCompletion",t.messageDirection=Cc.MessageDirection.clientToServer,t.type=new Cc.ProtocolRequestType(t.method)})(Lc||(Wi.InlineCompletionRequest=Lc={}))});var Gd=Z(m=>{"use strict";y();Object.defineProperty(m,"__esModule",{value:!0});m.WorkspaceSymbolRequest=m.CodeActionResolveRequest=m.CodeActionRequest=m.DocumentSymbolRequest=m.DocumentHighlightRequest=m.ReferencesRequest=m.DefinitionRequest=m.SignatureHelpRequest=m.SignatureHelpTriggerKind=m.HoverRequest=m.CompletionResolveRequest=m.CompletionRequest=m.CompletionTriggerKind=m.PublishDiagnosticsNotification=m.WatchKind=m.RelativePattern=m.FileChangeType=m.DidChangeWatchedFilesNotification=m.WillSaveTextDocumentWaitUntilRequest=m.WillSaveTextDocumentNotification=m.TextDocumentSaveReason=m.DidSaveTextDocumentNotification=m.DidCloseTextDocumentNotification=m.DidChangeTextDocumentNotification=m.TextDocumentContentChangeEvent=m.DidOpenTextDocumentNotification=m.TextDocumentSyncKind=m.TelemetryEventNotification=m.LogMessageNotification=m.ShowMessageRequest=m.ShowMessageNotification=m.MessageType=m.DidChangeConfigurationNotification=m.ExitNotification=m.ShutdownRequest=m.InitializedNotification=m.InitializeErrorCodes=m.InitializeRequest=m.WorkDoneProgressOptions=m.TextDocumentRegistrationOptions=m.StaticRegistrationOptions=m.PositionEncodingKind=m.FailureHandlingKind=m.ResourceOperationKind=m.UnregistrationRequest=m.RegistrationRequest=m.DocumentSelector=m.NotebookCellTextDocumentFilter=m.NotebookDocumentFilter=m.TextDocumentFilter=void 0;m.MonikerRequest=m.MonikerKind=m.UniquenessLevel=m.WillDeleteFilesRequest=m.DidDeleteFilesNotification=m.WillRenameFilesRequest=m.DidRenameFilesNotification=m.WillCreateFilesRequest=m.DidCreateFilesNotification=m.FileOperationPatternKind=m.LinkedEditingRangeRequest=m.ShowDocumentRequest=m.SemanticTokensRegistrationType=m.SemanticTokensRefreshRequest=m.SemanticTokensRangeRequest=m.SemanticTokensDeltaRequest=m.SemanticTokensRequest=m.TokenFormat=m.CallHierarchyPrepareRequest=m.CallHierarchyOutgoingCallsRequest=m.CallHierarchyIncomingCallsRequest=m.WorkDoneProgressCancelNotification=m.WorkDoneProgressCreateRequest=m.WorkDoneProgress=m.SelectionRangeRequest=m.DeclarationRequest=m.FoldingRangeRefreshRequest=m.FoldingRangeRequest=m.ColorPresentationRequest=m.DocumentColorRequest=m.ConfigurationRequest=m.DidChangeWorkspaceFoldersNotification=m.WorkspaceFoldersRequest=m.TypeDefinitionRequest=m.ImplementationRequest=m.ApplyWorkspaceEditRequest=m.ExecuteCommandRequest=m.PrepareRenameRequest=m.RenameRequest=m.PrepareSupportDefaultBehavior=m.DocumentOnTypeFormattingRequest=m.DocumentRangesFormattingRequest=m.DocumentRangeFormattingRequest=m.DocumentFormattingRequest=m.DocumentLinkResolveRequest=m.DocumentLinkRequest=m.CodeLensRefreshRequest=m.CodeLensResolveRequest=m.CodeLensRequest=m.WorkspaceSymbolResolveRequest=void 0;m.InlineCompletionRequest=m.DidCloseNotebookDocumentNotification=m.DidSaveNotebookDocumentNotification=m.DidChangeNotebookDocumentNotification=m.NotebookCellArrayChange=m.DidOpenNotebookDocumentNotification=m.NotebookDocumentSyncRegistrationType=m.NotebookDocument=m.NotebookCell=m.ExecutionSummary=m.NotebookCellKind=m.DiagnosticRefreshRequest=m.WorkspaceDiagnosticRequest=m.DocumentDiagnosticRequest=m.DocumentDiagnosticReportKind=m.DiagnosticServerCancellationData=m.InlayHintRefreshRequest=m.InlayHintResolveRequest=m.InlayHintRequest=m.InlineValueRefreshRequest=m.InlineValueRequest=m.TypeHierarchySupertypesRequest=m.TypeHierarchySubtypesRequest=m.TypeHierarchyPrepareRequest=void 0;var O=ve(),Ac=xi(),De=Ii(),__=sl();Object.defineProperty(m,"ImplementationRequest",{enumerable:!0,get:i(function(){return __.ImplementationRequest},"get")});var g_=ll();Object.defineProperty(m,"TypeDefinitionRequest",{enumerable:!0,get:i(function(){return g_.TypeDefinitionRequest},"get")});var $d=fl();Object.defineProperty(m,"WorkspaceFoldersRequest",{enumerable:!0,get:i(function(){return $d.WorkspaceFoldersRequest},"get")});Object.defineProperty(m,"DidChangeWorkspaceFoldersNotification",{enumerable:!0,get:i(function(){return $d.DidChangeWorkspaceFoldersNotification},"get")});var p_=gl();Object.defineProperty(m,"ConfigurationRequest",{enumerable:!0,get:i(function(){return p_.ConfigurationRequest},"get")});var zd=yl();Object.defineProperty(m,"DocumentColorRequest",{enumerable:!0,get:i(function(){return zd.DocumentColorRequest},"get")});Object.defineProperty(m,"ColorPresentationRequest",{enumerable:!0,get:i(function(){return zd.ColorPresentationRequest},"get")});var Hd=vl();Object.defineProperty(m,"FoldingRangeRequest",{enumerable:!0,get:i(function(){return Hd.FoldingRangeRequest},"get")});Object.defineProperty(m,"FoldingRangeRefreshRequest",{enumerable:!0,get:i(function(){return Hd.FoldingRangeRefreshRequest},"get")});var h_=Sl();Object.defineProperty(m,"DeclarationRequest",{enumerable:!0,get:i(function(){return h_.DeclarationRequest},"get")});var y_=Il();Object.defineProperty(m,"SelectionRangeRequest",{enumerable:!0,get:i(function(){return y_.SelectionRangeRequest},"get")});var sa=Pl();Object.defineProperty(m,"WorkDoneProgress",{enumerable:!0,get:i(function(){return sa.WorkDoneProgress},"get")});Object.defineProperty(m,"WorkDoneProgressCreateRequest",{enumerable:!0,get:i(function(){return sa.WorkDoneProgressCreateRequest},"get")});Object.defineProperty(m,"WorkDoneProgressCancelNotification",{enumerable:!0,get:i(function(){return sa.WorkDoneProgressCancelNotification},"get")});var aa=Al();Object.defineProperty(m,"CallHierarchyIncomingCallsRequest",{enumerable:!0,get:i(function(){return aa.CallHierarchyIncomingCallsRequest},"get")});Object.defineProperty(m,"CallHierarchyOutgoingCallsRequest",{enumerable:!0,get:i(function(){return aa.CallHierarchyOutgoingCallsRequest},"get")});Object.defineProperty(m,"CallHierarchyPrepareRequest",{enumerable:!0,get:i(function(){return aa.CallHierarchyPrepareRequest},"get")});var On=Wl();Object.defineProperty(m,"TokenFormat",{enumerable:!0,get:i(function(){return On.TokenFormat},"get")});Object.defineProperty(m,"SemanticTokensRequest",{enumerable:!0,get:i(function(){return On.SemanticTokensRequest},"get")});Object.defineProperty(m,"SemanticTokensDeltaRequest",{enumerable:!0,get:i(function(){return On.SemanticTokensDeltaRequest},"get")});Object.defineProperty(m,"SemanticTokensRangeRequest",{enumerable:!0,get:i(function(){return On.SemanticTokensRangeRequest},"get")});Object.defineProperty(m,"SemanticTokensRefreshRequest",{enumerable:!0,get:i(function(){return On.SemanticTokensRefreshRequest},"get")});Object.defineProperty(m,"SemanticTokensRegistrationType",{enumerable:!0,get:i(function(){return On.SemanticTokensRegistrationType},"get")});var b_=zl();Object.defineProperty(m,"ShowDocumentRequest",{enumerable:!0,get:i(function(){return b_.ShowDocumentRequest},"get")});var w_=Gl();Object.defineProperty(m,"LinkedEditingRangeRequest",{enumerable:!0,get:i(function(){return w_.LinkedEditingRangeRequest},"get")});var nn=tc();Object.defineProperty(m,"FileOperationPatternKind",{enumerable:!0,get:i(function(){return nn.FileOperationPatternKind},"get")});Object.defineProperty(m,"DidCreateFilesNotification",{enumerable:!0,get:i(function(){return nn.DidCreateFilesNotification},"get")});Object.defineProperty(m,"WillCreateFilesRequest",{enumerable:!0,get:i(function(){return nn.WillCreateFilesRequest},"get")});Object.defineProperty(m,"DidRenameFilesNotification",{enumerable:!0,get:i(function(){return nn.DidRenameFilesNotification},"get")});Object.defineProperty(m,"WillRenameFilesRequest",{enumerable:!0,get:i(function(){return nn.WillRenameFilesRequest},"get")});Object.defineProperty(m,"DidDeleteFilesNotification",{enumerable:!0,get:i(function(){return nn.DidDeleteFilesNotification},"get")});Object.defineProperty(m,"WillDeleteFilesRequest",{enumerable:!0,get:i(function(){return nn.WillDeleteFilesRequest},"get")});var ua=sc();Object.defineProperty(m,"UniquenessLevel",{enumerable:!0,get:i(function(){return ua.UniquenessLevel},"get")});Object.defineProperty(m,"MonikerKind",{enumerable:!0,get:i(function(){return ua.MonikerKind},"get")});Object.defineProperty(m,"MonikerRequest",{enumerable:!0,get:i(function(){return ua.MonikerRequest},"get")});var la=cc();Object.defineProperty(m,"TypeHierarchyPrepareRequest",{enumerable:!0,get:i(function(){return la.TypeHierarchyPrepareRequest},"get")});Object.defineProperty(m,"TypeHierarchySubtypesRequest",{enumerable:!0,get:i(function(){return la.TypeHierarchySubtypesRequest},"get")});Object.defineProperty(m,"TypeHierarchySupertypesRequest",{enumerable:!0,get:i(function(){return la.TypeHierarchySupertypesRequest},"get")});var Vd=mc();Object.defineProperty(m,"InlineValueRequest",{enumerable:!0,get:i(function(){return Vd.InlineValueRequest},"get")});Object.defineProperty(m,"InlineValueRefreshRequest",{enumerable:!0,get:i(function(){return Vd.InlineValueRefreshRequest},"get")});var ca=hc();Object.defineProperty(m,"InlayHintRequest",{enumerable:!0,get:i(function(){return ca.InlayHintRequest},"get")});Object.defineProperty(m,"InlayHintResolveRequest",{enumerable:!0,get:i(function(){return ca.InlayHintResolveRequest},"get")});Object.defineProperty(m,"InlayHintRefreshRequest",{enumerable:!0,get:i(function(){return ca.InlayHintRefreshRequest},"get")});var ur=Sc();Object.defineProperty(m,"DiagnosticServerCancellationData",{enumerable:!0,get:i(function(){return ur.DiagnosticServerCancellationData},"get")});Object.defineProperty(m,"DocumentDiagnosticReportKind",{enumerable:!0,get:i(function(){return ur.DocumentDiagnosticReportKind},"get")});Object.defineProperty(m,"DocumentDiagnosticRequest",{enumerable:!0,get:i(function(){return ur.DocumentDiagnosticRequest},"get")});Object.defineProperty(m,"WorkspaceDiagnosticRequest",{enumerable:!0,get:i(function(){return ur.WorkspaceDiagnosticRequest},"get")});Object.defineProperty(m,"DiagnosticRefreshRequest",{enumerable:!0,get:i(function(){return ur.DiagnosticRefreshRequest},"get")});var yt=Pc();Object.defineProperty(m,"NotebookCellKind",{enumerable:!0,get:i(function(){return yt.NotebookCellKind},"get")});Object.defineProperty(m,"ExecutionSummary",{enumerable:!0,get:i(function(){return yt.ExecutionSummary},"get")});Object.defineProperty(m,"NotebookCell",{enumerable:!0,get:i(function(){return yt.NotebookCell},"get")});Object.defineProperty(m,"NotebookDocument",{enumerable:!0,get:i(function(){return yt.NotebookDocument},"get")});Object.defineProperty(m,"NotebookDocumentSyncRegistrationType",{enumerable:!0,get:i(function(){return yt.NotebookDocumentSyncRegistrationType},"get")});Object.defineProperty(m,"DidOpenNotebookDocumentNotification",{enumerable:!0,get:i(function(){return yt.DidOpenNotebookDocumentNotification},"get")});Object.defineProperty(m,"NotebookCellArrayChange",{enumerable:!0,get:i(function(){return yt.NotebookCellArrayChange},"get")});Object.defineProperty(m,"DidChangeNotebookDocumentNotification",{enumerable:!0,get:i(function(){return yt.DidChangeNotebookDocumentNotification},"get")});Object.defineProperty(m,"DidSaveNotebookDocumentNotification",{enumerable:!0,get:i(function(){return yt.DidSaveNotebookDocumentNotification},"get")});Object.defineProperty(m,"DidCloseNotebookDocumentNotification",{enumerable:!0,get:i(function(){return yt.DidCloseNotebookDocumentNotification},"get")});var v_=Dc();Object.defineProperty(m,"InlineCompletionRequest",{enumerable:!0,get:i(function(){return v_.InlineCompletionRequest},"get")});var na;(function(t){function e(n){let r=n;return De.string(r)||De.string(r.language)||De.string(r.scheme)||De.string(r.pattern)}i(e,"is"),t.is=e})(na||(m.TextDocumentFilter=na={}));var ra;(function(t){function e(n){let r=n;return De.objectLiteral(r)&&(De.string(r.notebookType)||De.string(r.scheme)||De.string(r.pattern))}i(e,"is"),t.is=e})(ra||(m.NotebookDocumentFilter=ra={}));var ia;(function(t){function e(n){let r=n;return De.objectLiteral(r)&&(De.string(r.notebook)||ra.is(r.notebook))&&(r.language===void 0||De.string(r.language))}i(e,"is"),t.is=e})(ia||(m.NotebookCellTextDocumentFilter=ia={}));var oa;(function(t){function e(n){if(!Array.isArray(n))return!1;for(let r of n)if(!De.string(r)&&!na.is(r)&&!ia.is(r))return!1;return!0}i(e,"is"),t.is=e})(oa||(m.DocumentSelector=oa={}));var Oc;(function(t){t.method="client/registerCapability",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolRequestType(t.method)})(Oc||(m.RegistrationRequest=Oc={}));var Fc;(function(t){t.method="client/unregisterCapability",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolRequestType(t.method)})(Fc||(m.UnregistrationRequest=Fc={}));var qc;(function(t){t.Create="create",t.Rename="rename",t.Delete="delete"})(qc||(m.ResourceOperationKind=qc={}));var jc;(function(t){t.Abort="abort",t.Transactional="transactional",t.TextOnlyTransactional="textOnlyTransactional",t.Undo="undo"})(jc||(m.FailureHandlingKind=jc={}));var Uc;(function(t){t.UTF8="utf-8",t.UTF16="utf-16",t.UTF32="utf-32"})(Uc||(m.PositionEncodingKind=Uc={}));var Wc;(function(t){function e(n){let r=n;return r&&De.string(r.id)&&r.id.length>0}i(e,"hasId"),t.hasId=e})(Wc||(m.StaticRegistrationOptions=Wc={}));var Bc;(function(t){function e(n){let r=n;return r&&(r.documentSelector===null||oa.is(r.documentSelector))}i(e,"is"),t.is=e})(Bc||(m.TextDocumentRegistrationOptions=Bc={}));var $c;(function(t){function e(r){let o=r;return De.objectLiteral(o)&&(o.workDoneProgress===void 0||De.boolean(o.workDoneProgress))}i(e,"is"),t.is=e;function n(r){let o=r;return o&&De.boolean(o.workDoneProgress)}i(n,"hasWorkDoneProgress"),t.hasWorkDoneProgress=n})($c||(m.WorkDoneProgressOptions=$c={}));var zc;(function(t){t.method="initialize",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(zc||(m.InitializeRequest=zc={}));var Hc;(function(t){t.unknownProtocolVersion=1})(Hc||(m.InitializeErrorCodes=Hc={}));var Vc;(function(t){t.method="initialized",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(Vc||(m.InitializedNotification=Vc={}));var Gc;(function(t){t.method="shutdown",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType0(t.method)})(Gc||(m.ShutdownRequest=Gc={}));var Zc;(function(t){t.method="exit",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType0(t.method)})(Zc||(m.ExitNotification=Zc={}));var Qc;(function(t){t.method="workspace/didChangeConfiguration",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(Qc||(m.DidChangeConfigurationNotification=Qc={}));var Jc;(function(t){t.Error=1,t.Warning=2,t.Info=3,t.Log=4,t.Debug=5})(Jc||(m.MessageType=Jc={}));var Xc;(function(t){t.method="window/showMessage",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolNotificationType(t.method)})(Xc||(m.ShowMessageNotification=Xc={}));var Yc;(function(t){t.method="window/showMessageRequest",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolRequestType(t.method)})(Yc||(m.ShowMessageRequest=Yc={}));var Kc;(function(t){t.method="window/logMessage",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolNotificationType(t.method)})(Kc||(m.LogMessageNotification=Kc={}));var ed;(function(t){t.method="telemetry/event",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolNotificationType(t.method)})(ed||(m.TelemetryEventNotification=ed={}));var td;(function(t){t.None=0,t.Full=1,t.Incremental=2})(td||(m.TextDocumentSyncKind=td={}));var nd;(function(t){t.method="textDocument/didOpen",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(nd||(m.DidOpenTextDocumentNotification=nd={}));var rd;(function(t){function e(r){let o=r;return o!=null&&typeof o.text=="string"&&o.range!==void 0&&(o.rangeLength===void 0||typeof o.rangeLength=="number")}i(e,"isIncremental"),t.isIncremental=e;function n(r){let o=r;return o!=null&&typeof o.text=="string"&&o.range===void 0&&o.rangeLength===void 0}i(n,"isFull"),t.isFull=n})(rd||(m.TextDocumentContentChangeEvent=rd={}));var id;(function(t){t.method="textDocument/didChange",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(id||(m.DidChangeTextDocumentNotification=id={}));var od;(function(t){t.method="textDocument/didClose",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(od||(m.DidCloseTextDocumentNotification=od={}));var sd;(function(t){t.method="textDocument/didSave",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(sd||(m.DidSaveTextDocumentNotification=sd={}));var ad;(function(t){t.Manual=1,t.AfterDelay=2,t.FocusOut=3})(ad||(m.TextDocumentSaveReason=ad={}));var ud;(function(t){t.method="textDocument/willSave",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(ud||(m.WillSaveTextDocumentNotification=ud={}));var ld;(function(t){t.method="textDocument/willSaveWaitUntil",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(ld||(m.WillSaveTextDocumentWaitUntilRequest=ld={}));var cd;(function(t){t.method="workspace/didChangeWatchedFiles",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolNotificationType(t.method)})(cd||(m.DidChangeWatchedFilesNotification=cd={}));var dd;(function(t){t.Created=1,t.Changed=2,t.Deleted=3})(dd||(m.FileChangeType=dd={}));var fd;(function(t){function e(n){let r=n;return De.objectLiteral(r)&&(Ac.URI.is(r.baseUri)||Ac.WorkspaceFolder.is(r.baseUri))&&De.string(r.pattern)}i(e,"is"),t.is=e})(fd||(m.RelativePattern=fd={}));var md;(function(t){t.Create=1,t.Change=2,t.Delete=4})(md||(m.WatchKind=md={}));var _d;(function(t){t.method="textDocument/publishDiagnostics",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolNotificationType(t.method)})(_d||(m.PublishDiagnosticsNotification=_d={}));var gd;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.TriggerForIncompleteCompletions=3})(gd||(m.CompletionTriggerKind=gd={}));var pd;(function(t){t.method="textDocument/completion",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(pd||(m.CompletionRequest=pd={}));var hd;(function(t){t.method="completionItem/resolve",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(hd||(m.CompletionResolveRequest=hd={}));var yd;(function(t){t.method="textDocument/hover",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(yd||(m.HoverRequest=yd={}));var bd;(function(t){t.Invoked=1,t.TriggerCharacter=2,t.ContentChange=3})(bd||(m.SignatureHelpTriggerKind=bd={}));var wd;(function(t){t.method="textDocument/signatureHelp",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(wd||(m.SignatureHelpRequest=wd={}));var vd;(function(t){t.method="textDocument/definition",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(vd||(m.DefinitionRequest=vd={}));var Ed;(function(t){t.method="textDocument/references",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Ed||(m.ReferencesRequest=Ed={}));var Td;(function(t){t.method="textDocument/documentHighlight",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Td||(m.DocumentHighlightRequest=Td={}));var Sd;(function(t){t.method="textDocument/documentSymbol",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Sd||(m.DocumentSymbolRequest=Sd={}));var Rd;(function(t){t.method="textDocument/codeAction",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Rd||(m.CodeActionRequest=Rd={}));var xd;(function(t){t.method="codeAction/resolve",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(xd||(m.CodeActionResolveRequest=xd={}));var Id;(function(t){t.method="workspace/symbol",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Id||(m.WorkspaceSymbolRequest=Id={}));var Nd;(function(t){t.method="workspaceSymbol/resolve",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Nd||(m.WorkspaceSymbolResolveRequest=Nd={}));var Md;(function(t){t.method="textDocument/codeLens",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Md||(m.CodeLensRequest=Md={}));var kd;(function(t){t.method="codeLens/resolve",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(kd||(m.CodeLensResolveRequest=kd={}));var Pd;(function(t){t.method="workspace/codeLens/refresh",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolRequestType0(t.method)})(Pd||(m.CodeLensRefreshRequest=Pd={}));var Cd;(function(t){t.method="textDocument/documentLink",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Cd||(m.DocumentLinkRequest=Cd={}));var Ld;(function(t){t.method="documentLink/resolve",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Ld||(m.DocumentLinkResolveRequest=Ld={}));var Dd;(function(t){t.method="textDocument/formatting",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Dd||(m.DocumentFormattingRequest=Dd={}));var Ad;(function(t){t.method="textDocument/rangeFormatting",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Ad||(m.DocumentRangeFormattingRequest=Ad={}));var Od;(function(t){t.method="textDocument/rangesFormatting",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Od||(m.DocumentRangesFormattingRequest=Od={}));var Fd;(function(t){t.method="textDocument/onTypeFormatting",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Fd||(m.DocumentOnTypeFormattingRequest=Fd={}));var qd;(function(t){t.Identifier=1})(qd||(m.PrepareSupportDefaultBehavior=qd={}));var jd;(function(t){t.method="textDocument/rename",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(jd||(m.RenameRequest=jd={}));var Ud;(function(t){t.method="textDocument/prepareRename",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Ud||(m.PrepareRenameRequest=Ud={}));var Wd;(function(t){t.method="workspace/executeCommand",t.messageDirection=O.MessageDirection.clientToServer,t.type=new O.ProtocolRequestType(t.method)})(Wd||(m.ExecuteCommandRequest=Wd={}));var Bd;(function(t){t.method="workspace/applyEdit",t.messageDirection=O.MessageDirection.serverToClient,t.type=new O.ProtocolRequestType("workspace/applyEdit")})(Bd||(m.ApplyWorkspaceEditRequest=Bd={}))});var Qd=Z(Bi=>{"use strict";y();Object.defineProperty(Bi,"__esModule",{value:!0});Bi.createProtocolConnection=void 0;var Zd=tn();function E_(t,e,n,r){return Zd.ConnectionStrategy.is(r)&&(r={connectionStrategy:r}),(0,Zd.createMessageConnection)(t,e,n,r)}i(E_,"createProtocolConnection");Bi.createProtocolConnection=E_});var Xd=Z(Je=>{"use strict";y();var T_=Je&&Je.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var o=Object.getOwnPropertyDescriptor(e,n);(!o||("get"in o?!e.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:i(function(){return e[n]},"get")}),Object.defineProperty(t,r,o)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),$i=Je&&Je.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&T_(e,t,n)};Object.defineProperty(Je,"__esModule",{value:!0});Je.LSPErrorCodes=Je.createProtocolConnection=void 0;$i(tn(),Je);$i(xi(),Je);$i(ve(),Je);$i(Gd(),Je);var S_=Qd();Object.defineProperty(Je,"createProtocolConnection",{enumerable:!0,get:i(function(){return S_.createProtocolConnection},"get")});var Jd;(function(t){t.lspReservedErrorRangeStart=-32899,t.RequestFailed=-32803,t.ServerCancelled=-32802,t.ContentModified=-32801,t.RequestCancelled=-32800,t.lspReservedErrorRangeEnd=-32800})(Jd||(Je.LSPErrorCodes=Jd={}))});var Kd=Z(bt=>{"use strict";y();var R_=bt&&bt.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n);var o=Object.getOwnPropertyDescriptor(e,n);(!o||("get"in o?!e.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:i(function(){return e[n]},"get")}),Object.defineProperty(t,r,o)}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Yd=bt&&bt.__exportStar||function(t,e){for(var n in t)n!=="default"&&!Object.prototype.hasOwnProperty.call(e,n)&&R_(e,t,n)};Object.defineProperty(bt,"__esModule",{value:!0});bt.createProtocolConnection=void 0;var x_=Zs();Yd(Zs(),bt);Yd(Xd(),bt);function I_(t,e,n,r){return(0,x_.createMessageConnection)(t,e,n,r)}i(I_,"createProtocolConnection");bt.createProtocolConnection=I_});var k_={};lf(k_,{IndexWorker:()=>Hi,isIndexWorker:()=>N_,runIndexWorker:()=>M_});module.exports=cf(k_);y();y();y();var zt={abap:{extensions:[".abap"]},aspdotnet:{extensions:[".asax",".ascx",".ashx",".asmx",".aspx",".axd"]},bat:{extensions:[".bat",".cmd"]},bibtex:{extensions:[".bib",".bibtex"]},blade:{extensions:[".blade",".blade.php"]},BluespecSystemVerilog:{extensions:[".bsv"]},c:{extensions:[".c",".cats",".h",".h.in",".idc"]},csharp:{extensions:[".cake",".cs",".cs.pp",".csx",".linq"]},cpp:{extensions:[".c++",".cc",".cp",".cpp",".cppm",".cxx",".h",".h++",".hh",".hpp",".hxx",".idl",".inc",".inl",".ino",".ipp",".ixx",".rc",".re",".tcc",".tpp",".txx",".i"]},cobol:{extensions:[".cbl",".ccp",".cob",".cobol",".cpy"]},css:{extensions:[".css",".wxss"]},clojure:{extensions:[".bb",".boot",".cl2",".clj",".cljc",".cljs",".cljs.hl",".cljscm",".cljx",".edn",".hic"],filenames:["riemann.config"]},ql:{extensions:[".ql",".qll"]},coffeescript:{extensions:["._coffee",".cake",".cjsx",".coffee",".iced"],filenames:["Cakefile"]},cuda:{extensions:[".cu",".cuh"]},dart:{extensions:[".dart"]},dockerfile:{extensions:[".containerfile",".dockerfile"],filenames:["Containerfile","Dockerfile"]},dotenv:{extensions:[".env"],filenames:[".env",".env.ci",".env.dev",".env.development",".env.development.local",".env.example",".env.local",".env.prod",".env.production",".env.sample",".env.staging",".env.test",".env.testing"]},html:{extensions:[".ect",".ejs",".ejs.t",".jst",".hta",".htm",".html",".html.hl",".html5",".inc",".jsp",".njk",".tpl",".twig",".wxml",".xht",".xhtml",".phtml",".liquid"]},elixir:{extensions:[".ex",".exs"],filenames:["mix.lock"]},erlang:{extensions:[".app",".app.src",".erl",".es",".escript",".hrl",".xrl",".yrl"],filenames:["Emakefile","rebar.config","rebar.config.lock","rebar.lock"]},fsharp:{extensions:[".fs",".fsi",".fsx"]},go:{extensions:[".go"]},groovy:{extensions:[".gradle",".groovy",".grt",".gtpl",".gvy",".jenkinsfile"],filenames:["Jenkinsfile","Jenkinsfile"]},graphql:{extensions:[".gql",".graphql",".graphqls"]},terraform:{extensions:[".hcl",".nomad",".tf",".tfvars",".workflow"]},hlsl:{extensions:[".cginc",".fx",".fxh",".hlsl",".hlsli"]},erb:{extensions:[".erb",".erb.deface",".rhtml"]},razor:{extensions:[".cshtml",".razor"]},haml:{extensions:[".haml",".haml.deface"]},handlebars:{extensions:[".handlebars",".hbs"]},haskell:{extensions:[".hs",".hs-boot",".hsc"]},ini:{extensions:[".cfg",".cnf",".dof",".ini",".lektorproject",".prefs",".pro",".properties",".url"],filenames:[".buckconfig",".coveragerc",".flake8",".pylintrc","HOSTS","buildozer.spec","hosts","pylintrc","vlcrc"]},json:{extensions:[".4DForm",".4DProject",".JSON-tmLanguage",".avsc",".geojson",".gltf",".har",".ice",".json",".json.example",".jsonl",".mcmeta",".sarif",".tact",".tfstate",".tfstate.backup",".topojson",".webapp",".webmanifest",".yy",".yyp"],filenames:[".all-contributorsrc",".arcconfig",".auto-changelog",".c8rc",".htmlhintrc",".imgbotconfig",".nycrc",".tern-config",".tern-project",".watchmanconfig","MODULE.bazel.lock","Package.resolved","Pipfile.lock","bun.lock","composer.lock","deno.lock","flake.lock","mcmod.info"]},jsonc:{extensions:[".code-snippets",".code-workspace",".jsonc",".sublime-build",".sublime-color-scheme",".sublime-commands",".sublime-completions",".sublime-keymap",".sublime-macro",".sublime-menu",".sublime-mousemap",".sublime-project",".sublime-settings",".sublime-theme",".sublime-workspace",".sublime_metrics",".sublime_session"],filenames:[".babelrc",".devcontainer.json",".eslintrc.json",".jscsrc",".jshintrc",".jslintrc",".swcrc","api-extractor.json","argv.json","devcontainer.json","extensions.json","jsconfig.json","keybindings.json","language-configuration.json","launch.json","profiles.json","settings.json","tasks.json","tsconfig.json","tslint.json"]},java:{extensions:[".jav",".java",".jsh"]},javascript:{extensions:["._js",".bones",".cjs",".es",".es6",".frag",".gs",".jake",".javascript",".js",".jsb",".jscad",".jsfl",".jslib",".jsm",".jspre",".jss",".mjs",".njs",".pac",".sjs",".ssjs",".xsjs",".xsjslib"],filenames:["Jakefile"]},julia:{extensions:[".jl"]},kotlin:{extensions:[".kt",".ktm",".kts"]},less:{extensions:[".less"]},lua:{extensions:[".fcgi",".lua",".luau",".nse",".p8",".pd_lua",".rbxs",".rockspec",".wlua"],filenames:[".luacheckrc"]},makefile:{extensions:[".d",".mak",".make",".makefile",".mk",".mkfile"],filenames:["BSDmakefile","GNUmakefile","Kbuild","Makefile","Makefile.am","Makefile.boot","Makefile.frag","Makefile.in","Makefile.inc","Makefile.wat","makefile","makefile.sco","mkfile"]},markdown:{extensions:[".livemd",".markdown",".md",".mdown",".mdwn",".mdx",".mkd",".mkdn",".mkdown",".ronn",".scd",".workbook"],filenames:["contents.lr"]},"objective-c":{extensions:[".h",".m"]},"objective-cpp":{extensions:[".mm"]},php:{extensions:[".aw",".ctp",".fcgi",".inc",".install",".module",".php",".php3",".php4",".php5",".phps",".phpt",".theme"],filenames:[".php",".php_cs",".php_cs.dist","Phakefile"]},perl:{extensions:[".al",".cgi",".fcgi",".perl",".ph",".pl",".plx",".pm",".psgi",".t"],filenames:[".latexmkrc","Makefile.PL","Rexfile","ack","cpanfile","latexmkrc"]},powershell:{extensions:[".ps1",".psd1",".psm1"]},pug:{extensions:[".jade",".pug"]},python:{extensions:[".cgi",".codon",".fcgi",".gyp",".gypi",".lmi",".py",".py3",".pyde",".pyi",".pyp",".pyt",".pyw",".rpy",".sage",".spec",".tac",".wsgi",".xpy"],filenames:[".gclient","DEPS","SConscript","SConstruct","wscript"]},r:{extensions:[".r",".rd",".rsx"],filenames:[".Rprofile","expr-dist"]},ruby:{extensions:[".builder",".eye",".fcgi",".gemspec",".god",".jbuilder",".mspec",".pluginspec",".podspec",".prawn",".rabl",".rake",".rb",".rbi",".rbuild",".rbw",".rbx",".ru",".ruby",".spec",".thor",".watchr"],filenames:[".irbrc",".pryrc",".simplecov","Appraisals","Berksfile","Brewfile","Buildfile","Capfile","Dangerfile","Deliverfile","Fastfile","Gemfile","Guardfile","Jarfile","Mavenfile","Podfile","Puppetfile","Rakefile","Snapfile","Steepfile","Thorfile","Vagrantfile","buildfile"]},rust:{extensions:[".rs",".rs.in"]},scss:{extensions:[".scss"]},sql:{extensions:[".cql",".ddl",".inc",".mysql",".prc",".sql",".tab",".udf",".viw"]},sass:{extensions:[".sass"]},scala:{extensions:[".kojo",".sbt",".sc",".scala"]},shellscript:{extensions:[".bash",".bats",".cgi",".command",".fcgi",".fish",".ksh",".sh",".sh.in",".tmux",".tool",".trigger",".zsh",".zsh-theme"],filenames:[".bash_aliases",".bash_functions",".bash_history",".bash_logout",".bash_profile",".bashrc",".cshrc",".envrc",".flaskenv",".kshrc",".login",".profile",".tmux.conf",".zlogin",".zlogout",".zprofile",".zshenv",".zshrc","9fs","PKGBUILD","bash_aliases","bash_logout","bash_profile","bashrc","cshrc","gradlew","kshrc","login","man","profile","tmux.conf","zlogin","zlogout","zprofile","zshenv","zshrc"]},slang:{extensions:[".fxc",".hlsl",".s",".slang",".slangh",".usf",".ush",".vfx"]},slim:{extensions:[".slim"]},solidity:{extensions:[".sol"]},stylus:{extensions:[".styl"]},svelte:{extensions:[".svelte"]},swift:{extensions:[".swift"]},systemverilog:{extensions:[".sv",".svh",".vh"]},typescriptreact:{extensions:[".tsx"]},latex:{extensions:[".aux",".bbx",".cbx",".cls",".dtx",".ins",".lbx",".ltx",".mkii",".mkiv",".mkvi",".sty",".tex",".toc"]},typescript:{extensions:[".cts",".mts",".ts"]},verilog:{extensions:[".v",".veo"]},vim:{extensions:[".vba",".vim",".vimrc",".vmb"],filenames:[".exrc",".gvimrc",".nvimrc",".vimrc","_vimrc","gvimrc","nvimrc","vimrc"]},vb:{extensions:[".vb",".vbhtml",".Dsr",".bas",".cls",".ctl",".frm",".vbs"]},vue:{extensions:[".nvue",".vue"]},xml:{extensions:[".adml",".admx",".ant",".axaml",".axml",".builds",".ccproj",".ccxml",".clixml",".cproject",".cscfg",".csdef",".csl",".csproj",".ct",".depproj",".dita",".ditamap",".ditaval",".dll.config",".dotsettings",".filters",".fsproj",".fxml",".glade",".gml",".gmx",".gpx",".grxml",".gst",".hzp",".iml",".ivy",".jelly",".jsproj",".kml",".launch",".mdpolicy",".mjml",".mod",".mojo",".mxml",".natvis",".ncl",".ndproj",".nproj",".nuspec",".odd",".osm",".pkgproj",".plist",".pluginspec",".proj",".props",".ps1xml",".psc1",".pt",".pubxml",".qhelp",".rdf",".res",".resx",".rss",".sch",".scxml",".sfproj",".shproj",".srdf",".storyboard",".sublime-snippet",".svg",".sw",".targets",".tml",".typ",".ui",".urdf",".ux",".vbproj",".vcxproj",".vsixmanifest",".vssettings",".vstemplate",".vxml",".wixproj",".workflow",".wsdl",".wsf",".wxi",".wxl",".wxs",".x3d",".xacro",".xaml",".xib",".xlf",".xliff",".xmi",".xml",".xml.dist",".xmp",".xproj",".xsd",".xspec",".xul",".zcml"],filenames:[".classpath",".cproject",".project","App.config","NuGet.config","Settings.StyleCop","Web.Debug.config","Web.Release.config","Web.config","packages.config"]},xsl:{extensions:[".xsl",".xslt"]},yaml:{extensions:[".mir",".reek",".rviz",".sublime-syntax",".syntax",".yaml",".yaml-tmlanguage",".yaml.sed",".yml",".yml.mysql"],filenames:[".clang-format",".clang-tidy",".clangd",".gemrc","CITATION.cff","glide.lock","pixi.lock","yarn.lock"]},javascriptreact:{extensions:[".jsx"]},legend:{extensions:[".pure"]}};y();y();var ha=[".ejs",".erb",".haml",".hbs",".j2",".jinja",".jinja2",".liquid",".mustache",".njk",".php",".pug",".slim",".webc"],ya={".php":[".blade"]},ba=Object.keys(zt).flatMap(t=>zt[t].extensions);y();var Ea=require("os"),Gi=require("path");y();var wa;(()=>{"use strict";var t={975:A=>{function E(S){if(typeof S!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(S))}i(E,"e");function j(S,I){for(var k,$="",V=0,U=-1,ee=0,te=0;te<=S.length;++te){if(te<S.length)k=S.charCodeAt(te);else{if(k===47)break;k=47}if(k===47){if(!(U===te-1||ee===1))if(U!==te-1&&ee===2){if($.length<2||V!==2||$.charCodeAt($.length-1)!==46||$.charCodeAt($.length-2)!==46){if($.length>2){var Ie=$.lastIndexOf("/");if(Ie!==$.length-1){Ie===-1?($="",V=0):V=($=$.slice(0,Ie)).length-1-$.lastIndexOf("/"),U=te,ee=0;continue}}else if($.length===2||$.length===1){$="",V=0,U=te,ee=0;continue}}I&&($.length>0?$+="/..":$="..",V=2)}else $.length>0?$+="/"+S.slice(U+1,te):$=S.slice(U+1,te),V=te-U-1;U=te,ee=0}else k===46&&ee!==-1?++ee:ee=-1}return $}i(j,"r");var B={resolve:i(function(){for(var S,I="",k=!1,$=arguments.length-1;$>=-1&&!k;$--){var V;$>=0?V=arguments[$]:(S===void 0&&(S=process.cwd()),V=S),E(V),V.length!==0&&(I=V+"/"+I,k=V.charCodeAt(0)===47)}return I=j(I,!k),k?I.length>0?"/"+I:"/":I.length>0?I:"."},"resolve"),normalize:i(function(S){if(E(S),S.length===0)return".";var I=S.charCodeAt(0)===47,k=S.charCodeAt(S.length-1)===47;return(S=j(S,!I)).length!==0||I||(S="."),S.length>0&&k&&(S+="/"),I?"/"+S:S},"normalize"),isAbsolute:i(function(S){return E(S),S.length>0&&S.charCodeAt(0)===47},"isAbsolute"),join:i(function(){if(arguments.length===0)return".";for(var S,I=0;I<arguments.length;++I){var k=arguments[I];E(k),k.length>0&&(S===void 0?S=k:S+="/"+k)}return S===void 0?".":B.normalize(S)},"join"),relative:i(function(S,I){if(E(S),E(I),S===I||(S=B.resolve(S))===(I=B.resolve(I)))return"";for(var k=1;k<S.length&&S.charCodeAt(k)===47;++k);for(var $=S.length,V=$-k,U=1;U<I.length&&I.charCodeAt(U)===47;++U);for(var ee=I.length-U,te=V<ee?V:ee,Ie=-1,de=0;de<=te;++de){if(de===te){if(ee>te){if(I.charCodeAt(U+de)===47)return I.slice(U+de+1);if(de===0)return I.slice(U+de)}else V>te&&(S.charCodeAt(k+de)===47?Ie=de:de===0&&(Ie=0));break}var ze=S.charCodeAt(k+de);if(ze!==I.charCodeAt(U+de))break;ze===47&&(Ie=de)}var mt="";for(de=k+Ie+1;de<=$;++de)de!==$&&S.charCodeAt(de)!==47||(mt.length===0?mt+="..":mt+="/..");return mt.length>0?mt+I.slice(U+Ie):(U+=Ie,I.charCodeAt(U)===47&&++U,I.slice(U))},"relative"),_makeLong:i(function(S){return S},"_makeLong"),dirname:i(function(S){if(E(S),S.length===0)return".";for(var I=S.charCodeAt(0),k=I===47,$=-1,V=!0,U=S.length-1;U>=1;--U)if((I=S.charCodeAt(U))===47){if(!V){$=U;break}}else V=!1;return $===-1?k?"/":".":k&&$===1?"//":S.slice(0,$)},"dirname"),basename:i(function(S,I){if(I!==void 0&&typeof I!="string")throw new TypeError('"ext" argument must be a string');E(S);var k,$=0,V=-1,U=!0;if(I!==void 0&&I.length>0&&I.length<=S.length){if(I.length===S.length&&I===S)return"";var ee=I.length-1,te=-1;for(k=S.length-1;k>=0;--k){var Ie=S.charCodeAt(k);if(Ie===47){if(!U){$=k+1;break}}else te===-1&&(U=!1,te=k+1),ee>=0&&(Ie===I.charCodeAt(ee)?--ee==-1&&(V=k):(ee=-1,V=te))}return $===V?V=te:V===-1&&(V=S.length),S.slice($,V)}for(k=S.length-1;k>=0;--k)if(S.charCodeAt(k)===47){if(!U){$=k+1;break}}else V===-1&&(U=!1,V=k+1);return V===-1?"":S.slice($,V)},"basename"),extname:i(function(S){E(S);for(var I=-1,k=0,$=-1,V=!0,U=0,ee=S.length-1;ee>=0;--ee){var te=S.charCodeAt(ee);if(te!==47)$===-1&&(V=!1,$=ee+1),te===46?I===-1?I=ee:U!==1&&(U=1):I!==-1&&(U=-1);else if(!V){k=ee+1;break}}return I===-1||$===-1||U===0||U===1&&I===$-1&&I===k+1?"":S.slice(I,$)},"extname"),format:i(function(S){if(S===null||typeof S!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof S);return function(I,k){var $=k.dir||k.root,V=k.base||(k.name||"")+(k.ext||"");return $?$===k.root?$+V:$+"/"+V:V}(0,S)},"format"),parse:i(function(S){E(S);var I={root:"",dir:"",base:"",ext:"",name:""};if(S.length===0)return I;var k,$=S.charCodeAt(0),V=$===47;V?(I.root="/",k=1):k=0;for(var U=-1,ee=0,te=-1,Ie=!0,de=S.length-1,ze=0;de>=k;--de)if(($=S.charCodeAt(de))!==47)te===-1&&(Ie=!1,te=de+1),$===46?U===-1?U=de:ze!==1&&(ze=1):U!==-1&&(ze=-1);else if(!Ie){ee=de+1;break}return U===-1||te===-1||ze===0||ze===1&&U===te-1&&U===ee+1?te!==-1&&(I.base=I.name=ee===0&&V?S.slice(1,te):S.slice(ee,te)):(ee===0&&V?(I.name=S.slice(1,U),I.base=S.slice(1,te)):(I.name=S.slice(ee,U),I.base=S.slice(ee,te)),I.ext=S.slice(U,te)),ee>0?I.dir=S.slice(0,ee-1):V&&(I.dir="/"),I},"parse"),sep:"/",delimiter:":",win32:null,posix:null};B.posix=B,A.exports=B}},e={};function n(A){var E=e[A];if(E!==void 0)return E.exports;var j=e[A]={exports:{}};return t[A](j,j.exports,n),j.exports}i(n,"r"),n.d=(A,E)=>{for(var j in E)n.o(E,j)&&!n.o(A,j)&&Object.defineProperty(A,j,{enumerable:!0,get:E[j]})},n.o=(A,E)=>Object.prototype.hasOwnProperty.call(A,E),n.r=A=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(A,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(A,"__esModule",{value:!0})};var r={};let o;n.r(r),n.d(r,{URI:i(()=>M,"URI"),Utils:i(()=>Ce,"Utils")}),typeof process=="object"?o=process.platform==="win32":typeof navigator=="object"&&(o=navigator.userAgent.indexOf("Windows")>=0);let s=/^\w[\w\d+.-]*$/,u=/^\//,l=/^\/\//;function f(A,E){if(!A.scheme&&E)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${A.authority}", path: "${A.path}", query: "${A.query}", fragment: "${A.fragment}"}`);if(A.scheme&&!s.test(A.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(A.path){if(A.authority){if(!u.test(A.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(A.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}i(f,"a");let _="",v="/",T=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class M{static{i(this,"l")}static isUri(E){return E instanceof M||!!E&&typeof E.authority=="string"&&typeof E.fragment=="string"&&typeof E.path=="string"&&typeof E.query=="string"&&typeof E.scheme=="string"&&typeof E.fsPath=="string"&&typeof E.with=="function"&&typeof E.toString=="function"}scheme;authority;path;query;fragment;constructor(E,j,B,S,I,k=!1){typeof E=="object"?(this.scheme=E.scheme||_,this.authority=E.authority||_,this.path=E.path||_,this.query=E.query||_,this.fragment=E.fragment||_):(this.scheme=function($,V){return $||V?$:"file"}(E,k),this.authority=j||_,this.path=function($,V){switch($){case"https":case"http":case"file":V?V[0]!==v&&(V=v+V):V=v}return V}(this.scheme,B||_),this.query=S||_,this.fragment=I||_,f(this,k))}get fsPath(){return L(this,!1)}with(E){if(!E)return this;let{scheme:j,authority:B,path:S,query:I,fragment:k}=E;return j===void 0?j=this.scheme:j===null&&(j=_),B===void 0?B=this.authority:B===null&&(B=_),S===void 0?S=this.path:S===null&&(S=_),I===void 0?I=this.query:I===null&&(I=_),k===void 0?k=this.fragment:k===null&&(k=_),j===this.scheme&&B===this.authority&&S===this.path&&I===this.query&&k===this.fragment?this:new W(j,B,S,I,k)}static parse(E,j=!1){let B=T.exec(E);return B?new W(B[2]||_,le(B[4]||_),le(B[5]||_),le(B[7]||_),le(B[9]||_),j):new W(_,_,_,_,_)}static file(E){let j=_;if(o&&(E=E.replace(/\\/g,v)),E[0]===v&&E[1]===v){let B=E.indexOf(v,2);B===-1?(j=E.substring(2),E=v):(j=E.substring(2,B),E=E.substring(B)||v)}return new W("file",j,E,_,_)}static from(E){let j=new W(E.scheme,E.authority,E.path,E.query,E.fragment);return f(j,!0),j}toString(E=!1){return H(this,E)}toJSON(){return this}static revive(E){if(E){if(E instanceof M)return E;{let j=new W(E);return j._formatted=E.external,j._fsPath=E._sep===D?E.fsPath:null,j}}return E}}let D=o?1:void 0;class W extends M{static{i(this,"d")}_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=L(this,!1)),this._fsPath}toString(E=!1){return E?H(this,!0):(this._formatted||(this._formatted=H(this,!1)),this._formatted)}toJSON(){let E={$mid:1};return this._fsPath&&(E.fsPath=this._fsPath,E._sep=D),this._formatted&&(E.external=this._formatted),this.path&&(E.path=this.path),this.scheme&&(E.scheme=this.scheme),this.authority&&(E.authority=this.authority),this.query&&(E.query=this.query),this.fragment&&(E.fragment=this.fragment),E}}let F={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function P(A,E,j){let B,S=-1;for(let I=0;I<A.length;I++){let k=A.charCodeAt(I);if(k>=97&&k<=122||k>=65&&k<=90||k>=48&&k<=57||k===45||k===46||k===95||k===126||E&&k===47||j&&k===91||j&&k===93||j&&k===58)S!==-1&&(B+=encodeURIComponent(A.substring(S,I)),S=-1),B!==void 0&&(B+=A.charAt(I));else{B===void 0&&(B=A.substr(0,I));let $=F[k];$!==void 0?(S!==-1&&(B+=encodeURIComponent(A.substring(S,I)),S=-1),B+=$):S===-1&&(S=I)}}return S!==-1&&(B+=encodeURIComponent(A.substring(S))),B!==void 0?B:A}i(P,"m");function z(A){let E;for(let j=0;j<A.length;j++){let B=A.charCodeAt(j);B===35||B===63?(E===void 0&&(E=A.substr(0,j)),E+=F[B]):E!==void 0&&(E+=A[j])}return E!==void 0?E:A}i(z,"y");function L(A,E){let j;return j=A.authority&&A.path.length>1&&A.scheme==="file"?`//${A.authority}${A.path}`:A.path.charCodeAt(0)===47&&(A.path.charCodeAt(1)>=65&&A.path.charCodeAt(1)<=90||A.path.charCodeAt(1)>=97&&A.path.charCodeAt(1)<=122)&&A.path.charCodeAt(2)===58?E?A.path.substr(1):A.path[1].toLowerCase()+A.path.substr(2):A.path,o&&(j=j.replace(/\//g,"\\")),j}i(L,"v");function H(A,E){let j=E?z:P,B="",{scheme:S,authority:I,path:k,query:$,fragment:V}=A;if(S&&(B+=S,B+=":"),(I||S==="file")&&(B+=v,B+=v),I){let U=I.indexOf("@");if(U!==-1){let ee=I.substr(0,U);I=I.substr(U+1),U=ee.lastIndexOf(":"),U===-1?B+=j(ee,!1,!1):(B+=j(ee.substr(0,U),!1,!1),B+=":",B+=j(ee.substr(U+1),!1,!0)),B+="@"}I=I.toLowerCase(),U=I.lastIndexOf(":"),U===-1?B+=j(I,!1,!0):(B+=j(I.substr(0,U),!1,!0),B+=I.substr(U))}if(k){if(k.length>=3&&k.charCodeAt(0)===47&&k.charCodeAt(2)===58){let U=k.charCodeAt(1);U>=65&&U<=90&&(k=`/${String.fromCharCode(U+32)}:${k.substr(3)}`)}else if(k.length>=2&&k.charCodeAt(1)===58){let U=k.charCodeAt(0);U>=65&&U<=90&&(k=`${String.fromCharCode(U+32)}:${k.substr(2)}`)}B+=j(k,!0,!1)}return $&&(B+="?",B+=j($,!1,!1)),V&&(B+="#",B+=E?V:P(V,!1,!1)),B}i(H,"b");function Y(A){try{return decodeURIComponent(A)}catch{return A.length>3?A.substr(0,3)+Y(A.substr(3)):A}}i(Y,"C");let K=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function le(A){return A.match(K)?A.replace(K,E=>Y(E)):A}i(le,"w");var J=n(975);let ie=J.posix||J,Te="/";var Ce;(function(A){A.joinPath=function(E,...j){return E.with({path:ie.join(E.path,...j)})},A.resolvePath=function(E,...j){let B=E.path,S=!1;B[0]!==Te&&(B=Te+B,S=!0);let I=ie.resolve(B,...j);return S&&I[0]===Te&&!E.authority&&(I=I.substring(1)),E.with({path:I})},A.dirname=function(E){if(E.path.length===0||E.path===Te)return E;let j=ie.dirname(E.path);return j.length===1&&j.charCodeAt(0)===46&&(j=""),E.with({path:j})},A.basename=function(E){return ie.basename(E.path)},A.extname=function(E){return ie.extname(E.path)}})(Ce||(Ce={})),wa=r})();var{URI:Vi,Utils:df}=wa;function Ta(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substring(0,3)+Ta(t.substring(3)):t}}i(Ta,"decodeURIComponentGraceful");var va=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ff(t){return t.match(va)?t.replace(va,e=>Ta(e)):t}i(ff,"percentDecode");function mf(t){if(typeof t!="string"&&(t=t.uri),/^[A-Za-z]:\\/.test(t))throw new Error(`Could not parse <${t}>: Windows-style path`);try{let e=t.match(/^(?:([^:/?#]+?:)?\/\/)(\/\/.*)$/);return e?Vi.parse(e[1]+e[2],!0):Vi.parse(t,!0)}catch(e){throw new Error(`Could not parse <${t}>`,{cause:e})}}i(mf,"parseUri");var _f=new Set(["file","notebook","vscode-notebook","vscode-notebook-cell"]);function kt(t){let e=mf(t);if(!_f.has(e.scheme))throw new Error(`Unsupported scheme: ${e.scheme}`);if((0,Ea.platform)()==="win32"){let n=e.path;return e.authority?n=`//${e.authority}${e.path}`:/^\/[A-Za-z]:/.test(n)&&(n=n.substring(1)),(0,Gi.normalize)(n)}else{if(e.authority)throw new Error("Unsupported remote file path");return e.path}}i(kt,"fsPath");function vr(t){try{return kt(t)}catch{return}}i(vr,"getFsPath");function Sa(t){return ff((typeof t=="string"?t:t.uri).replace(/[#?].*$/,"").replace(/\/$/,"").replace(/^.*[/:]/,""))}i(Sa,"basename");var Zi=Mt(require("node:path"));var cn=class{constructor(e,n,r){this.languageId=e;this.isGuess=n;this.fileExtension=r}static{i(this,"Language")}},Wn=class{static{i(this,"LanguageDetection")}},Qi=new Map,ln=new Map;for(let[t,{extensions:e,filenames:n}]of Object.entries(zt)){for(let r of e)Qi.set(r,[...Qi.get(r)??[],t]);for(let r of n??[])ln.set(r,[...ln.get(r)??[],t])}var Ji=class extends Wn{static{i(this,"FilenameAndExensionLanguageDetection")}detectLanguage(e){let n=Sa(e.uri),r=Zi.extname(n).toLowerCase(),o=this.extensionWithoutTemplateLanguage(n,r),s=this.detectLanguageId(n,o),u=this.computeFullyQualifiedExtension(r,o);return s?new cn(s.languageId,s.isGuess,u):new cn(e.languageId,!0,u)}extensionWithoutTemplateLanguage(e,n){if(ha.includes(n)){let r=e.substring(0,e.lastIndexOf(".")),o=Zi.extname(r).toLowerCase();if(o.length>0&&ba.includes(o)&&this.isExtensionValidForTemplateLanguage(n,o))return o}return n}isExtensionValidForTemplateLanguage(e,n){let r=ya[e];return!r||r.includes(n)}detectLanguageId(e,n){if(ln.has(e))return{languageId:ln.get(e)[0],isGuess:!1};let r=Qi.get(n)??[];if(r.length>0)return{languageId:r[0],isGuess:r.length>1};for(;e.includes(".");)if(e=e.replace(/\.[^.]*$/,""),ln.has(e))return{languageId:ln.get(e)[0],isGuess:!1}}computeFullyQualifiedExtension(e,n){return e!==n?n+e:e}},Xi=class extends Wn{constructor(n){super();this.delegate=n}static{i(this,"GroupingLanguageDetection")}detectLanguage(n){let r=this.delegate.detectLanguage(n),o=r.languageId;return o==="c"||o==="cpp"?new cn("cpp",r.isGuess,r.fileExtension):r}},Yi=class extends Wn{constructor(n){super();this.delegate=n}static{i(this,"ClientProvidedLanguageDetection")}detectLanguage(n){return n.uri.startsWith("untitled:")||n.uri.startsWith("vscode-notebook-cell:")?new cn(n.languageId,!0,""):this.delegate.detectLanguage(n)}},Z_=new Xi(new Yi(new Ji));y();y();y();y();function Tt(t){if(t.isCancellationRequested)throw new Ki}i(Tt,"throwIfCancellationRequested");var Ki=class extends Error{static{i(this,"CancellationError")}constructor(){super(gf),this.name=this.message}},gf="Canceled",Er=class{constructor(){this.items=[]}static{i(this,"Stack")}push(e){this.items.push(e)}pop(){return this.items.pop()}peek(){return this.items[this.items.length-1]}tryPeek(){return this.items.length>0}toArray(){return this.items}};y();y();var dn=class extends Error{constructor(n,r){super(n,{cause:r});this.code="CopilotPromptLoadFailure"}static{i(this,"CopilotPromptLoadFailure")}};y();var Ra=Mt(require("node:fs/promises")),Tr=Mt(require("node:path"));async function Sr(t){return await Ra.readFile(eo(t))}i(Sr,"readFile");function eo(t){return Tr.default.resolve(Tr.default.extname(__filename)!==".ts"?__dirname:Tr.default.resolve(__dirname,"../../dist"),t)}i(eo,"locateFile");var Rr=Mt(xa());var Ia={python:"python",javascript:"javascript",javascriptreact:"javascript",jsx:"javascript",typescript:"typescript",typescriptreact:"tsx",go:"go",ruby:"ruby",csharp:"c_sharp",java:"java",php:"php",c:"cpp",cpp:"cpp"};function pf(t){if(!(t in Ia))throw new Error(`Unrecognized language: ${t}`);return Ia[t]}i(pf,"languageIdToWasmLanguage");var to=new Map;async function hf(t){let e;try{e=await Sr(`tree-sitter-${t}.wasm`)}catch(n){throw n instanceof Error&&"code"in n&&typeof n.code=="string"&&n.name==="Error"?new dn(`Could not load tree-sitter-${t}.wasm`,n):n}return Rr.default.Language.load(e)}i(hf,"loadWasmLanguage");function yf(t){let e=pf(t);if(!to.has(e)){let n=hf(e);to.set(e,n)}return to.get(e)}i(yf,"getLanguage");var no=class extends Error{static{i(this,"WrappedError")}constructor(e,n){super(e,{cause:n})}};async function Na(t,e){await Rr.default.init({locateFile:i(s=>eo(s),"locateFile")});let n;try{n=new Rr.default}catch(s){throw s&&typeof s=="object"&&"message"in s&&typeof s.message=="string"&&s.message.includes("table index is out of bounds")?new no(`Could not init Parse for language <${t}>`,s):s}let r=await yf(t);n.setLanguage(r);let o=n.parse(e);return n.delete(),o}i(Na,"parseTreeSitter");function Ht(t){switch(t){case 0:case 2:case 7:case 8:case 11:case 12:case 13:case 14:return!0;default:return!1}}i(Ht,"isTypeDefinition");var He=class t{static{i(this,"TextRange")}static{this.empty=new t(0,0)}constructor(e,n){this.start=e,this.length=n}static fromBounds(e,n){return new t(e,n-e)}get end(){return this.start+this.length}contains(e){return this.start<=e&&this.end>=e}containsRange(e){return this.start<=e.start&&this.end>=e.end}equals(e){return this.start===e.start&&this.length===e.length}getText(e){return e.slice(this.start,this.end)}getTextWithIndentation(e,n){let r=[],o=this.start;for(o=ka(e,e.length,o),Ma(r,n);o<this.end;)e[o]!=="\r"&&e[o]!==`
`?r.push(e[o++]):e[o]==="\r"&&o<e.length&&e[o+1]!==`
`||e[o]===`
`?(r.push(`
`),o=ka(e,e.length,++o),Ma(r,n)):o++;return r.join("")}};function Ma(t,e){for(let n=0;n<e;n++)t.push(" ")}i(Ma,"appendIndent");function ka(t,e,n){for(;n<e&&(t[n]===" "||t[n]==="	");)n++;return n}i(ka,"consumeIndent");var Pt=class{constructor(e,n,r,o,s,u,l,f,_){this.fileName=e;this.fullyQualifiedName=n;this.unqualifiedName=r;this.commentRange=o;this.nameRange=s;this.bodyRange=u;this.extentRange=l;this.kind=f;this.refKind=_;if(e.indexOf("\\")!==-1)throw new Error("fileName must be normalized to use forward slashes as path separators")}static{i(this,"SymbolRange")}equals(e){return this.fileName===e.fileName&&this.fullyQualifiedName===e.fullyQualifiedName&&this.unqualifiedName===e.unqualifiedName&&this.commentRange.equals(e.commentRange)&&this.nameRange.equals(e.nameRange)&&this.bodyRange.equals(e.bodyRange)&&this.extentRange.equals(e.extentRange)&&this.kind===e.kind&&this.refKind===e.refKind}},Ae=class t{constructor(){this.queriesCache=new Map}static{i(this,"SymbolExtractorBase")}async findMatches(e,n){let r=await Na(this.languageId,e),o=r.getLanguage(),u=this.getOrCreateQuery(o,n).matches(r.rootNode);return{tree:r,matches:u}}async executeQuery(e,n,r){let o;try{o=await this.findMatches(n,r);let s=new Er,u=[];for(let l of o.matches){let f=this.createSymbolRange(s,e,n,l.captures);f&&u.push(f)}return u}catch{return[]}finally{o?.tree.delete()}}getOrCreateQuery(e,n){let r=this.queriesCache.get(n);return r||(r=e.query(n),this.queriesCache.set(n,r)),r}createSymbolRange(e,n,r,o){let s=0,u=0,l=0,f=0,_=0,v=0,T=0,M=0,D=null,W=null;for(let z=0;z<o.length;z++){let L=o[z].name;L==="name"?(_=o[z].node.startIndex,v=o[z].node.endIndex):L==="reference"?(_=o[z].node.startIndex,v=o[z].node.endIndex,l=o[z].node.startIndex,f=o[z].node.endIndex,D=L):L==="body"?(T=o[z].node.startIndex,M=o[z].node.endIndex):L==="comment"?(s=s===0?o[z].node.startIndex:Math.min(s,o[z].node.startIndex),u=Math.max(u,o[z].node.endIndex)):L==="receiver"?W=He.fromBounds(o[z].node.startIndex,o[z].node.endIndex).getText(r):(l=o[z].node.startIndex,f=o[z].node.endIndex,D=L)}D==="definition.module.filescoped"&&(M=r.length,f=M);let F=He.fromBounds(l,f),P=l>0||f>0||_>0||v>0?new Pt(n,"","",He.fromBounds(s,u),He.fromBounds(_,v),He.fromBounds(T,M),F,t.kindFromString(D),0):null;if(P){t.updateScopesForSymbol(e,P);let z=P.nameRange.getText(r),L=this.createNameFromScopes(r,e.toArray());return L=W?`${W}.${L}`:L,new Pt(n,L,z.substring(z.lastIndexOf(".")+1),P.commentRange,P.nameRange,P.bodyRange,P.extentRange,P.kind,0)}return null}static updateScopesForSymbol(e,n){for(;e.tryPeek()&&!e.peek()?.extentRange.containsRange(n.extentRange);)e.pop();e.push(n)}static kindFromString(e){switch(e){case"definition.class":return 0;case"definition.constant":return 1;case"definition.enum_variant":return 3;case"definition.enum":return 2;case"definition.field":return 4;case"definition.function":return 5;case"definition.implementation":return 6;case"definition.interface":return 7;case"definition.macro":return 8;case"definition.method":return 9;case"import.module":case"definition.module":case"definition.module.filescoped":return 10;case"definition.struct":return 11;case"definition.trait":return 12;case"definition.type":return 13;case"definition.union":return 14;case"reference":return 16;case"wildcard":return 18;case"alias":return 19;case"import":return 17;default:throw new Error("NotSupportedException")}}cleanQuotedString(e){return e.replace(/^(['"])(.*)\1$/,"$2")}};var xr=class extends Ae{static{i(this,"GoSymbolExtractor")}get languageId(){return"go"}extractSymbols(e,n){return this.executeQuery(e,n,bf)}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},Ir=class extends Ae{static{i(this,"GoReferenceExtractor")}get languageId(){return"go"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}extractReferences(e,n){return this.executeQuery(e,n,wf)}async extractLocalReferences(e,n,r){let o=await this.executeQuery(e,n,vf),s=o.filter(f=>f.kind!==9),u=o.filter(f=>f.kind===9&&f.extentRange.containsRange(r)),l=[];for(let f of u)l.push(...s.filter(_=>f.extentRange.containsRange(_.extentRange)));return l}};var bf=`
(
    ((comment)* @comment)
    .
    (type_declaration (type_spec name: (_) @name type: (struct_type (field_declaration_list) @body))) @definition.struct
)

(
    ((comment)* @comment)
    .
    (type_declaration (type_spec name: (_) @name type: (interface_type (_)) @body)) @definition.interface
)

(
    ((comment)* @comment)
    .
    (method_declaration receiver: (parameter_list (parameter_declaration type: [(type_identifier) @receiver (pointer_type (type_identifier) @receiver)] )) name: (_) @name body: (_) @body) @definition.method
)

(
    ((comment)* @comment)
    .
    (method_elem name: (_) @name) @definition.method
)

(
    ((comment)* @comment)
    .
    (function_declaration name: (_) @name) @definition.method
)

(
    ((comment)* @comment)
    .
    (field_declaration name: (_) @name) @definition.field
)
`;var wf=`
(call_expression function: (_) @name) @reference

(type_identifier) @reference
`,vf=`
(call_expression function: (_) @name) @reference

(type_identifier) @reference
`;y();var Nr=class extends Ae{static{i(this,"JavaSymbolExtractor")}get languageId(){return"java"}extractSymbols(e,n){return this.executeQuery(e,n,Ef)}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},Mr=class extends Ae{static{i(this,"JavaReferenceExtractor")}get languageId(){return"java"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}extractReferences(e,n){return this.executeQuery(e,n,Tf)}async extractLocalReferences(e,n,r){let o=await this.executeQuery(e,n,Sf),s=o.filter(f=>f.kind!==9),u=o.filter(f=>f.kind===9&&f.extentRange.containsRange(r)),l=[];for(let f of u)l.push(...s.filter(_=>f.extentRange.containsRange(_.extentRange)));return l}};var Ef=`
(
    [
        (block_comment) @comment
        (line_comment)* @comment
    ]
    .
    (class_declaration name: (identifier) @name body: (class_body) @body) @definition.class
)

(
    [
        (block_comment) @comment
        (line_comment)* @comment
    ]
    .
    (constructor_declaration name: (identifier) @name body: (constructor_body) @body) @definition.method
)

(
    [
        (block_comment) @comment
        (line_comment)* @comment
    ]
    .
    (method_declaration name: (identifier) @name body: (block)? @body) @definition.method
)

(
    [
        (block_comment) @comment
        (line_comment)* @comment
    ]
    .
    (interface_declaration name: (identifier) @name body: (interface_body) @body) @definition.interface
)

(
    [
        (block_comment) @comment
        (line_comment)* @comment
    ]
    .
    (field_declaration declarator: (variable_declarator name: (identifier) @name)) @definition.field
)

(
    [
        ((line_comment)* @comment)
        ((block_comment)* @comment)
    ]
    .
    (enum_declaration name: (_) @name body: (_) @body) @definition.enum
)

(
    [
        ((line_comment)* @comment)
        ((block_comment)* @comment)
    ]
    .
    (enum_constant name: (identifier) @name) @definition.enum_variant
)
`;var Tf=`
(method_invocation 
  name: (identifier) @name
) @reference

(type_identifier) @reference
`,Sf=`
(method_invocation 
  name: (identifier) @name
) @reference

(type_identifier) @reference
`;y();var Rf=new Set(["null","undefined","void","object","symbol","bigint","Array","Promise","Date","RegExp","Map","Set"]),kr=class extends Ae{static{i(this,"JavaScriptSymbolExtractor")}get languageId(){return"javascript"}extractSymbols(e,n){return this.executeQuery(e,n,xf)}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},Pr=class extends Ae{static{i(this,"JavaScriptReferenceExtractor")}extractLocalReferences(e,n,r){throw new Error("Method not implemented.")}get languageId(){return"javascript"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}async extractReferences(e,n){return(await this.executeQuery(e,n,If)).filter(o=>!Rf.has(o.unqualifiedName))}},xf=`
(
    ((comment)* @comment)
    .
    [
        (class_declaration name: (_) @name body: (_) @body) @definition.class
        (function_declaration name: (_) @name body: (_) @body) @definition.function
        (export_statement declaration: (lexical_declaration (variable_declarator name: (identifier) @name value: (_) @body))) @definition.function
    ]
)
`,If=`
(call_expression function: (_) @name) @reference
`;y();var Nf=new Set(["int","str","float","bool","list","dict","tuple","set"]),Cr=class extends Ae{static{i(this,"PythonSymbolExtractor")}get languageId(){return"python"}extractSymbols(e,n){return this.executeQuery(e,n,Mf)}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},Lr=class extends Ae{static{i(this,"PythonReferenceExtractor")}get languageId(){return"python"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}async extractReferences(e,n){return(await this.executeQuery(e,n,kf)).filter(o=>!Nf.has(o.unqualifiedName))}async extractLocalReferences(e,n,r){let o=await this.executeQuery(e,n,Pf),s=o.filter(f=>f.kind!==9),u=o.filter(f=>f.kind===9&&f.extentRange.containsRange(r)),l=[];for(let f of u)l.push(...s.filter(_=>f.extentRange.containsRange(_.extentRange)));return l}};var Mf=`
(
    ((comment)* @comment)
    .
    (class_definition name: (_) @name body: (_) @body) @definition.class
)

(
    ((comment)* @comment)
    .
    (function_definition name: (_) @name body: (_) @body) @definition.method
)
`,kf=`
(call function: (_) @name) @reference

(type [  
    (identifier)* @name  
    (_ (identifier) @name)* 
]) @reference

(class_definition superclasses: (argument_list (identifier) @name)) @reference
`;var Pf=`
(call function: (_) @name) @reference

(type [  
    (identifier)* @name  
    (_ (identifier) @name)* 
])  @reference

(class_definition superclasses: (argument_list (identifier) @name)) @reference
`;y();var Cf=new Set(["string","number","boolean","null","undefined","void","any","never","object","symbol","bigint","Array","Promise","Date","RegExp","Map","Set"]),fn=class extends Ae{static{i(this,"TypeScriptSymbolExtractor")}get languageId(){return"typescript"}extractSymbols(e,n){return this.executeQuery(e,n,Lf)}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},mn=class extends Ae{static{i(this,"TypeScriptReferenceExtractor")}get languageId(){return"typescript"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}async extractReferences(e,n){return(await this.executeQuery(e,n,Df)).filter(o=>!Cf.has(o.unqualifiedName))}async extractLocalReferences(e,n,r){throw new Error("Method not implemented.")}},Lf=`
(
    ((comment)* @comment)
    .
    [
        (class_declaration name: (_) @name body: (_) @body) @definition.class
        (interface_declaration name: (_) @name body: (_) @body) @definition.interface
        (type_alias_declaration name: (type_identifier) @name) @definition.type
        (abstract_class_declaration name: (type_identifier) @name) @definition.class
        (enum_declaration name: (identifier) @name) @definition.type
    ]
)

(
    ((comment)* @comment)
    .
    [
        (method_definition name: (_) @name body: (_) @body) @definition.method
        (function_declaration name: (_) @name body: (_) @body) @definition.function
        (function_signature name: (identifier) @name) @definition.function
        (method_signature name: (property_identifier) @name) @definition.method
        (abstract_method_signature name: (property_identifier) @name) @definition.method
        (variable_declarator name: (identifier) @name type: (type_annotation (type_identifier)))
    ]
)
`,Df=`
(call_expression function: (_) @name) @reference

(type_identifier) @name @reference

(new_expression constructor: (identifier) @name) @reference
`;y();var Af=new Set(["string","number","boolean","null","undefined","void","any","never","object","symbol","bigint","Array","Promise","Date","RegExp","Map","Set"]),Dr=class extends fn{static{i(this,"TypeScriptReactSymbolExtractor")}get languageId(){return"typescriptreact"}extractSymbols(e,n){return Promise.all([this.executeQuery(e,n,Of),super.extractSymbols(e,n)]).then(([r,o])=>[...r,...o])}createNameFromScopes(e,n){return n.map(r=>r.nameRange.getText(e)).join(".")}},Ar=class extends mn{static{i(this,"TypeScriptReactReferenceExtractor")}get languageId(){return"typescriptreact"}createNameFromScopes(e,n){return n.length>0?n[n.length-1].nameRange.getText(e):""}async extractReferences(e,n){let[r,o]=await Promise.all([this.executeQuery(e,n,Ff),super.extractReferences(e,n)]);return[...r.filter(s=>!Af.has(s.unqualifiedName)),...o]}},Of=`
(
    ((comment)* @comment)
    .
    [
        (lexical_declaration
            (variable_declarator
            name: (identifier) @name
            value: (arrow_function
                parameters: (_) 
                body: (_) @body))) @definition.function
    ]
)
`,Ff=`
    (jsx_element open_tag: (jsx_opening_element name: (_) @name) close_tag: (jsx_closing_element) ) @reference
    
    (jsx_self_closing_element name: (_) @name) @reference
    
    (import_specifier name:(identifier) @name) @reference
`;var ro=[{symbolExtractor:new Nr,referenceExtractor:new Mr,languageId:"java"},{symbolExtractor:new xr,referenceExtractor:new Ir,languageId:"go"},{symbolExtractor:new Cr,referenceExtractor:new Lr,languageId:"python"},{symbolExtractor:new fn,referenceExtractor:new mn,languageId:"typescript"},{symbolExtractor:new Dr,referenceExtractor:new Ar,languageId:"typescriptreact"},{symbolExtractor:new kr,referenceExtractor:new Pr,languageId:"javascript"}];var Ca=ro.map(t=>t.referenceExtractor),La=ro.map(t=>t.symbolExtractor),mp=ro.map(t=>zt[t.languageId].extensions).flat();y();y();y();y();y();y();y();y();y();function io(t,e,n){return{type:"virtual",indentation:t,subs:e,label:n}}i(io,"virtualNode");function Da(t,e,n,r,o){if(n==="")throw new Error("Cannot create a line node with an empty source line");return{type:"line",indentation:t,lineNumber:e,sourceLine:n,subs:r,label:o}}i(Da,"lineNode");function oo(t){return{type:"blank",lineNumber:t,subs:[]}}i(oo,"blankNode");function Or(t){return{type:"top",indentation:-1,subs:t??[]}}i(Or,"topNode");function Oe(t){return t.type==="blank"}i(Oe,"isBlank");function Vt(t){return t.type==="line"}i(Vt,"isLine");function Gt(t){return t.type==="virtual"}i(Gt,"isVirtual");y();function Aa(t,e){return We(t,n=>{n.label=n.label?e(n.label)?void 0:n.label:void 0},"bottomUp"),t}i(Aa,"clearLabelsIf");function Zt(t,e){switch(t.type){case"line":case"virtual":{let n=t.subs.map(r=>Zt(r,e));return{...t,subs:n,label:t.label?e(t.label):void 0}}case"blank":return{...t,label:t.label?e(t.label):void 0};case"top":return{...t,subs:t.subs.map(n=>Zt(n,e)),label:t.label?e(t.label):void 0}}}i(Zt,"mapLabels");function We(t,e,n){function r(o){n==="topDown"&&e(o),o.subs.forEach(s=>{r(s)}),n==="bottomUp"&&e(o)}i(r,"_visit"),r(t)}i(We,"visitTree");function so(t,e,n,r){let o=e;function s(u){o=n(u,o)}return i(s,"visitor"),We(t,s,r),o}i(so,"foldTree");function Fr(t,e,n){let r=i(s=>{if(n!==void 0&&n(s))return s;{let u=s.subs.map(r).filter(l=>l!==void 0);return s.subs=u,e(s)}},"rebuild"),o=r(t);return o!==void 0?o:Or()}i(Fr,"rebuildTree");y();function jf(t){let e=t.split(`
`),n=e.map(_=>_.match(/^\s*/)[0].length),r=e.map(_=>_.trimLeft());function o(_){let[v,T]=s(_+1,n[_]);return[Da(n[_],_,r[_],v),T]}i(o,"parseNode");function s(_,v){let T,M=[],D=_,W;for(;D<r.length&&(r[D]===""||n[D]>v);)if(r[D]==="")W===void 0&&(W=D),D+=1;else{if(W!==void 0){for(let F=W;F<D;F++)M.push(oo(F));W=void 0}[T,D]=o(D),M.push(T)}return W!==void 0&&(D=W),[M,D]}i(s,"parseSubs");let[u,l]=s(0,-1),f=l;for(;f<r.length&&r[f]==="";)u.push(oo(f)),f+=1;if(f<r.length)throw new Error(`Parsing did not go to end of file. Ended at ${f} out of ${r.length}`);return Or(u)}i(jf,"parseRaw");function Bn(t,e){function n(r){if(Vt(r)){let o=e.find(s=>s.matches(r.sourceLine));o&&(r.label=o.label)}}i(n,"visitor"),We(t,n,"bottomUp")}i(Bn,"labelLines");function qr(t){function e(n){if(Gt(n)&&n.label===void 0){let r=n.subs.filter(o=>!Oe(o));r.length===1&&(n.label=r[0].label)}}i(e,"visitor"),We(t,e,"bottomUp")}i(qr,"labelVirtualInherited");function $n(t){return Object.keys(t).map(e=>{let n;return t[e].test?n=i(r=>t[e].test(r),"matches"):n=t[e],{matches:n,label:e}})}i($n,"buildLabelRules");function ao(t){let n=Fr(t,i(function(r){if(r.subs.length===0||r.subs.findIndex(u=>u.label==="closer"||u.label==="opener")===-1)return r;let o=[],s;for(let u=0;u<r.subs.length;u++){let l=r.subs[u],f=r.subs[u-1];if(l.label==="opener"&&f!==void 0&&Vt(f))f.subs.push(l),l.subs.forEach(_=>f.subs.push(_)),l.subs=[];else if(l.label==="closer"&&s!==void 0&&(Vt(l)||Gt(l))&&l.indentation>=s.indentation){let _=o.length-1;for(;_>0&&Oe(o[_]);)_-=1;if(s.subs.push(...o.splice(_+1)),l.subs.length>0){let v=s.subs.findIndex(W=>W.label!=="newVirtual"),T=s.subs.slice(0,v),M=s.subs.slice(v),D=M.length>0?[io(l.indentation,M,"newVirtual")]:[];s.subs=[...T,...D,l]}else s.subs.push(l)}else o.push(l),Oe(l)||(s=l)}return r.subs=o,r},"rebuilder"));return Aa(t,r=>r==="newVirtual"),n}i(ao,"combineClosersAndOpeners");function Oa(t,e=Oe,n){return Fr(t,i(function(o){if(o.subs.length<=1)return o;let s=[],u=[],l,f=!1;function _(v=!1){if(l!==void 0&&(s.length>0||!v)){let T=io(l,u,n);s.push(T)}else u.forEach(T=>s.push(T))}i(_,"flushBlockIntoNewSubs");for(let v=0;v<o.subs.length;v++){let T=o.subs[v],M=e(T);!M&&f&&(_(),u=[]),f=M,u.push(T),Oe(T)||(l=l??T.indentation)}return _(!0),o.subs=s,o},"rebuilder"))}i(Oa,"groupBlocks");function Qt(t){return Fr(t,i(function(n){return Gt(n)&&n.label===void 0&&n.subs.length<=1?n.subs.length===0?void 0:n.subs[0]:(n.subs.length===1&&Gt(n.subs[0])&&n.subs[0].label===void 0&&(n.subs=n.subs[0].subs),n)},"rebuilder"))}i(Qt,"flattenVirtual");var Uf={opener:/^[[({]/,closer:/^[\])}]/},Wf=$n(Uf),Fa={};function uo(t,e){Fa[t]=e}i(uo,"registerLanguageSpecificParser");function jr(t,e){let n=jf(t),r=Fa[e??""];return r?r(n):(Bn(n,Wf),ao(n))}i(jr,"parseTree");var Bf={package:/^package /,import:/^import /,class:/\bclass /,interface:/\binterface /,javadoc:/^\/\*\*/,comment_multi:/^\/\*[^*]/,comment_single:/^\/\//,annotation:/^@/,opener:/^[[({]/,closer:/^[\])}]/},$f=$n(Bf);function qa(t){let e=t;return Bn(e,$f),e=ao(e),e=Qt(e),qr(e),We(e,n=>{if(n.label==="class"||n.label==="interface")for(let r of n.subs)!Oe(r)&&(r.label===void 0||r.label==="annotation")&&(r.label="member")},"bottomUp"),e}i(qa,"processJava");y();var zf={heading:/^# /,subheading:/^## /,subsubheading:/### /},Hf=$n(zf);function ja(t){let e=t;if(Bn(e,Hf),Oe(e))return e;function n(s){if(s.label==="heading")return 1;if(s.label==="subheading")return 2;if(s.label==="subsubheading")return 3}i(n,"headingLevel");let r=[e],o=[...e.subs];e.subs=[];for(let s of o){let u=n(s);if(u===void 0||Oe(s))r[r.length-1].subs.push(s);else{for(;r.length<u;)r.push(r[r.length-1]);for(r[u-1].subs.push(s),r[u]=s;r.length>u+1;)r.pop()}}return e=Oa(e),e=Qt(e),qr(e),e}i(ja,"processMarkdown");y();function Ua(t){return" ".repeat(t.indentation)+t.sourceLine+`
`}i(Ua,"deparseLine");uo("markdown",ja);uo("java",qa);y();y();var _n=Mt(eu());var Hn=new Map;function tt(t="o200k_base"){let e=Hn.get(t);return e!==void 0?e:Hn.get("o200k_base")}i(tt,"getTokenizer");async function am(t){if(!t.endsWith(".tiktoken.noindex"))throw new Error("File does not end with .tiktoken.noindex");let e=await Sr(t),n=new Map,r=[];for(let o=0;o<e.length;o++){if(e[o]!==255||r.length===0){r.push(e[o]);continue}n.set(Uint8Array.from(r),n.size),r=[]}return n.set(Uint8Array.from(r),n.size),n}i(am,"parseTikTokenNoIndex");var Hr=class t{constructor(e){this._tokenizer=e}static{i(this,"TTokenizer")}static async create(e){try{let n=(0,_n.createTokenizer)(await am(`resources/${e}.tiktoken.noindex`),(0,_n.getSpecialTokensByEncoder)(e),(0,_n.getRegexByEncoder)(e),32768);return new t(n)}catch(n){throw n instanceof Error?new dn("Could not load tokenizer",n):n}}tokenize(e){return this._tokenizer.encode(e)}detokenize(e){return this._tokenizer.decode(e)}tokenLength(e){return this.tokenize(e).length}tokenizeStrings(e){return this.tokenize(e).map(r=>this.detokenize([r]))}takeLastTokens(e,n){if(n<=0)return{text:"",tokens:[]};let r=4,o=1,s=Math.min(e.length,n*r),u=e.slice(-s),l=this.tokenize(u);for(;l.length<n+2&&s<e.length;)s=Math.min(e.length,s+n*o),u=e.slice(-s),l=this.tokenize(u);return l.length<n?{text:e,tokens:l}:(l=l.slice(-n),{text:this.detokenize(l),tokens:l})}takeFirstTokens(e,n){if(n<=0)return{text:"",tokens:[]};let r=4,o=1,s=Math.min(e.length,n*r),u=e.slice(0,s),l=this.tokenize(u);for(;l.length<n+2&&s<e.length;)s=Math.min(e.length,s+n*o),u=e.slice(0,s),l=this.tokenize(u);return l.length<n?{text:e,tokens:l}:(l=l.slice(0,n),{text:this.detokenize(l),tokens:l})}takeLastLinesTokens(e,n){let{text:r}=this.takeLastTokens(e,n);if(r.length===e.length||e[e.length-r.length-1]===`
`)return r;let o=r.indexOf(`
`);return r.substring(o+1)}},bo=class{constructor(){this.hash=i(e=>{let n=0;for(let r=0;r<e.length;r++){let o=e.charCodeAt(r);n=(n<<5)-n+o,n&=n&65535}return n},"hash")}static{i(this,"MockTokenizer")}tokenize(e){return this.tokenizeStrings(e).map(this.hash)}detokenize(e){return e.map(n=>n.toString()).join(" ")}tokenizeStrings(e){return e.split(/\b/)}tokenLength(e){return this.tokenizeStrings(e).length}takeLastTokens(e,n){let r=this.tokenizeStrings(e).slice(-n);return{text:r.join(""),tokens:r.map(this.hash)}}takeFirstTokens(e,n){let r=this.tokenizeStrings(e).slice(0,n);return{text:r.join(""),tokens:r.map(this.hash)}}takeLastLinesTokens(e,n){let{text:r}=this.takeLastTokens(e,n);if(r.length===e.length||e[e.length-r.length-1]===`
`)return r;let o=r.indexOf(`
`);return r.substring(o+1)}};var Uh=(async()=>{Hn.set("mock",new bo),Hn.set("cl100k_base",await Hr.create("cl100k_base")),Hn.set("o200k_base",await Hr.create("o200k_base"))})();var um={worthUp:.9,worthSibling:.88,worthDown:.8};function wo(t,e,n=tt(),r=um){let o=Zt(t,s=>s?1:void 0);return We(o,s=>{if(Oe(s))return;let u=s.subs.reduce((l,f)=>Math.max(l,f.label??0),0);s.label=Math.max(s.label??0,u*r.worthUp)},"bottomUp"),We(o,s=>{if(Oe(s))return;let u=s.subs.map(_=>_.label??0),l=[...u];for(let _=0;_<u.length;_++)u[_]!==0&&(l=l.map((v,T)=>Math.max(v,Math.pow(r.worthSibling,Math.abs(_-T))*u[_])));let f=s.label;f!==void 0&&(l=l.map(_=>Math.max(_,r.worthDown*f))),s.subs.forEach((_,v)=>_.label=l[v])},"topDown"),lm(o,e,n)}i(wo,"fromTreeWithFocussedLines");function lm(t,e,n=tt()){let r=so(t,[],(o,s)=>((o.type==="line"||o.type==="blank")&&s.push(o.type==="line"?[Ua(o).trimEnd(),o.label??0]:["",o.label??0]),s),"topDown");return new Jt(r,e,n)}i(lm,"fromTreeWithValuedLines");function tu(t,e=!0,n=!0,r,o=tt()){let s=typeof t=="string"?jr(t):jr(t.source,t.languageId);Qt(s);let u=Zt(s,l=>e&&l!=="closer");return We(u,l=>{l.label===void 0&&(l.label=e&&l.label!==!1)},"topDown"),e&&We(u,l=>{if(l.label){let f=!1;for(let _ of[...l.subs].reverse())_.label&&!f?f=!0:_.label=!1}else for(let f of l.subs)f.label=!1;l.subs.length>0&&(l.label=!1)},"topDown"),n&&We(u,l=>{l.label||=(Vt(l)||Oe(l))&&l.lineNumber==0},"topDown"),wo(u,r,o)}i(tu,"elidableTextForSourceCode");y();var Vn=class t{constructor(e,n,r,o="strict",s){this.text=e;this._value=n;this._cost=r;this.metadata=s;if(e.includes(`
`)&&o!=="none")throw new Error("LineWithValueAndCost: text contains newline");if(n<0&&o!=="none")throw new Error("LineWithValueAndCost: value is negative");if(r<0&&o!=="none")throw new Error("LineWithValueAndCost: cost is negative");if(o=="strict"&&n>1)throw new Error("Value should normally be between 0 and 1 -- set validation to `loose` to ignore this error")}static{i(this,"LineWithValueAndCost")}get value(){return this._value}get cost(){return this._cost}adjustValue(e){return this._value*=e,this}setValue(e){return this._value=e,this}recost(e=n=>tt().tokenLength(n+`
`)){return this._cost=e(this.text),this}copy(){return new t(this.text,this.value,this.cost,"none",this.metadata)}};var nu="elidableTextChunk",Jt=class t{constructor(e,n,r=tt()){this.metadata=n;this.tokenizer=r;this.lines=[];let o=[];for(let s of e){let u=Array.isArray(s)?s[1]:1,l=Array.isArray(s)?s[0]:s;typeof l=="string"?l.split(`
`).forEach(f=>o.push(new Vn(f,u,r.tokenLength(f+`
`),"strict",this.metadata))):l instanceof t?l.lines.forEach(f=>o.push(f.copy().adjustValue(u))):"source"in l&&"languageId"in l&&tu(l).lines.forEach(f=>o.push(f.copy().adjustValue(u)))}this.lines=o}static{i(this,"ElidableText")}adjust(e){this.lines.forEach(n=>n.adjustValue(e))}recost(e=n=>tt().tokenLength(n+`
`)){this.lines.forEach(n=>n.recost(e))}elide(e,n="[...]",r=!0,o="removeLeastDesirable",s=this.tokenizer,u="topToBottom"){let l=this.lines.map(f=>f.copy());return cm(l,e,n,r,o,s,u)}};function cm(t,e,n,r,o,s,u){if(s.tokenLength(n+`
`)>e)throw new Error("maxTokens must be larger than the ellipsis length");let l=0,f=0,_=0;for(let P of t)l+=P.cost,f=Math.max(f,P.value),_=Math.max(_,P.text.length),o==="removeLeastBangForBuck"&&P.adjustValue(1/P.cost);let v=f+1,T=_+1,M=n.trim(),D=i(P=>P?.text.match(/^\s*/)?.[0].length??0,"getIndentation"),W=i(P=>P?.text.trim()===M,"isEllipsis"),F=t.length+1;for(;l>e&&F-- >0;){let P=-1,z=1/0;for(let J=0;J<t.length;J++){let ie=t[J];(ie.value<z||ie.value===z&&u==="bottomToTop")&&(z=ie.value,P=J)}let L=t[P],H=L.metadata?.get(nu);if(H!==void 0&&H.size>0)for(let J of t){let ie=J.metadata?.get(nu);ie&&ie.size>0&&[...H].every(Te=>ie.has(Te))&&J.text.trim()!==M&&J.adjustValue(0)}let Y=0;if(r){let J=t.slice(0,P+1).reverse().find(Ce=>Ce.text.trim()!==""),ie=t[P-1],Te=t[P+1];Y=Math.min(J?D(J):0,W(ie)?D(ie):T,W(Te)?D(Te):T)}let K=" ".repeat(Y)+n,le=new Vn(K,v,s.tokenLength(K+`
`),"loose",L.metadata);l-=L.cost,t.splice(P,1,le),l+=le.cost,W(t[P+1])&&(l-=t[P+1].cost,t.splice(P+1,1)),W(t[P-1])&&(l-=t[P-1].cost,t.splice(P-1,1),P--),l>e&&t.every(J=>J.value===v)&&(r=!1)}if(F<0)throw new Error("Infinite loop in ElidableText.makePrompt: Defensive counter < 0 in ElidableText.makePrompt with end text");t=t.filter(P=>P.value!==0);for(let P=t.length-1;P>0;P--)W(t[P])&&W(t[P-1])&&t.splice(P,1);return{getText:i(()=>t.map(P=>P.text).join(`
`),"getText"),getLines:i(()=>t,"getLines")}}i(cm,"elide");y();y();function lt(){}i(lt,"Diff");lt.prototype={diff:i(function(e,n){var r,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=o.callback;typeof o=="function"&&(s=o,o={});var u=this;function l(Y){return Y=u.postProcess(Y,o),s?(setTimeout(function(){s(Y)},0),!0):Y}i(l,"done"),e=this.castInput(e,o),n=this.castInput(n,o),e=this.removeEmpty(this.tokenize(e,o)),n=this.removeEmpty(this.tokenize(n,o));var f=n.length,_=e.length,v=1,T=f+_;o.maxEditLength!=null&&(T=Math.min(T,o.maxEditLength));var M=(r=o.timeout)!==null&&r!==void 0?r:1/0,D=Date.now()+M,W=[{oldPos:-1,lastComponent:void 0}],F=this.extractCommon(W[0],n,e,0,o);if(W[0].oldPos+1>=_&&F+1>=f)return l(ru(u,W[0].lastComponent,n,e,u.useLongestToken));var P=-1/0,z=1/0;function L(){for(var Y=Math.max(P,-v);Y<=Math.min(z,v);Y+=2){var K=void 0,le=W[Y-1],J=W[Y+1];le&&(W[Y-1]=void 0);var ie=!1;if(J){var Te=J.oldPos-Y;ie=J&&0<=Te&&Te<f}var Ce=le&&le.oldPos+1<_;if(!ie&&!Ce){W[Y]=void 0;continue}if(!Ce||ie&&le.oldPos<J.oldPos?K=u.addToPath(J,!0,!1,0,o):K=u.addToPath(le,!1,!0,1,o),F=u.extractCommon(K,n,e,Y,o),K.oldPos+1>=_&&F+1>=f)return l(ru(u,K.lastComponent,n,e,u.useLongestToken));W[Y]=K,K.oldPos+1>=_&&(z=Math.min(z,Y-1)),F+1>=f&&(P=Math.max(P,Y+1))}v++}if(i(L,"execEditLength"),s)i(function Y(){setTimeout(function(){if(v>T||Date.now()>D)return s();L()||Y()},0)},"exec")();else for(;v<=T&&Date.now()<=D;){var H=L();if(H)return H}},"diff"),addToPath:i(function(e,n,r,o,s){var u=e.lastComponent;return u&&!s.oneChangePerToken&&u.added===n&&u.removed===r?{oldPos:e.oldPos+o,lastComponent:{count:u.count+1,added:n,removed:r,previousComponent:u.previousComponent}}:{oldPos:e.oldPos+o,lastComponent:{count:1,added:n,removed:r,previousComponent:u}}},"addToPath"),extractCommon:i(function(e,n,r,o,s){for(var u=n.length,l=r.length,f=e.oldPos,_=f-o,v=0;_+1<u&&f+1<l&&this.equals(r[f+1],n[_+1],s);)_++,f++,v++,s.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return v&&!s.oneChangePerToken&&(e.lastComponent={count:v,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=f,_},"extractCommon"),equals:i(function(e,n,r){return r.comparator?r.comparator(e,n):e===n||r.ignoreCase&&e.toLowerCase()===n.toLowerCase()},"equals"),removeEmpty:i(function(e){for(var n=[],r=0;r<e.length;r++)e[r]&&n.push(e[r]);return n},"removeEmpty"),castInput:i(function(e){return e},"castInput"),tokenize:i(function(e){return Array.from(e)},"tokenize"),join:i(function(e){return e.join("")},"join"),postProcess:i(function(e){return e},"postProcess")};function ru(t,e,n,r,o){for(var s=[],u;e;)s.push(e),u=e.previousComponent,delete e.previousComponent,e=u;s.reverse();for(var l=0,f=s.length,_=0,v=0;l<f;l++){var T=s[l];if(T.removed)T.value=t.join(r.slice(v,v+T.count)),v+=T.count;else{if(!T.added&&o){var M=n.slice(_,_+T.count);M=M.map(function(D,W){var F=r[v+W];return F.length>D.length?F:D}),T.value=t.join(M)}else T.value=t.join(n.slice(_,_+T.count));_+=T.count,T.added||(v+=T.count)}}return s}i(ru,"buildValues");var by=new lt;function iu(t,e){var n;for(n=0;n<t.length&&n<e.length;n++)if(t[n]!=e[n])return t.slice(0,n);return t.slice(0,n)}i(iu,"longestCommonPrefix");function ou(t,e){var n;if(!t||!e||t[t.length-1]!=e[e.length-1])return"";for(n=0;n<t.length&&n<e.length;n++)if(t[t.length-(n+1)]!=e[e.length-(n+1)])return t.slice(-n);return t.slice(-n)}i(ou,"longestCommonSuffix");function vo(t,e,n){if(t.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return n+t.slice(e.length)}i(vo,"replacePrefix");function Eo(t,e,n){if(!e)return t+n;if(t.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(t)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return t.slice(0,-e.length)+n}i(Eo,"replaceSuffix");function Gn(t,e){return vo(t,e,"")}i(Gn,"removePrefix");function Vr(t,e){return Eo(t,e,"")}i(Vr,"removeSuffix");function su(t,e){return e.slice(0,dm(t,e))}i(su,"maximumOverlap");function dm(t,e){var n=0;t.length>e.length&&(n=t.length-e.length);var r=e.length;t.length<e.length&&(r=t.length);var o=Array(r),s=0;o[0]=0;for(var u=1;u<r;u++){for(e[u]==e[s]?o[u]=o[s]:o[u]=s;s>0&&e[u]!=e[s];)s=o[s];e[u]==e[s]&&s++}s=0;for(var l=n;l<t.length;l++){for(;s>0&&t[l]!=e[s];)s=o[s];t[l]==e[s]&&s++}return s}i(dm,"overlapCount");var Gr="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",fm=new RegExp("[".concat(Gr,"]+|\\s+|[^").concat(Gr,"]"),"ug"),Zr=new lt;Zr.equals=function(t,e,n){return n.ignoreCase&&(t=t.toLowerCase(),e=e.toLowerCase()),t.trim()===e.trim()};Zr.tokenize=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n;if(e.intlSegmenter){if(e.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');n=Array.from(e.intlSegmenter.segment(t),function(s){return s.segment})}else n=t.match(fm)||[];var r=[],o=null;return n.forEach(function(s){/\s/.test(s)?o==null?r.push(s):r.push(r.pop()+s):/\s/.test(o)?r[r.length-1]==o?r.push(r.pop()+s):r.push(o+s):r.push(s),o=s}),r};Zr.join=function(t){return t.map(function(e,n){return n==0?e:e.replace(/^\s+/,"")}).join("")};Zr.postProcess=function(t,e){if(!t||e.oneChangePerToken)return t;var n=null,r=null,o=null;return t.forEach(function(s){s.added?r=s:s.removed?o=s:((r||o)&&au(n,o,r,s),n=s,r=null,o=null)}),(r||o)&&au(n,o,r,null),t};function au(t,e,n,r){if(e&&n){var o=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],u=n.value.match(/^\s*/)[0],l=n.value.match(/\s*$/)[0];if(t){var f=iu(o,u);t.value=Eo(t.value,u,f),e.value=Gn(e.value,f),n.value=Gn(n.value,f)}if(r){var _=ou(s,l);r.value=vo(r.value,l,_),e.value=Vr(e.value,_),n.value=Vr(n.value,_)}}else if(n)t&&(n.value=n.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(t&&r){var v=r.value.match(/^\s*/)[0],T=e.value.match(/^\s*/)[0],M=e.value.match(/\s*$/)[0],D=iu(v,T);e.value=Gn(e.value,D);var W=ou(Gn(v,D),M);e.value=Vr(e.value,W),r.value=vo(r.value,v,W),t.value=Eo(t.value,v,v.slice(0,v.length-W.length))}else if(r){var F=r.value.match(/^\s*/)[0],P=e.value.match(/\s*$/)[0],z=su(P,F);e.value=Vr(e.value,z)}else if(t){var L=t.value.match(/\s*$/)[0],H=e.value.match(/^\s*/)[0],Y=su(L,H);e.value=Gn(e.value,Y)}}i(au,"dedupeWhitespaceInChangeObjects");var mm=new lt;mm.tokenize=function(t){var e=new RegExp("(\\r?\\n)|[".concat(Gr,"]+|[^\\S\\n\\r]+|[^").concat(Gr,"]"),"ug");return t.match(e)||[]};var xo=new lt;xo.tokenize=function(t,e){e.stripTrailingCr&&(t=t.replace(/\r\n/g,`
`));var n=[],r=t.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var o=0;o<r.length;o++){var s=r[o];o%2&&!e.newlineIsToken?n[n.length-1]+=s:n.push(s)}return n};xo.equals=function(t,e,n){return n.ignoreWhitespace?((!n.newlineIsToken||!t.includes(`
`))&&(t=t.trim()),(!n.newlineIsToken||!e.includes(`
`))&&(e=e.trim())):n.ignoreNewlineAtEof&&!n.newlineIsToken&&(t.endsWith(`
`)&&(t=t.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),lt.prototype.equals.call(this,t,e,n)};var _m=new lt;_m.tokenize=function(t){return t.split(/(\S.+?[.!?])(?=\s+|$)/)};var gm=new lt;gm.tokenize=function(t){return t.split(/([{}:;,]|\s+)/)};function To(t){"@babel/helpers - typeof";return To=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},To(t)}i(To,"_typeof");var Zn=new lt;Zn.useLongestToken=!0;Zn.tokenize=xo.tokenize;Zn.castInput=function(t,e){var n=e.undefinedReplacement,r=e.stringifyReplacer,o=r===void 0?function(s,u){return typeof u>"u"?n:u}:r;return typeof t=="string"?t:JSON.stringify(So(t,null,null,o),o,"  ")};Zn.equals=function(t,e,n){return lt.prototype.equals.call(Zn,t.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),n)};function So(t,e,n,r,o){e=e||[],n=n||[],r&&(t=r(o,t));var s;for(s=0;s<e.length;s+=1)if(e[s]===t)return n[s];var u;if(Object.prototype.toString.call(t)==="[object Array]"){for(e.push(t),u=new Array(t.length),n.push(u),s=0;s<t.length;s+=1)u[s]=So(t[s],e,n,r,o);return e.pop(),n.pop(),u}if(t&&t.toJSON&&(t=t.toJSON()),To(t)==="object"&&t!==null){e.push(t),u={},n.push(u);var l=[],f;for(f in t)Object.prototype.hasOwnProperty.call(t,f)&&l.push(f);for(l.sort(),s=0;s<l.length;s+=1)f=l[s],u[f]=So(t[f],e,n,r,f);e.pop(),n.pop()}else u=t;return u}i(So,"canonicalize");var Ro=new lt;Ro.tokenize=function(t){return t.slice()};Ro.join=Ro.removeEmpty=function(t){return t};var uu=require("fs");async function lu(t,e,n,r,o){let s=new Map,u=0;for(let T of t){let M=T.node.fileName.toLowerCase(),D=s.get(M);D?D.symbols.push(T):s.set(M,{symbols:[T],topRank:u}),u++}if(s.delete(n.toLowerCase()),s.size===0)return[];let l=e,f=e/Math.min(4,s.size),_=[],v=Array.from(s.keys());v.sort((T,M)=>{let D=s.get(T).topRank,W=s.get(M).topRank;return D-W});for(let T of v){let M=s.get(T).symbols;if(M.length===0)continue;let D=M[0].node.fileName,W=M.reduce((F,P)=>F+P.node.extentRange.length,0);for(let F of M){if(l<=5)return _;Tt(o);let P=F.node.extentRange.length/W,z=Math.min(l,P*f),L=(await hm([F])).elide(z).getText();l-=r.tokenLength(L),_.push({uri:D,value:L})}}return _}i(lu,"symbolRangesToCodeSnippets");async function hm(t){if(t.length===0)return new Jt([]);let e=t[0].node.fileName,n=[],r="";try{let o=vr(e);o&&(r=(await uu.promises.readFile(o)).toString())}catch{}for(let o of t)cu(o,r).forEach(s=>n.push(s));return new Jt(n)}i(hm,"sameFileSymbolRangeToElidableText");function cu(t,e){let r=[],o=t.node,s=e.substring(Io(e,o.commentRange.start),o.commentRange.end);r.push([No(s),1-3e-4]);let u=Io(e,o.bodyRange.start),l=Io(e,o.extentRange.start),f=o.bodyRange.length===0?e.substring(l,o.extentRange.end):e.substring(o.commentRange.length===0?l:o.commentRange.end,u);if(Ht(o.kind)&&(f="BEGIN "+f.trimStart()),r.push([No(f),1-1e-4]),t.children.length>0)for(let _ of t.children)r.push(...cu(_,e));else{let _=1-(Ht(t.node.kind)?3e-4:4e-4),v=e.substring(u,o.bodyRange.end);r.push([No(v),_])}return Ht(o.kind)&&r.push(["END "+f.substring(6),1-1e-4]),r.filter(_=>_[0].length>0)}i(cu,"prepareForElidableText");function Io(t,e){for(;e-1>=0&&(t[e-1]==="	"||t[e-1]===" ");)e--;return e}i(Io,"shiftLeftToNearestLineEndingOrAlphanumeric");function No(t){let e=0;for(;e<t.length&&(t[e]==="\r"||t[e]===`
`);)e++;let n=t.length-1;for(;n>=0&&(t[n]==="\r"||t[n]===`
`||t[n]===" "||t[n]==="	");)n--;return t.substring(e,n+1)}i(No,"trimLineEndingsAndTrailingWhitespace");var Mo=class{constructor(e,n){this.referenceExtractors=new Map;this.index=e;for(let r of n)this.referenceExtractors.set(r.languageId,r)}static{i(this,"ContextRetrievalStrategy")}},Lt=class extends Mo{static{i(this,"UnqualifiedNameRetrievalStrategy")}constructor(e,n,r,o){super(e,n),this.caseSensitive=r,this.typesOnly=o}async getContextAtPositionAsync(e,n,r,o,s,u){let l=this.referenceExtractors.get(o);if(!l)return[];Tt(u);let f=await l.extractReferences(e,n);Tt(u);let _=Array.from(f);_.sort((D,W)=>this.compareSymbolRangesByProximityToCaret(D,W,r));let v=[],T=new Set,M=await this.findDefinitionsViaUnqualifiedNames(_,s,u);this.typesOnly&&(M=M.filter(D=>Ht(D.kind)));for(let D of M)T.has(JSON.stringify(D))||(T.add(JSON.stringify(D)),v.push(await this.makeSymbolRangeNodeFromDefinition(D,u)));return v}async findDefinitionsViaUnqualifiedNames(e,n,r){let o=Array.from(new Set(e.map(T=>T.unqualifiedName))),s=!this.caseSensitive,u=await this.index.findPotentialDefinitionsAsync(o,s,r),l=i(T=>s?T.toLowerCase():T,"lowercaseIfCaseInsensitive"),f=i(T=>l(T.unqualifiedName),"getSymbolKey"),_=new Map;for(let T of u){let M=f(T),D=_.get(M)??[];_.set(M,[...D,T])}let v=[];for(let T of o){let M=_.get(l(T));if(!(!M||M.length>n.mlcpMaxSymbolMatches)){if(v.length+M.length>n.mlcpMaxContextItems){v.push(...M.slice(0,n.mlcpMaxContextItems-v.length));break}v.push(...M)}}return v}async makeSymbolRangeNodeFromDefinition(e,n){if(Tt(n),Ht(e.kind)){let r=await this.index.findSymbolsByFullyQualifiedNamePrefix(e.fileName,e.fullyQualifiedName+".",n),o=await Promise.all(r.map(s=>this.makeSymbolRangeNodeFromDefinition(s,n)));return{node:e,children:o}}else return{node:e,children:[]}}compareSymbolRangesByProximityToCaret(e,n,r){let o=e.extentRange.end<=r,s=n.extentRange.end<=r;if(o&&!s)return-1;if(!o&&s)return 1;let u=Math.abs(e.extentRange.start-r),l=Math.abs(n.extentRange.start-r);return u-l}},Qr=class{constructor(e,n){this.strategies=new Map;this.strategies.set("go",new Lt(e,n,!0,!0)),this.strategies.set("java",new Lt(e,n,!0,!0)),this.strategies.set("python",new Lt(e,n,!0,!1)),this.strategies.set("typescript",new Lt(e,n,!0,!0)),this.strategies.set("typescriptreact",new Lt(e,n,!0,!0)),this.strategies.set("javascript",new Lt(e,n,!0,!1))}static{i(this,"SyntaxAwareContextRetrieval")}async getStringifiedContextAtPositionAsync(e,n,r,o,s,u,l){let f=await this.getContextAtPositionAsync(e,n,r,o,s,l);Tt(l);let _=tt();return lu(f,u,e,_,l)}async getContextAtPositionAsync(e,n,r,o,s,u){return this.strategies.has(o)?this.strategies.get(o).getContextAtPositionAsync(e,n,r,o,s,u):[]}};y();y();y();y();var Xt={Id:"id"},Ye={FilePath:"filePath",LastWriteTimeUtc:"lastWriteTimeUtc"},se={DocumentId:"documentId",FullyQualifiedName:"fullyQualifiedName",UnqualifiedName:"unqualifiedName",CommentStart:"commentStart",CommentLength:"commentLength",NameStart:"nameStart",NameLength:"nameLength",BodyStart:"bodyStart",BodyLength:"bodyLength",ExtentStart:"extentStart",ExtentLength:"extentLength",SymbolKind:"symbolKind",RefKind:"refKind"};y();y();var Jr=class{constructor(e,n,r){this.tableName=e;this.createOptimizations=n;this.extraCreateDeclarations=r;this.primaryKey=new ko(Xt.Id)}static{i(this,"SQLTableQueryGenerator")}createTableQueries(){return this.createTableString??=this.generateCreateTableString(),[this.createTableString,...this.createOptimizations]}generateCreateTableString(){let e=this.fields.map(n=>n.initColumnString());return this.extraCreateDeclarations&&e.push(this.extraCreateDeclarations),e.push(),[`CREATE TABLE IF NOT EXISTS ${this.tableName} (`,`    ${this.primaryKey.initColumnString()},`,`    ${e.join(`,
`)}`,")"].join(`
`)}insertQuery(e,n){this.cachedInsertQueryStrings??=this.generateInsertQueryStrings();let r=[this.cachedInsertQueryStrings.prefix,Array(n).fill(this.cachedInsertQueryStrings.valuesTemplate).join(`,
`)];return e&&r.push("ON CONFLICT DO NOTHING"),r.join(`
`)}generateInsertQueryStrings(){return{prefix:`INSERT INTO ${this.tableName} (${this.fields.map(e=>e.name).join(", ")})
VALUES`,valuesTemplate:`(${Array(this.fields.length).fill("?").join(", ")})`}}},Qn=class t extends Jr{constructor(){super(t.tableName,[`CREATE UNIQUE INDEX IF NOT EXISTS 'IX_Document_FilePath' ON '${t.tableName}' ('${Ye.FilePath}');`]);this.fields=[new Yn(Ye.FilePath,{notNull:!0,collate:!0,noCase:!0,unique:!0}),new Ve(Ye.LastWriteTimeUtc,{notNull:!0})]}static{i(this,"DocumentQueryGenerator")}static{this.tableName="Document"}},Jn=class t extends Jr{constructor(){super(t.tableName,[`CREATE INDEX IF NOT EXISTS 'IX_Symbol_DocumentId' ON '${t.tableName}' ('${se.DocumentId}', '${se.ExtentStart}', '${se.ExtentLength}');`,`CREATE INDEX IF NOT EXISTS 'IX_Symbol_UnqualifiedName' ON '${t.tableName}' ('${se.UnqualifiedName}');`],`FOREIGN KEY(${se.DocumentId}) REFERENCES Document(${Xt.Id}) ON DELETE CASCADE`);this.fields=[new Ve(se.DocumentId),new Yn(se.FullyQualifiedName,{notNull:!0}),new Yn(se.UnqualifiedName,{notNull:!0}),new Ve(se.CommentStart,{notNull:!0}),new Ve(se.CommentLength,{notNull:!0}),new Ve(se.NameStart,{notNull:!0}),new Ve(se.NameLength,{notNull:!0}),new Ve(se.BodyStart,{notNull:!0}),new Ve(se.BodyLength,{notNull:!0}),new Ve(se.ExtentStart,{notNull:!0}),new Ve(se.ExtentLength,{notNull:!0}),new Ve(se.SymbolKind,{notNull:!0}),new Ve(se.RefKind,{notNull:!0})]}static{i(this,"SymbolQueryGenerator")}static{this.tableName="Symbol"}},Xn=class{constructor(e,n){this.name=e;this.notNull=n?.notNull??!1}static{i(this,"SQLField")}},Yn=class extends Xn{static{i(this,"StringColumn")}constructor(e,n){super(e,{notNull:n?.notNull}),this.collate=n?.collate??!1,this.noCase=n?.noCase??!1,this.unique=n?.unique??!1}initColumnString(){let e=this.collate?"COLLATE":void 0,n=this.noCase?"NOCASE":void 0,r=this.notNull?"NOT NULL":void 0,o=this.unique?"UNIQUE":void 0,s=[e,n,r,o].filter(u=>u).join(" ");return`'${this.name}' VARCHAR(500) ${s}`}},Ve=class extends Xn{static{i(this,"NumberColumn")}initColumnString(){return`'${this.name}' INTEGER${this.notNull?" NOT NULL":""}`}},ko=class extends Xn{static{i(this,"NumberPrimaryKeyColumn")}initColumnString(){return`'${this.name}' INTEGER PRIMARY KEY AUTOINCREMENT${this.notNull?" NOT NULL":""}`}};var Xr=class{static{i(this,"SQLTable")}constructor(e){this.queryGenerator=this.createQueryGenerator(),this.init=this.doInit(e)}async doInit(e){let n=await e;if(n)return await this.create(n),n}async create(e){let n=i(o=>new Promise((s,u)=>{e.run(o,l=>{l?u(l):s()})}),"runQuery"),r=this.queryGenerator.createTableQueries();for(let o of r)await n(o)}async insert(e,n){let r=await this.init;r&&await new Promise((o,s)=>{let u=this.queryGenerator.insertQuery(n,e.length);r.run(u,e.map(l=>Object.values(l)).flat(),function(l){l?s(l):o(this)})})}async getAllRows(){let e=await this.init;return e?nt(e,`SELECT * FROM ${this.queryGenerator.tableName}`,[]):[]}async deleteRow(e){let n=await this.init;if(n)return nt(n,`DELETE FROM ${this.queryGenerator.tableName} WHERE ${Xt.Id} = ?`,[e])}},St=class t extends Xr{static{i(this,"DocumentTable")}static{this.tableName=Qn.tableName}createQueryGenerator(){return new Qn}async updateTimestamp(e,n){let r=await this.init;r&&await nt(r,`UPDATE ${t.tableName} SET ${Ye.LastWriteTimeUtc} = ? WHERE ${Ye.FilePath} = ?`,[n,e])}async deleteAllWithPath(e){let n=await this.init;n&&await nt(n,`DELETE FROM ${t.tableName} WHERE ${Ye.FilePath} = ?`,[e])}async getDocumentByFilePath(e){let n=await this.init;return n?nt(n,`SELECT * FROM ${this.queryGenerator.tableName} WHERE ${Ye.FilePath} = ?`,[e]):[]}},ke=class t extends Xr{static{i(this,"SymbolTable")}static{this.tableName=Jn.tableName}createQueryGenerator(){return new Jn}async clearAllSymbolsFromDocument(e){let n=await this.init;if(n)return nt(n,`DELETE FROM ${t.tableName} WHERE ${se.DocumentId} = ?`,[e])}};async function nt(t,e,n){return new Promise((r,o)=>t.all(e,n,(s,u)=>{s&&o(s),r(u)}))}i(nt,"runPromisifiedDBQuery");var ei=class{constructor(e){this.databaseFileName=e;this.innerJoinStatement=`INNER JOIN ${St.tableName} ON ${ke.tableName}.${se.DocumentId} = ${St.tableName}.${Xt.Id}`;this.db=this.initDb(e),this.documentTable=new St(this.db),this.symbolTable=new ke(this.db)}static{i(this,"DocumentSymbolDatabase")}async initDb(e){let n;try{n=await Promise.resolve().then(()=>Mt(Tu()))}catch(o){console.error("Error loading sqlite3 module:",o);return}let r;try{r=await new Promise((o,s)=>{let u=new n.default.Database(e,l=>{l?s(l):o(u)})})}catch(o){console.error("Error initializing database:",o);return}return await nt(r,"pragma journal_mode=wal",[]),await nt(r,"pragma synchronous=normal",[]),await nt(r,"pragma optimize=0x10002",[]),await nt(r,"pragma foreign_keys = ON",[]),r}async close(){let e=await this.db;if(e)return await this.documentTable.init,await this.symbolTable.init,new Promise((n,r)=>{e.close(o=>o?r(o):n())})}async querySymbolsFuzzilyUsingUnqualifiedName(e,n){return await this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${se.UnqualifiedName} BETWEEN ? AND (? || '~')`,`ORDER BY ${se.UnqualifiedName}`,"LIMIT ?"].join(`
`),[e,e,n])}async querySymbolsUsingUnqualifiedNames(e,n){let r=e.map(()=>"?").join(", "),o=n?"COLLATE NOCASE ":"";return await this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${se.UnqualifiedName} ${o}IN (${r})`].join(`
`),e)}async querySymbolsUsingFullyQualifiedName(e){return this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${se.FullyQualifiedName} = ?`].join(`
`),[e])}async querySymbolsContainingPosition(e,n){return this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${St.tableName}.${Ye.FilePath} = ? AND ${ke.tableName}.${se.ExtentStart} <= ? AND (${ke.tableName}.${se.ExtentStart} + ${ke.tableName}.${se.ExtentLength}) >= ?`].join(`
`),[e,n,n])}async querySymbolsContainedByRange(e,n,r){return this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${St.tableName}.${Ye.FilePath} = ? AND ${ke.tableName}.${se.ExtentStart} >= ? AND (${ke.tableName}.${se.ExtentStart} + ${ke.tableName}.${se.ExtentLength}) <= ?`,`ORDER BY ${ke.tableName}.${se.ExtentStart}`].join(`
`),[e,n,r])}async querySymbolsByQualifiedNamePrefix(e,n){return this.query([`SELECT * FROM ${ke.tableName}`,this.innerJoinStatement,`WHERE ${St.tableName}.${Ye.FilePath} = ? AND ${ke.tableName}.${se.FullyQualifiedName} LIKE ?`,`ORDER BY ${ke.tableName}.${se.ExtentStart}`].join(`
`),[e,`${n}%`])}async insertSymbols(e){return await this.symbolTable.insert(e,!1)}async insertDocument(e,n,r=!1){return await this.documentTable.insert([{filePath:e,lastWriteTimeUtc:n}],r)}async clearAllSymbolsFromDocument(e){return this.symbolTable.clearAllSymbolsFromDocument(e)}async updateDocumentTimestamp(e,n){return this.documentTable.updateTimestamp(e,n)}async deleteAllDocumentsWithPath(e){return this.documentTable.deleteAllWithPath(e)}async getAllDocuments(){return this.documentTable.getAllRows()}getDocument(e){return this.documentTable.getDocumentByFilePath(e)}async query(e,n){let r=await this.db;return r?(await this.documentTable.init,await this.symbolTable.init,nt(r,e,n)):[]}};var ti=class{static{i(this,"SQLStorageReaderWriter")}constructor(e){this.database=new ei(e)}async close(){await this.database.close()}async insertOrReplaceDocumentSymbolsAsync(e,n,r){let o=await this.getOrCreateDocumentAsync(e,n);await this.database.clearAllSymbolsFromDocument(o.id),r.length>0&&await this.database.insertSymbols(r.map(s=>({documentId:o.id,fullyQualifiedName:s.fullyQualifiedName,unqualifiedName:s.unqualifiedName,commentStart:s.commentRange.start,commentLength:s.commentRange.length,nameStart:s.nameRange.start,nameLength:s.nameRange.length,bodyStart:s.bodyRange.start,bodyLength:s.bodyRange.length,extentStart:s.extentRange.start,extentLength:s.extentRange.length,symbolKind:s.kind,refKind:s.refKind}))),await this.database.updateDocumentTimestamp(e,n)}async addDocumentsAsync(e){for(let n of e)n=n.toLowerCase(),await this.database.insertDocument(n,Date.now(),!0)}async deleteDocumentAsync(e){return await this.database.deleteAllDocumentsWithPath(e.toLowerCase())}async updateDocumentTimestampAsync(e,n){return await this.database.updateDocumentTimestamp(e.toLowerCase(),n)}async fuzzyMatchSymbolsAsync(e,n){return hn(await this.database.querySymbolsFuzzilyUsingUnqualifiedName(e,n))}async findPotentialDefinitionsAsync(e,n,r){let o=[],u=0;for(;u<e.length;){let l=e.slice(u,u+500);u+=500;let f=await this.database.querySymbolsUsingUnqualifiedNames(l,n);o.push(...f),Tt(r)}return hn(o)}async getDefinitionsAsync(e){return hn(await this.database.querySymbolsUsingFullyQualifiedName(e))}async findSymbolsContainingPositionAsync(e,n){let r=await this.database.querySymbolsContainingPosition(e.toLowerCase(),n);return hn(r)}async findSymbolsContainedByRangeAsync(e,n,r){return hn(await this.database.querySymbolsContainedByRange(e.toLowerCase(),n,r))}async findSymbolsByFullyQualifiedNamePrefix(e,n){return hn(await this.database.querySymbolsByQualifiedNamePrefix(e.toLowerCase(),n))}async getDocumentsAsync(){return this.database.getAllDocuments()}async getDocumentAsync(e){let n=await this.database.getDocument(e);if(n?.at(0))return n[0]}async getOrCreateDocumentAsync(e,n){let r=await this.database.getDocument(e);if(r?.at(0))return r[0];await this.database.insertDocument(e,n,!0);let o=await this.database.getDocument(e);if(!o)throw Error("Unable to insert document");return o[0]}};function hn(t){return t.map(e=>new Pt(e.filePath,e.fullyQualifiedName,e.unqualifiedName,new He(e.commentStart,e.commentLength),new He(e.nameStart,e.nameLength),new He(e.bodyStart,e.bodyLength),new He(e.extentStart,e.extentLength),e.symbolKind,e.refKind))}i(hn,"symbolsToSymbolRanges");var ri=Mt(require("fs/promises"));var ni=class{static{i(this,"Index")}constructor(e,n){this.storage=new ti(e),this.symbolExtractors=n}dispose(){return this.storage.close()}get reader(){return this.storage}async indexFile(e,n){let r=vr(e);if(!r)throw Error(`Cannot resolve a readable file path from ${e}`);let o;try{o=await ri.stat(r)}catch{await this.storage.deleteDocumentAsync(e);return}let s=o.mtimeMs,u=await this.storage.getDocumentAsync(e);if(u&&u.lastWriteTimeUtc>=s)return;let l=this.symbolExtractors.find(v=>v.languageId==n);if(!l)return;let f=(await ri.readFile(r)).toString(),_=await l.extractSymbols(e,f);await this.storage.insertOrReplaceDocumentSymbolsAsync(e,s,_)}async getDocumentFilePaths(){return(await this.storage.getDocumentsAsync()).map(n=>n.filePath)}};y();function Oo(t){let e=t;return typeof e?.cwd=="string"&&Array.isArray(e?.indexWorkspaceRoots)&&e.indexWorkspaceRoots.every(n=>Sm(n))}i(Oo,"isIndexWorkerData");var Ao=class{static{i(this,"IndexNotification")}constructor(e){this.operation=e}};var Rt={CreateIndex:"createIndex",AddOrInvalidated:"addOrInvalidated",GetContext:"getContext",Exit:"exit",Response:"response",RemoveIndex:"removeIndex",Cancel:"cancel",GetAllDocumentsInWorkspace:"getAllDocumentsInWorkspace"};var ct=class extends Ao{constructor(n,r,o){super(Rt.Response);this.id=n;this.error=r;this.data=o;r&&"code"in r&&typeof r.code=="string"&&(this.code=r.code)}static{i(this,"ResponseMessage")}};function Sm(t){return"databaseFilePath"in t&&"rootPath"in t}i(Sm,"isIndexableWorkspaceFolder");var ef=Mt(Kd()),rn=require("worker_threads");var zi=class{static{i(this,"IndexInfo")}constructor(e){this.index=new ni(e,La),this.contextRetreival=new Qr(this.index.reader,Ca)}},Hi=class t{constructor(e,n){this.indices=new Map;this.cancellationTokens=new Map;for(let r of n){let o=kt(r.rootPath);this.indices.set(o,new zi(r.databaseFilePath))}this.port=e,this.port.on("message",r=>void this.dispatchMessage(r,this.indices,this.cancellationTokens))}static{i(this,"IndexWorker")}async dispatchMessage(e,n,r){try{let o=new ef.CancellationTokenSource;r.set(e.id,o);let s;switch(e.operation){case Rt.AddOrInvalidated:s=await t.dispatchAddOrInvalidate(e,n,o.token);break;case Rt.GetContext:s=await t.dispatchGetContext(e,n,o.token);break;case Rt.Cancel:r.get(e.id)?.cancel(),s=new ct(e.id,void 0,void 0);break;case Rt.Exit:s=await this.dispatchExit(e,n,o.token);break;case Rt.CreateIndex:s=await t.dispatchCreateIndex(e,n,o.token);break;case Rt.RemoveIndex:s=await t.dispatchRemoveIndex(e,n,o.token);break;case Rt.GetAllDocumentsInWorkspace:s=await t.GetAllDocumentsInWorkspaceRequest(e,n,o.token);break;default:this.port?.postMessage(new Error(`Unknown operation: ${e.operation}`))}s&&this.port?.postMessage(s),r.get(e.id)?.dispose(),r.delete(e.id)}catch(o){if(!(o instanceof Error))throw o;this.port?.postMessage(new ct(e.id,o,void 0))}}static async GetAllDocumentsInWorkspaceRequest(e,n,r){let o=kt(e.baseWorkspaceFolderUri),s,u;return n.has(o)?u=await n.get(o).index.getDocumentFilePaths():s=new Error(`Index not found for ${e.baseWorkspaceFolderUri}`),new ct(e.id,s,u)}static async dispatchAddOrInvalidate(e,n,r){let o=kt(e.fileUri),s=t.getIndexInfo(o,n)?.index,u;return s?await s.indexFile(e.fileUri,e.languageId):u=new Error(`Index not found for ${e.fileUri}`),new ct(e.id,u,void 0)}static async dispatchGetContext(e,n,r){let o=kt(e.fileUri),s=t.getIndexInfo(o,n)?.contextRetreival,u,l;return s?l=await s.getStringifiedContextAtPositionAsync(e.fileUri,e.code,e.offset,e.languageId,e.params,8e3,r):u=new Error(`ContextRetrieval not found for ${e.fileUri}`),new ct(e.id,u,l)}async dispatchExit(e,n,r){for(let o of n.values())await o.index.dispose();n.clear(),this.port?.postMessage(new ct(e.id,void 0,void 0)),this.port?.close()}static async dispatchCreateIndex(e,n,r){let o=kt(e.baseWorkspaceFolderUri);return n.has(o)||n.set(o,new zi(e.databaseFilePath)),new ct(e.id,void 0,void 0)}static async dispatchRemoveIndex(e,n,r){let o=kt(e.baseWorkspaceFolderUri);if(n.has(o)){let s=n.get(o);s&&await s.index.dispose(),n.delete(o)}return new ct(e.id,void 0,void 0)}static getIndexInfo(e,n){for(let[r,o]of n)if(e.startsWith(r))return o}};function N_(){return Oo(rn.workerData)}i(N_,"isIndexWorker");function M_(){let t=rn.parentPort;if(!t)throw new Error("This must be run a worker thread.");if(!Oo(rn.workerData))throw new Error("Worker data must provide a valid database path.");let e=rn.workerData.cwd;process.cwd=()=>e,new Hi(t,rn.workerData.indexWorkspaceRoots)}i(M_,"runIndexWorker");0&&(module.exports={IndexWorker,isIndexWorker,runIndexWorker});
//# sourceMappingURL=indexWorker.js.map
