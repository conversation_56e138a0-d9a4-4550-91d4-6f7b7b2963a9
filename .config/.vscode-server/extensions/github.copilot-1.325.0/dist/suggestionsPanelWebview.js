"use strict";(()=>{var Ur=Object.defineProperty;var i=(r,t)=>Ur(r,"name",{value:t,configurable:!0});var A=function(){if(typeof globalThis<"u")return globalThis;if(typeof global<"u")return global;if(typeof self<"u")return self;if(typeof window<"u")return window;try{return new Function("return this")()}catch{return{}}}();A.trustedTypes===void 0&&(A.trustedTypes={createPolicy:i((r,t)=>t,"createPolicy")});var Ee={configurable:!1,enumerable:!1,writable:!1};A.FAST===void 0&&Reflect.defineProperty(A,"FAST",Object.assign({value:Object.create(null)},Ee));var V=A.FAST;if(V.getById===void 0){let r=Object.create(null);Reflect.defineProperty(V,"getById",Object.assign({value(t,e){let n=r[t];return n===void 0&&(n=e?r[t]=e():null),n}},Ee))}var F=Object.freeze([]);function bt(){let r=new WeakMap;return function(t){let e=r.get(t);if(e===void 0){let n=Reflect.getPrototypeOf(t);for(;e===void 0&&n!==null;)e=r.get(n),n=Reflect.getPrototypeOf(n);e=e===void 0?[]:e.slice(0),r.set(t,e)}return e}}i(bt,"createMetadataLocator");var Nt=A.FAST.getById(1,()=>{let r=[],t=[];function e(){if(t.length)throw t.shift()}i(e,"throwFirstError");function n(a){try{a.call()}catch(c){t.push(c),setTimeout(e,0)}}i(n,"tryRunTask");function o(){let c=0;for(;c<r.length;)if(n(r[c]),c++,c>1024){for(let u=0,l=r.length-c;u<l;u++)r[u]=r[u+c];r.length-=c,c=0}r.length=0}i(o,"process");function s(a){r.length<1&&A.requestAnimationFrame(o),r.push(a)}return i(s,"enqueue"),Object.freeze({enqueue:s,process:o})}),De=A.trustedTypes.createPolicy("fast-html",{createHTML:i(r=>r,"createHTML")}),Vt=De,nt=`fast-${Math.random().toString(36).substring(2,8)}`,Ht=`${nt}{`,yt=`}${nt}`,f=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(r){if(Vt!==De)throw new Error("The HTML policy can only be set once.");Vt=r},createHTML(r){return Vt.createHTML(r)},isMarker(r){return r&&r.nodeType===8&&r.data.startsWith(nt)},extractDirectiveIndexFromMarker(r){return parseInt(r.data.replace(`${nt}:`,""))},createInterpolationPlaceholder(r){return`${Ht}${r}${yt}`},createCustomAttributePlaceholder(r,t){return`${r}="${this.createInterpolationPlaceholder(t)}"`},createBlockPlaceholder(r){return`<!--${nt}:${r}-->`},queueUpdate:Nt.enqueue,processUpdates:Nt.process,nextUpdate(){return new Promise(Nt.enqueue)},setAttribute(r,t,e){e==null?r.removeAttribute(t):r.setAttribute(t,e)},setBooleanAttribute(r,t,e){e?r.setAttribute(t,""):r.removeAttribute(t)},removeChildNodes(r){for(let t=r.firstChild;t!==null;t=r.firstChild)r.removeChild(t)},createTemplateWalker(r){return document.createTreeWalker(r,133,null,!1)}});var X=class{static{i(this,"SubscriberSet")}constructor(t,e){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=t,this.sub1=e}has(t){return this.spillover===void 0?this.sub1===t||this.sub2===t:this.spillover.indexOf(t)!==-1}subscribe(t){let e=this.spillover;if(e===void 0){if(this.has(t))return;if(this.sub1===void 0){this.sub1=t;return}if(this.sub2===void 0){this.sub2=t;return}this.spillover=[this.sub1,this.sub2,t],this.sub1=void 0,this.sub2=void 0}else e.indexOf(t)===-1&&e.push(t)}unsubscribe(t){let e=this.spillover;if(e===void 0)this.sub1===t?this.sub1=void 0:this.sub2===t&&(this.sub2=void 0);else{let n=e.indexOf(t);n!==-1&&e.splice(n,1)}}notify(t){let e=this.spillover,n=this.source;if(e===void 0){let o=this.sub1,s=this.sub2;o!==void 0&&o.handleChange(n,t),s!==void 0&&s.handleChange(n,t)}else for(let o=0,s=e.length;o<s;++o)e[o].handleChange(n,t)}},Y=class{static{i(this,"PropertyChangeNotifier")}constructor(t){this.subscribers={},this.sourceSubscribers=null,this.source=t}notify(t){var e;let n=this.subscribers[t];n!==void 0&&n.notify(t),(e=this.sourceSubscribers)===null||e===void 0||e.notify(t)}subscribe(t,e){var n;if(e){let o=this.subscribers[e];o===void 0&&(this.subscribers[e]=o=new X(this.source)),o.subscribe(t)}else this.sourceSubscribers=(n=this.sourceSubscribers)!==null&&n!==void 0?n:new X(this.source),this.sourceSubscribers.subscribe(t)}unsubscribe(t,e){var n;if(e){let o=this.subscribers[e];o!==void 0&&o.unsubscribe(t)}else(n=this.sourceSubscribers)===null||n===void 0||n.unsubscribe(t)}};var y=V.getById(2,()=>{let r=/(:|&&|\|\||if)/,t=new WeakMap,e=f.queueUpdate,n,o=i(l=>{throw new Error("Must call enableArrayObservation before observing arrays.")},"createArrayObserver");function s(l){let h=l.$fastController||t.get(l);return h===void 0&&(Array.isArray(l)?h=o(l):t.set(l,h=new Y(l))),h}i(s,"getNotifier");let a=bt();class c{static{i(this,"DefaultObservableAccessor")}constructor(h){this.name=h,this.field=`_${h}`,this.callback=`${h}Changed`}getValue(h){return n!==void 0&&n.watch(h,this.name),h[this.field]}setValue(h,g){let w=this.field,E=h[w];if(E!==g){h[w]=g;let S=h[this.callback];typeof S=="function"&&S.call(h,E,g),s(h).notify(this.name)}}}class u extends X{static{i(this,"BindingObserverImplementation")}constructor(h,g,w=!1){super(h,g),this.binding=h,this.isVolatileBinding=w,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(h,g){this.needsRefresh&&this.last!==null&&this.disconnect();let w=n;n=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;let E=this.binding(h,g);return n=w,E}disconnect(){if(this.last!==null){let h=this.first;for(;h!==void 0;)h.notifier.unsubscribe(this,h.propertyName),h=h.next;this.last=null,this.needsRefresh=this.needsQueue=!0}}watch(h,g){let w=this.last,E=s(h),S=w===null?this.first:{};if(S.propertySource=h,S.propertyName=g,S.notifier=E,E.subscribe(this,g),w!==null){if(!this.needsRefresh){let T;n=void 0,T=w.propertySource[w.propertyName],n=this,h===T&&(this.needsRefresh=!0)}w.next=S}this.last=S}handleChange(){this.needsQueue&&(this.needsQueue=!1,e(this))}call(){this.last!==null&&(this.needsQueue=!0,this.notify(this))}records(){let h=this.first;return{next:i(()=>{let g=h;return g===void 0?{value:void 0,done:!0}:(h=h.next,{value:g,done:!1})},"next"),[Symbol.iterator]:function(){return this}}}}return Object.freeze({setArrayObserverFactory(l){o=l},getNotifier:s,track(l,h){n!==void 0&&n.watch(l,h)},trackVolatile(){n!==void 0&&(n.needsRefresh=!0)},notify(l,h){s(l).notify(h)},defineProperty(l,h){typeof h=="string"&&(h=new c(h)),a(l).push(h),Reflect.defineProperty(l,h.name,{enumerable:!0,get:i(function(){return h.getValue(this)},"get"),set:i(function(g){h.setValue(this,g)},"set")})},getAccessors:a,binding(l,h,g=this.isVolatileBinding(l)){return new u(l,h,g)},isVolatileBinding(l){return r.test(l.toString())}})});function O(r,t){y.defineProperty(r,t)}i(O,"observable");var _e=V.getById(3,()=>{let r=null;return{get(){return r},set(t){r=t}}}),I=class{static{i(this,"ExecutionContext")}constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return _e.get()}get isEven(){return this.index%2===0}get isOdd(){return this.index%2!==0}get isFirst(){return this.index===0}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}static setEvent(t){_e.set(t)}};y.defineProperty(I.prototype,"index");y.defineProperty(I.prototype,"length");var M=Object.seal(new I);var Z=class{static{i(this,"HTMLDirective")}constructor(){this.targetIndex=0}},K=class extends Z{static{i(this,"TargetedHTMLDirective")}constructor(){super(...arguments),this.createPlaceholder=f.createInterpolationPlaceholder}},tt=class extends Z{static{i(this,"AttachedBehaviorHTMLDirective")}constructor(t,e,n){super(),this.name=t,this.behavior=e,this.options=n}createPlaceholder(t){return f.createCustomAttributePlaceholder(this.name,t)}createBehavior(t){return new this.behavior(t,this.options)}};function Wr(r,t){this.source=r,this.context=t,this.bindingObserver===null&&(this.bindingObserver=y.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(r,t))}i(Wr,"normalBind");function Gr(r,t){this.source=r,this.context=t,this.target.addEventListener(this.targetName,this)}i(Gr,"triggerBind");function Qr(){this.bindingObserver.disconnect(),this.source=null,this.context=null}i(Qr,"normalUnbind");function Jr(){this.bindingObserver.disconnect(),this.source=null,this.context=null;let r=this.target.$fastView;r!==void 0&&r.isComposed&&(r.unbind(),r.needsBindOnly=!0)}i(Jr,"contentUnbind");function Xr(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}i(Xr,"triggerUnbind");function Yr(r){f.setAttribute(this.target,this.targetName,r)}i(Yr,"updateAttributeTarget");function Zr(r){f.setBooleanAttribute(this.target,this.targetName,r)}i(Zr,"updateBooleanAttributeTarget");function Kr(r){if(r==null&&(r=""),r.create){this.target.textContent="";let t=this.target.$fastView;t===void 0?t=r.create():this.target.$fastTemplate!==r&&(t.isComposed&&(t.remove(),t.unbind()),t=r.create()),t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.source,this.context)):(t.isComposed=!0,t.bind(this.source,this.context),t.insertBefore(this.target),this.target.$fastView=t,this.target.$fastTemplate=r)}else{let t=this.target.$fastView;t!==void 0&&t.isComposed&&(t.isComposed=!1,t.remove(),t.needsBindOnly?t.needsBindOnly=!1:t.unbind()),this.target.textContent=r}}i(Kr,"updateContentTarget");function tn(r){this.target[this.targetName]=r}i(tn,"updatePropertyTarget");function en(r){let t=this.classVersions||Object.create(null),e=this.target,n=this.version||0;if(r!=null&&r.length){let o=r.split(/\s+/);for(let s=0,a=o.length;s<a;++s){let c=o[s];c!==""&&(t[c]=n,e.classList.add(c))}}if(this.classVersions=t,this.version=n+1,n!==0){n-=1;for(let o in t)t[o]===n&&e.classList.remove(o)}}i(en,"updateClassTarget");var H=class extends K{static{i(this,"HTMLBindingDirective")}constructor(t){super(),this.binding=t,this.bind=Wr,this.unbind=Qr,this.updateTarget=Yr,this.isBindingVolatile=y.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(t){if(this.originalTargetName=t,t!==void 0)switch(t[0]){case":":if(this.cleanedTargetName=t.substr(1),this.updateTarget=tn,this.cleanedTargetName==="innerHTML"){let e=this.binding;this.binding=(n,o)=>f.createHTML(e(n,o))}break;case"?":this.cleanedTargetName=t.substr(1),this.updateTarget=Zr;break;case"@":this.cleanedTargetName=t.substr(1),this.bind=Gr,this.unbind=Xr;break;default:this.cleanedTargetName=t,t==="class"&&(this.updateTarget=en);break}}targetAtContent(){this.updateTarget=Kr,this.unbind=Jr}createBehavior(t){return new qt(t,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}},qt=class{static{i(this,"BindingBehavior")}constructor(t,e,n,o,s,a,c){this.source=null,this.context=null,this.bindingObserver=null,this.target=t,this.binding=e,this.isBindingVolatile=n,this.bind=o,this.unbind=s,this.updateTarget=a,this.targetName=c}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(t){I.setEvent(t);let e=this.binding(this.source,this.context);I.setEvent(null),e!==!0&&t.preventDefault()}};var zt=null,Ut=class r{static{i(this,"CompilationContext")}addFactory(t){t.targetIndex=this.targetIndex,this.behaviorFactories.push(t)}captureContentBinding(t){t.targetAtContent(),this.addFactory(t)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){zt=this}static borrow(t){let e=zt||new r;return e.directives=t,e.reset(),zt=null,e}};function rn(r){if(r.length===1)return r[0];let t,e=r.length,n=r.map(a=>typeof a=="string"?()=>a:(t=a.targetName||t,a.binding)),o=i((a,c)=>{let u="";for(let l=0;l<e;++l)u+=n[l](a,c);return u},"binding"),s=new H(o);return s.targetName=t,s}i(rn,"createAggregateBinding");var nn=yt.length;function Be(r,t){let e=t.split(Ht);if(e.length===1)return null;let n=[];for(let o=0,s=e.length;o<s;++o){let a=e[o],c=a.indexOf(yt),u;if(c===-1)u=a;else{let l=parseInt(a.substring(0,c));n.push(r.directives[l]),u=a.substring(c+nn)}u!==""&&n.push(u)}return n}i(Be,"parseContent");function Ae(r,t,e=!1){let n=t.attributes;for(let o=0,s=n.length;o<s;++o){let a=n[o],c=a.value,u=Be(r,c),l=null;u===null?e&&(l=new H(()=>c),l.targetName=a.name):l=rn(u),l!==null&&(t.removeAttributeNode(a),o--,s--,r.addFactory(l))}}i(Ae,"compileAttributes");function on(r,t,e){let n=Be(r,t.textContent);if(n!==null){let o=t;for(let s=0,a=n.length;s<a;++s){let c=n[s],u=s===0?t:o.parentNode.insertBefore(document.createTextNode(""),o.nextSibling);typeof c=="string"?u.textContent=c:(u.textContent=" ",r.captureContentBinding(c)),o=u,r.targetIndex++,u!==t&&e.nextNode()}r.targetIndex--}}i(on,"compileContent");function Fe(r,t){let e=r.content;document.adoptNode(e);let n=Ut.borrow(t);Ae(n,r,!0);let o=n.behaviorFactories;n.reset();let s=f.createTemplateWalker(e),a;for(;a=s.nextNode();)switch(n.targetIndex++,a.nodeType){case 1:Ae(n,a);break;case 3:on(n,a,s);break;case 8:f.isMarker(a)&&n.addFactory(t[f.extractDirectiveIndexFromMarker(a)])}let c=0;(f.isMarker(e.firstChild)||e.childNodes.length===1&&t.length)&&(e.insertBefore(document.createComment(""),e.firstChild),c=-1);let u=n.behaviorFactories;return n.release(),{fragment:e,viewBehaviorFactories:u,hostBehaviorFactories:o,targetOffset:c}}i(Fe,"compileTemplate");var Wt=document.createRange(),vt=class{static{i(this,"HTMLView")}constructor(t,e){this.fragment=t,this.behaviors=e,this.source=null,this.context=null,this.firstChild=t.firstChild,this.lastChild=t.lastChild}appendTo(t){t.appendChild(this.fragment)}insertBefore(t){if(this.fragment.hasChildNodes())t.parentNode.insertBefore(this.fragment,t);else{let e=this.lastChild;if(t.previousSibling===e)return;let n=t.parentNode,o=this.firstChild,s;for(;o!==e;)s=o.nextSibling,n.insertBefore(o,t),o=s;n.insertBefore(e,t)}}remove(){let t=this.fragment,e=this.lastChild,n=this.firstChild,o;for(;n!==e;)o=n.nextSibling,t.appendChild(n),n=o;t.appendChild(e)}dispose(){let t=this.firstChild.parentNode,e=this.lastChild,n=this.firstChild,o;for(;n!==e;)o=n.nextSibling,t.removeChild(n),n=o;t.removeChild(e);let s=this.behaviors,a=this.source;for(let c=0,u=s.length;c<u;++c)s[c].unbind(a)}bind(t,e){let n=this.behaviors;if(this.source!==t)if(this.source!==null){let o=this.source;this.source=t,this.context=e;for(let s=0,a=n.length;s<a;++s){let c=n[s];c.unbind(o),c.bind(t,e)}}else{this.source=t,this.context=e;for(let o=0,s=n.length;o<s;++o)n[o].bind(t,e)}}unbind(){if(this.source===null)return;let t=this.behaviors,e=this.source;for(let n=0,o=t.length;n<o;++n)t[n].unbind(e);this.source=null}static disposeContiguousBatch(t){if(t.length!==0){Wt.setStartBefore(t[0].firstChild),Wt.setEndAfter(t[t.length-1].lastChild),Wt.deleteContents();for(let e=0,n=t.length;e<n;++e){let o=t[e],s=o.behaviors,a=o.source;for(let c=0,u=s.length;c<u;++c)s[c].unbind(a)}}}};var xt=class{static{i(this,"ViewTemplate")}constructor(t,e){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=t,this.directives=e}create(t){if(this.fragment===null){let l,h=this.html;if(typeof h=="string"){l=document.createElement("template"),l.innerHTML=f.createHTML(h);let w=l.content.firstElementChild;w!==null&&w.tagName==="TEMPLATE"&&(l=w)}else l=h;let g=Fe(l,this.directives);this.fragment=g.fragment,this.viewBehaviorFactories=g.viewBehaviorFactories,this.hostBehaviorFactories=g.hostBehaviorFactories,this.targetOffset=g.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}let e=this.fragment.cloneNode(!0),n=this.viewBehaviorFactories,o=new Array(this.behaviorCount),s=f.createTemplateWalker(e),a=0,c=this.targetOffset,u=s.nextNode();for(let l=n.length;a<l;++a){let h=n[a],g=h.targetIndex;for(;u!==null;)if(c===g){o[a]=h.createBehavior(u);break}else u=s.nextNode(),c++}if(this.hasHostBehaviors){let l=this.hostBehaviorFactories;for(let h=0,g=l.length;h<g;++h,++a)o[a]=l[h].createBehavior(t)}return new vt(e,o)}render(t,e,n){typeof e=="string"&&(e=document.getElementById(e)),n===void 0&&(n=e);let o=this.create(n);return o.bind(t,M),o.appendTo(e),o}},sn=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function q(r,...t){let e=[],n="";for(let o=0,s=r.length-1;o<s;++o){let a=r[o],c=t[o];if(n+=a,c instanceof xt){let u=c;c=i(()=>u,"value")}if(typeof c=="function"&&(c=new H(c)),c instanceof K){let u=sn.exec(a);u!==null&&(c.targetName=u[2])}c instanceof Z?(n+=c.createPlaceholder(e.length),e.push(c)):n+=c}return n+=r[r.length-1],new xt(n,e)}i(q,"html");var x=class{static{i(this,"ElementStyles")}constructor(){this.targets=new WeakSet}addStylesTo(t){this.targets.add(t)}removeStylesFrom(t){this.targets.delete(t)}isAttachedTo(t){return this.targets.has(t)}withBehaviors(...t){return this.behaviors=this.behaviors===null?t:this.behaviors.concat(t),this}};x.create=(()=>{if(f.supportsAdoptedStyleSheets){let r=new Map;return t=>new Gt(t,r)}return r=>new Qt(r)})();function Jt(r){return r.map(t=>t instanceof x?Jt(t.styles):[t]).reduce((t,e)=>t.concat(e),[])}i(Jt,"reduceStyles");function Ie(r){return r.map(t=>t instanceof x?t.behaviors:null).reduce((t,e)=>e===null?t:(t===null&&(t=[]),t.concat(e)),null)}i(Ie,"reduceBehaviors");var wt=Symbol("prependToAdoptedStyleSheets");function Me(r){let t=[],e=[];return r.forEach(n=>(n[wt]?t:e).push(n)),{prepend:t,append:e}}i(Me,"separateSheetsToPrepend");var Le=i((r,t)=>{let{prepend:e,append:n}=Me(t);r.adoptedStyleSheets=[...e,...r.adoptedStyleSheets,...n]},"addAdoptedStyleSheets"),je=i((r,t)=>{r.adoptedStyleSheets=r.adoptedStyleSheets.filter(e=>t.indexOf(e)===-1)},"removeAdoptedStyleSheets");if(f.supportsAdoptedStyleSheets)try{document.adoptedStyleSheets.push(),document.adoptedStyleSheets.splice(),Le=i((r,t)=>{let{prepend:e,append:n}=Me(t);r.adoptedStyleSheets.splice(0,0,...e),r.adoptedStyleSheets.push(...n)},"addAdoptedStyleSheets"),je=i((r,t)=>{for(let e of t){let n=r.adoptedStyleSheets.indexOf(e);n!==-1&&r.adoptedStyleSheets.splice(n,1)}},"removeAdoptedStyleSheets")}catch{}var Gt=class extends x{static{i(this,"AdoptedStyleSheetsStyles")}constructor(t,e){super(),this.styles=t,this.styleSheetCache=e,this._styleSheets=void 0,this.behaviors=Ie(t)}get styleSheets(){if(this._styleSheets===void 0){let t=this.styles,e=this.styleSheetCache;this._styleSheets=Jt(t).map(n=>{if(n instanceof CSSStyleSheet)return n;let o=e.get(n);return o===void 0&&(o=new CSSStyleSheet,o.replaceSync(n),e.set(n,o)),o})}return this._styleSheets}addStylesTo(t){Le(t,this.styleSheets),super.addStylesTo(t)}removeStylesFrom(t){je(t,this.styleSheets),super.removeStylesFrom(t)}},an=0;function cn(){return`fast-style-class-${++an}`}i(cn,"getNextStyleClass");var Qt=class extends x{static{i(this,"StyleElementStyles")}constructor(t){super(),this.styles=t,this.behaviors=null,this.behaviors=Ie(t),this.styleSheets=Jt(t),this.styleClass=cn()}addStylesTo(t){let e=this.styleSheets,n=this.styleClass;t=this.normalizeTarget(t);for(let o=0;o<e.length;o++){let s=document.createElement("style");s.innerHTML=e[o],s.className=n,t.append(s)}super.addStylesTo(t)}removeStylesFrom(t){t=this.normalizeTarget(t);let e=t.querySelectorAll(`.${this.styleClass}`);for(let n=0,o=e.length;n<o;++n)t.removeChild(e[n]);super.removeStylesFrom(t)}isAttachedTo(t){return super.isAttachedTo(this.normalizeTarget(t))}normalizeTarget(t){return t===document?document.body:t}};var ot=Object.freeze({locate:bt()}),Ne={toView(r){return r?"true":"false"},fromView(r){return!(r==null||r==="false"||r===!1||r===0)}};var St=class r{static{i(this,"AttributeDefinition")}constructor(t,e,n=e.toLowerCase(),o="reflect",s){this.guards=new Set,this.Owner=t,this.name=e,this.attribute=n,this.mode=o,this.converter=s,this.fieldName=`_${e}`,this.callbackName=`${e}Changed`,this.hasCallback=this.callbackName in t.prototype,o==="boolean"&&s===void 0&&(this.converter=Ne)}setValue(t,e){let n=t[this.fieldName],o=this.converter;o!==void 0&&(e=o.fromView(e)),n!==e&&(t[this.fieldName]=e,this.tryReflectToAttribute(t),this.hasCallback&&t[this.callbackName](n,e),t.$fastController.notify(this.name))}getValue(t){return y.track(t,this.name),t[this.fieldName]}onAttributeChangedCallback(t,e){this.guards.has(t)||(this.guards.add(t),this.setValue(t,e),this.guards.delete(t))}tryReflectToAttribute(t){let e=this.mode,n=this.guards;n.has(t)||e==="fromView"||f.queueUpdate(()=>{n.add(t);let o=t[this.fieldName];switch(e){case"reflect":let s=this.converter;f.setAttribute(t,this.attribute,s!==void 0?s.toView(o):o);break;case"boolean":f.setBooleanAttribute(t,this.attribute,o);break}n.delete(t)})}static collect(t,...e){let n=[];e.push(ot.locate(t));for(let o=0,s=e.length;o<s;++o){let a=e[o];if(a!==void 0)for(let c=0,u=a.length;c<u;++c){let l=a[c];typeof l=="string"?n.push(new r(t,l)):n.push(new r(t,l.property,l.attribute,l.mode,l.converter))}}return n}};function p(r,t){let e;function n(o,s){arguments.length>1&&(e.property=s),ot.locate(o.constructor).push(e)}if(i(n,"decorator"),arguments.length>1){e={},n(r,t);return}return e=r===void 0?{}:r,n}i(p,"attr");var Ve={mode:"open"},He={},Xt=V.getById(4,()=>{let r=new Map;return Object.freeze({register(t){return r.has(t.type)?!1:(r.set(t.type,t),!0)},getByType(t){return r.get(t)}})}),D=class{static{i(this,"FASTElementDefinition")}constructor(t,e=t.definition){typeof e=="string"&&(e={name:e}),this.type=t,this.name=e.name,this.template=e.template;let n=St.collect(t,e.attributes),o=new Array(n.length),s={},a={};for(let c=0,u=n.length;c<u;++c){let l=n[c];o[c]=l.attribute,s[l.name]=l,a[l.attribute]=l}this.attributes=n,this.observedAttributes=o,this.propertyLookup=s,this.attributeLookup=a,this.shadowOptions=e.shadowOptions===void 0?Ve:e.shadowOptions===null?void 0:Object.assign(Object.assign({},Ve),e.shadowOptions),this.elementOptions=e.elementOptions===void 0?He:Object.assign(Object.assign({},He),e.elementOptions),this.styles=e.styles===void 0?void 0:Array.isArray(e.styles)?x.create(e.styles):e.styles instanceof x?e.styles:x.create([e.styles])}get isDefined(){return!!Xt.getByType(this.type)}define(t=customElements){let e=this.type;if(Xt.register(this)){let n=this.attributes,o=e.prototype;for(let s=0,a=n.length;s<a;++s)y.defineProperty(o,n[s]);Reflect.defineProperty(e,"observedAttributes",{value:this.observedAttributes,enumerable:!0})}return t.get(this.name)||t.define(this.name,e,this.elementOptions),this}};D.forType=Xt.getByType;var qe=new WeakMap,ln={bubbles:!0,composed:!0,cancelable:!0};function Yt(r){return r.shadowRoot||qe.get(r)||null}i(Yt,"getShadowRoot");var Ct=class r extends Y{static{i(this,"Controller")}constructor(t,e){super(t),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this._isConnected=!1,this.$fastController=this,this.view=null,this.element=t,this.definition=e;let n=e.shadowOptions;if(n!==void 0){let s=t.attachShadow(n);n.mode==="closed"&&qe.set(t,s)}let o=y.getAccessors(t);if(o.length>0){let s=this.boundObservables=Object.create(null);for(let a=0,c=o.length;a<c;++a){let u=o[a].name,l=t[u];l!==void 0&&(delete t[u],s[u]=l)}}}get isConnected(){return y.track(this,"isConnected"),this._isConnected}setIsConnected(t){this._isConnected=t,y.notify(this,"isConnected")}get template(){return this._template}set template(t){this._template!==t&&(this._template=t,this.needsInitialization||this.renderTemplate(t))}get styles(){return this._styles}set styles(t){this._styles!==t&&(this._styles!==null&&this.removeStyles(this._styles),this._styles=t,!this.needsInitialization&&t!==null&&this.addStyles(t))}addStyles(t){let e=Yt(this.element)||this.element.getRootNode();if(t instanceof HTMLStyleElement)e.append(t);else if(!t.isAttachedTo(e)){let n=t.behaviors;t.addStylesTo(e),n!==null&&this.addBehaviors(n)}}removeStyles(t){let e=Yt(this.element)||this.element.getRootNode();if(t instanceof HTMLStyleElement)e.removeChild(t);else if(t.isAttachedTo(e)){let n=t.behaviors;t.removeStylesFrom(e),n!==null&&this.removeBehaviors(n)}}addBehaviors(t){let e=this.behaviors||(this.behaviors=new Map),n=t.length,o=[];for(let s=0;s<n;++s){let a=t[s];e.has(a)?e.set(a,e.get(a)+1):(e.set(a,1),o.push(a))}if(this._isConnected){let s=this.element;for(let a=0;a<o.length;++a)o[a].bind(s,M)}}removeBehaviors(t,e=!1){let n=this.behaviors;if(n===null)return;let o=t.length,s=[];for(let a=0;a<o;++a){let c=t[a];if(n.has(c)){let u=n.get(c)-1;u===0||e?n.delete(c)&&s.push(c):n.set(c,u)}}if(this._isConnected){let a=this.element;for(let c=0;c<s.length;++c)s[c].unbind(a)}}onConnectedCallback(){if(this._isConnected)return;let t=this.element;this.needsInitialization?this.finishInitialization():this.view!==null&&this.view.bind(t,M);let e=this.behaviors;if(e!==null)for(let[n]of e)n.bind(t,M);this.setIsConnected(!0)}onDisconnectedCallback(){if(!this._isConnected)return;this.setIsConnected(!1);let t=this.view;t!==null&&t.unbind();let e=this.behaviors;if(e!==null){let n=this.element;for(let[o]of e)o.unbind(n)}}onAttributeChangedCallback(t,e,n){let o=this.definition.attributeLookup[t];o!==void 0&&o.onAttributeChangedCallback(this.element,n)}emit(t,e,n){return this._isConnected?this.element.dispatchEvent(new CustomEvent(t,Object.assign(Object.assign({detail:e},ln),n))):!1}finishInitialization(){let t=this.element,e=this.boundObservables;if(e!==null){let o=Object.keys(e);for(let s=0,a=o.length;s<a;++s){let c=o[s];t[c]=e[c]}this.boundObservables=null}let n=this.definition;this._template===null&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():n.template&&(this._template=n.template||null)),this._template!==null&&this.renderTemplate(this._template),this._styles===null&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():n.styles&&(this._styles=n.styles||null)),this._styles!==null&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(t){let e=this.element,n=Yt(e)||e;this.view!==null?(this.view.dispose(),this.view=null):this.needsInitialization||f.removeChildNodes(n),t&&(this.view=t.render(e,n,e))}static forCustomElement(t){let e=t.$fastController;if(e!==void 0)return e;let n=D.forType(t.constructor);if(n===void 0)throw new Error("Missing FASTElement definition.");return t.$fastController=new r(t,n)}};function ze(r){return class extends r{constructor(){super(),Ct.forCustomElement(this)}$emit(t,e,n){return this.$fastController.emit(t,e,n)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(t,e,n){this.$fastController.onAttributeChangedCallback(t,e,n)}}}i(ze,"createFASTElement");var L=Object.assign(ze(HTMLElement),{from(r){return ze(r)},define(r,t){return new D(r,t).define().type}});var z=class{static{i(this,"CSSDirective")}createCSS(){return""}createBehavior(){}};function un(r,t){let e=[],n="",o=[];for(let s=0,a=r.length-1;s<a;++s){n+=r[s];let c=t[s];if(c instanceof z){let u=c.createBehavior();c=c.createCSS(),u&&o.push(u)}c instanceof x||c instanceof CSSStyleSheet?(n.trim()!==""&&(e.push(n),n=""),e.push(c)):n+=c}return n+=r[r.length-1],n.trim()!==""&&e.push(n),{styles:e,behaviors:o}}i(un,"collectStyles");function U(r,...t){let{styles:e,behaviors:n}=un(r,t),o=x.create(e);return n.length&&o.withBehaviors(...n),o}i(U,"css");var Zt=class{static{i(this,"RefBehavior")}constructor(t,e){this.target=t,this.propertyName=e}bind(t){t[this.propertyName]=this.target}unbind(){}};function $(r){return new tt("fast-ref",Zt,r)}i($,"ref");var kt=class{static{i(this,"NodeObservationBehavior")}constructor(t,e){this.target=t,this.options=e,this.source=null}bind(t){let e=this.options.property;this.shouldUpdate=y.getAccessors(t).some(n=>n.name===e),this.source=t,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(F),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let t=this.getNodes();return this.options.filter!==void 0&&(t=t.filter(this.options.filter)),t}updateTarget(t){this.source[this.options.property]=t}};var Kt=class extends kt{static{i(this,"SlottedBehavior")}constructor(t,e){super(t,e)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}};function Ue(r){return typeof r=="string"&&(r={property:r}),new tt("fast-slotted",Kt,r)}i(Ue,"slotted");var Pt=class{static{i(this,"StartEnd")}handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}},We=i((r,t)=>q`
    <span
        part="end"
        ${$("endContainer")}
        class=${e=>t.end?"end":void 0}
    >
        <slot name="end" ${$("end")} @slotchange="${e=>e.handleEndContentChange()}">
            ${t.end||""}
        </slot>
    </span>
`,"endSlotTemplate"),Ge=i((r,t)=>q`
    <span
        part="start"
        ${$("startContainer")}
        class="${e=>t.start?"start":void 0}"
    >
        <slot
            name="start"
            ${$("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        >
            ${t.start||""}
        </slot>
    </span>
`,"startSlotTemplate"),_i=q`
    <span part="end" ${$("endContainer")}>
        <slot
            name="end"
            ${$("end")}
            @slotchange="${r=>r.handleEndContentChange()}"
        ></slot>
    </span>
`,Ai=q`
    <span part="start" ${$("startContainer")}>
        <slot
            name="start"
            ${$("start")}
            @slotchange="${r=>r.handleStartContentChange()}"
        ></slot>
    </span>
`;function m(r,t,e,n){var o=arguments.length,s=o<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,e):n,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(r,t,e,n);else for(var c=r.length-1;c>=0;c--)(a=r[c])&&(s=(o<3?a(s):o>3?a(t,e,s):a(t,e))||s);return o>3&&s&&Object.defineProperty(t,e,s),s}i(m,"__decorate");var te=new Map;"metadata"in Reflect||(Reflect.metadata=function(r,t){return function(e){Reflect.defineMetadata(r,t,e)}},Reflect.defineMetadata=function(r,t,e){let n=te.get(e);n===void 0&&te.set(e,n=new Map),n.set(r,t)},Reflect.getOwnMetadata=function(r,t){let e=te.get(t);if(e!==void 0)return e.get(r)});var oe=class{static{i(this,"ResolverBuilder")}constructor(t,e){this.container=t,this.key=e}instance(t){return this.registerResolver(0,t)}singleton(t){return this.registerResolver(1,t)}transient(t){return this.registerResolver(2,t)}callback(t){return this.registerResolver(3,t)}cachedCallback(t){return this.registerResolver(3,or(t))}aliasTo(t){return this.registerResolver(5,t)}registerResolver(t,e){let{container:n,key:o}=this;return this.container=this.key=void 0,n.registerResolver(o,new C(o,t,e))}};function it(r){let t=r.slice(),e=Object.keys(r),n=e.length,o;for(let s=0;s<n;++s)o=e[s],ir(o)||(t[o]=r[o]);return t}i(it,"cloneArrayWithPossibleProps");var dn=Object.freeze({none(r){throw Error(`${r.toString()} not registered, did you forget to add @singleton()?`)},singleton(r){return new C(r,1,r)},transient(r){return new C(r,2,r)}}),ee=Object.freeze({default:Object.freeze({parentLocator:i(()=>null,"parentLocator"),responsibleForOwnerRequests:!1,defaultResolver:dn.singleton})}),Qe=new Map;function Je(r){return t=>Reflect.getOwnMetadata(r,t)}i(Je,"getParamTypes");var Xe=null,b=Object.freeze({createContainer(r){return new st(null,Object.assign({},ee.default,r))},findResponsibleContainer(r){let t=r.$$container$$;return t&&t.responsibleForOwnerRequests?t:b.findParentContainer(r)},findParentContainer(r){let t=new CustomEvent(nr,{bubbles:!0,composed:!0,cancelable:!0,detail:{container:void 0}});return r.dispatchEvent(t),t.detail.container||b.getOrCreateDOMContainer()},getOrCreateDOMContainer(r,t){return r?r.$$container$$||new st(r,Object.assign({},ee.default,t,{parentLocator:b.findParentContainer})):Xe||(Xe=new st(null,Object.assign({},ee.default,t,{parentLocator:i(()=>null,"parentLocator")})))},getDesignParamtypes:Je("design:paramtypes"),getAnnotationParamtypes:Je("di:paramtypes"),getOrCreateAnnotationParamTypes(r){let t=this.getAnnotationParamtypes(r);return t===void 0&&Reflect.defineMetadata("di:paramtypes",t=[],r),t},getDependencies(r){let t=Qe.get(r);if(t===void 0){let e=r.inject;if(e===void 0){let n=b.getDesignParamtypes(r),o=b.getAnnotationParamtypes(r);if(n===void 0)if(o===void 0){let s=Object.getPrototypeOf(r);typeof s=="function"&&s!==Function.prototype?t=it(b.getDependencies(s)):t=[]}else t=it(o);else if(o===void 0)t=it(n);else{t=it(n);let s=o.length,a;for(let l=0;l<s;++l)a=o[l],a!==void 0&&(t[l]=a);let c=Object.keys(o);s=c.length;let u;for(let l=0;l<s;++l)u=c[l],ir(u)||(t[u]=o[u])}}else t=it(e);Qe.set(r,t)}return t},defineProperty(r,t,e,n=!1){let o=`$di_${t}`;Reflect.defineProperty(r,t,{get:i(function(){let s=this[o];if(s===void 0&&(s=(this instanceof HTMLElement?b.findResponsibleContainer(this):b.getOrCreateDOMContainer()).get(e),this[o]=s,n&&this instanceof L)){let c=this.$fastController,u=i(()=>{let h=b.findResponsibleContainer(this).get(e),g=this[o];h!==g&&(this[o]=s,c.notify(t))},"handleChange");c.subscribe({handleChange:u},"isConnected")}return s},"get")})},createInterface(r,t){let e=typeof r=="function"?r:t,n=typeof r=="string"?r:r&&"friendlyName"in r&&r.friendlyName||tr,o=typeof r=="string"?!1:r&&"respectConnection"in r&&r.respectConnection||!1,s=i(function(a,c,u){if(a==null||new.target!==void 0)throw new Error(`No registration for interface: '${s.friendlyName}'`);if(c)b.defineProperty(a,c,s,o);else{let l=b.getOrCreateAnnotationParamTypes(a);l[u]=s}},"Interface");return s.$isInterface=!0,s.friendlyName=n??"(anonymous)",e!=null&&(s.register=function(a,c){return e(new oe(a,c??s))}),s.toString=i(function(){return`InterfaceSymbol<${s.friendlyName}>`},"toString"),s},inject(...r){return function(t,e,n){if(typeof n=="number"){let o=b.getOrCreateAnnotationParamTypes(t),s=r[0];s!==void 0&&(o[n]=s)}else if(e)b.defineProperty(t,e,r[0]);else{let o=n?b.getOrCreateAnnotationParamTypes(n.value):b.getOrCreateAnnotationParamTypes(t),s;for(let a=0;a<r.length;++a)s=r[a],s!==void 0&&(o[a]=s)}}},transient(r){return r.register=i(function(e){return W.transient(r,r).register(e)},"register"),r.registerInRequestor=!1,r},singleton(r,t=fn){return r.register=i(function(n){return W.singleton(r,r).register(n)},"register"),r.registerInRequestor=t.scoped,r}}),hn=b.createInterface("Container");function Rt(r){return function(t){let e=i(function(n,o,s){b.inject(e)(n,o,s)},"resolver");return e.$isResolver=!0,e.resolve=function(n,o){return r(t,n,o)},e}}i(Rt,"createResolver");var ji=b.inject;var fn={scoped:!1};function pn(r){return function(t,e){e=!!e;let n=i(function(o,s,a){b.inject(n)(o,s,a)},"resolver");return n.$isResolver=!0,n.resolve=function(o,s){return r(t,o,s,e)},n}}i(pn,"createAllResolver");var Ni=pn((r,t,e,n)=>e.getAll(r,n)),Vi=Rt((r,t,e)=>()=>e.get(r)),Hi=Rt((r,t,e)=>{if(e.has(r,!0))return e.get(r)});function se(r,t,e){b.inject(se)(r,t,e)}i(se,"ignore");se.$isResolver=!0;se.resolve=()=>{};var qi=Rt((r,t,e)=>{let n=rr(r,t),o=new C(r,0,n);return e.registerResolver(r,o),n}),zi=Rt((r,t,e)=>rr(r,t));function rr(r,t){return t.getFactory(r).construct(t)}i(rr,"createNewInstance");var C=class{static{i(this,"ResolverImpl")}constructor(t,e,n){this.key=t,this.strategy=e,this.state=n,this.resolving=!1}get $isResolver(){return!0}register(t){return t.registerResolver(this.key,this)}resolve(t,e){switch(this.strategy){case 0:return this.state;case 1:{if(this.resolving)throw new Error(`Cyclic dependency found: ${this.state.name}`);return this.resolving=!0,this.state=t.getFactory(this.state).construct(e),this.strategy=0,this.resolving=!1,this.state}case 2:{let n=t.getFactory(this.state);if(n===null)throw new Error(`Resolver for ${String(this.key)} returned a null factory`);return n.construct(e)}case 3:return this.state(t,e,this);case 4:return this.state[0].resolve(t,e);case 5:return e.get(this.state);default:throw new Error(`Invalid resolver strategy specified: ${this.strategy}.`)}}getFactory(t){var e,n,o;switch(this.strategy){case 1:case 2:return t.getFactory(this.state);case 5:return(o=(n=(e=t.getResolver(this.state))===null||e===void 0?void 0:e.getFactory)===null||n===void 0?void 0:n.call(e,t))!==null&&o!==void 0?o:null;default:return null}}};function Ye(r){return this.get(r)}i(Ye,"containerGetKey");function mn(r,t){return t(r)}i(mn,"transformInstance");var ie=class{static{i(this,"FactoryImpl")}constructor(t,e){this.Type=t,this.dependencies=e,this.transformers=null}construct(t,e){let n;return e===void 0?n=new this.Type(...this.dependencies.map(Ye,t)):n=new this.Type(...this.dependencies.map(Ye,t),...e),this.transformers==null?n:this.transformers.reduce(mn,n)}registerTransformer(t){(this.transformers||(this.transformers=[])).push(t)}},gn={$isResolver:!0,resolve(r,t){return t}};function $t(r){return typeof r.register=="function"}i($t,"isRegistry");function bn(r){return $t(r)&&typeof r.registerInRequestor=="boolean"}i(bn,"isSelfRegistry");function Ze(r){return bn(r)&&r.registerInRequestor}i(Ze,"isRegisterInRequester");function yn(r){return r.prototype!==void 0}i(yn,"isClass");var vn=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),nr="__DI_LOCATE_PARENT__",re=new Map,st=class r{static{i(this,"ContainerImpl")}constructor(t,e){this.owner=t,this.config=e,this._parent=void 0,this.registerDepth=0,this.context=null,t!==null&&(t.$$container$$=this),this.resolvers=new Map,this.resolvers.set(hn,gn),t instanceof Node&&t.addEventListener(nr,n=>{n.composedPath()[0]!==this.owner&&(n.detail.container=this,n.stopImmediatePropagation())})}get parent(){return this._parent===void 0&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return this.parent===null?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}registerWithContext(t,...e){return this.context=t,this.register(...e),this.context=null,this}register(...t){if(++this.registerDepth===100)throw new Error("Unable to autoregister dependency");let e,n,o,s,a,c=this.context;for(let u=0,l=t.length;u<l;++u)if(e=t[u],!!er(e))if($t(e))e.register(this,c);else if(yn(e))W.singleton(e,e).register(this);else for(n=Object.keys(e),s=0,a=n.length;s<a;++s)o=e[n[s]],er(o)&&($t(o)?o.register(this,c):this.register(o));return--this.registerDepth,this}registerResolver(t,e){Tt(t);let n=this.resolvers,o=n.get(t);return o==null?n.set(t,e):o instanceof C&&o.strategy===4?o.state.push(e):n.set(t,new C(t,4,[o,e])),e}registerTransformer(t,e){let n=this.getResolver(t);if(n==null)return!1;if(n.getFactory){let o=n.getFactory(this);return o==null?!1:(o.registerTransformer(e),!0)}return!1}getResolver(t,e=!0){if(Tt(t),t.resolve!==void 0)return t;let n=this,o;for(;n!=null;)if(o=n.resolvers.get(t),o==null){if(n.parent==null){let s=Ze(t)?this:n;return e?this.jitRegister(t,s):null}n=n.parent}else return o;return null}has(t,e=!1){return this.resolvers.has(t)?!0:e&&this.parent!=null?this.parent.has(t,!0):!1}get(t){if(Tt(t),t.$isResolver)return t.resolve(this,this);let e=this,n;for(;e!=null;)if(n=e.resolvers.get(t),n==null){if(e.parent==null){let o=Ze(t)?this:e;return n=this.jitRegister(t,o),n.resolve(e,this)}e=e.parent}else return n.resolve(e,this);throw new Error(`Unable to resolve key: ${String(t)}`)}getAll(t,e=!1){Tt(t);let n=this,o=n,s;if(e){let a=F;for(;o!=null;)s=o.resolvers.get(t),s!=null&&(a=a.concat(Ke(s,o,n))),o=o.parent;return a}else for(;o!=null;)if(s=o.resolvers.get(t),s==null){if(o=o.parent,o==null)return F}else return Ke(s,o,n);return F}getFactory(t){let e=re.get(t);if(e===void 0){if(xn(t))throw new Error(`${t.name} is a native function and therefore cannot be safely constructed by DI. If this is intentional, please use a callback or cachedCallback resolver.`);re.set(t,e=new ie(t,b.getDependencies(t)))}return e}registerFactory(t,e){re.set(t,e)}createChild(t){return new r(null,Object.assign({},this.config,t,{parentLocator:i(()=>this,"parentLocator")}))}jitRegister(t,e){if(typeof t!="function")throw new Error(`Attempted to jitRegister something that is not a constructor: '${t}'. Did you forget to register this dependency?`);if(vn.has(t.name))throw new Error(`Attempted to jitRegister an intrinsic type: ${t.name}. Did you forget to add @inject(Key)`);if($t(t)){let n=t.register(e);if(!(n instanceof Object)||n.resolve==null){let o=e.resolvers.get(t);if(o!=null)return o;throw new Error("A valid resolver was not returned from the static register method")}return n}else{if(t.$isInterface)throw new Error(`Attempted to jitRegister an interface: ${t.friendlyName}`);{let n=this.config.defaultResolver(t,e);return e.resolvers.set(t,n),n}}}},ne=new WeakMap;function or(r){return function(t,e,n){if(ne.has(n))return ne.get(n);let o=r(t,e,n);return ne.set(n,o),o}}i(or,"cacheCallbackResult");var W=Object.freeze({instance(r,t){return new C(r,0,t)},singleton(r,t){return new C(r,1,t)},transient(r,t){return new C(r,2,t)},callback(r,t){return new C(r,3,t)},cachedCallback(r,t){return new C(r,3,or(t))},aliasTo(r,t){return new C(t,5,r)}});function Tt(r){if(r==null)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}i(Tt,"validateKey");function Ke(r,t,e){if(r instanceof C&&r.strategy===4){let n=r.state,o=n.length,s=new Array(o);for(;o--;)s[o]=n[o].resolve(t,e);return s}return[r.resolve(t,e)]}i(Ke,"buildAllResponse");var tr="(anonymous)";function er(r){return typeof r=="object"&&r!==null||typeof r=="function"}i(er,"isObject");var xn=function(){let r=new WeakMap,t=!1,e="",n=0;return function(o){return t=r.get(o),t===void 0&&(e=o.toString(),n=e.length,t=n>=29&&n<=100&&e.charCodeAt(n-1)===125&&e.charCodeAt(n-2)<=32&&e.charCodeAt(n-3)===93&&e.charCodeAt(n-4)===101&&e.charCodeAt(n-5)===100&&e.charCodeAt(n-6)===111&&e.charCodeAt(n-7)===99&&e.charCodeAt(n-8)===32&&e.charCodeAt(n-9)===101&&e.charCodeAt(n-10)===118&&e.charCodeAt(n-11)===105&&e.charCodeAt(n-12)===116&&e.charCodeAt(n-13)===97&&e.charCodeAt(n-14)===110&&e.charCodeAt(n-15)===88,r.set(o,t)),t}}(),Ot={};function ir(r){switch(typeof r){case"number":return r>=0&&(r|0)===r;case"string":{let t=Ot[r];if(t!==void 0)return t;let e=r.length;if(e===0)return Ot[r]=!1;let n=0;for(let o=0;o<e;++o)if(n=r.charCodeAt(o),o===0&&n===48&&e>1||n<48||n>57)return Ot[r]=!1;return Ot[r]=!0}default:return!1}}i(ir,"isArrayIndex");function sr(r){return`${r.toLowerCase()}:presentation`}i(sr,"presentationKeyFromTag");var Et=new Map,_t=Object.freeze({define(r,t,e){let n=sr(r);Et.get(n)===void 0?Et.set(n,t):Et.set(n,!1),e.register(W.instance(n,t))},forTag(r,t){let e=sr(r),n=Et.get(e);return n===!1?b.findResponsibleContainer(t).get(e):n||null}}),Dt=class{static{i(this,"DefaultComponentPresentation")}constructor(t,e){this.template=t||null,this.styles=e===void 0?null:Array.isArray(e)?x.create(e):e instanceof x?e:x.create([e])}applyTo(t){let e=t.$fastController;e.template===null&&(e.template=this.template),e.styles===null&&(e.styles=this.styles)}};var j=class r extends L{static{i(this,"FoundationElement")}constructor(){super(...arguments),this._presentation=void 0}get $presentation(){return this._presentation===void 0&&(this._presentation=_t.forTag(this.tagName,this)),this._presentation}templateChanged(){this.template!==void 0&&(this.$fastController.template=this.template)}stylesChanged(){this.styles!==void 0&&(this.$fastController.styles=this.styles)}connectedCallback(){this.$presentation!==null&&this.$presentation.applyTo(this),super.connectedCallback()}static compose(t){return(e={})=>new ae(this===r?class extends r{}:this,t,e)}};m([O],j.prototype,"template",void 0);m([O],j.prototype,"styles",void 0);function at(r,t,e){return typeof r=="function"?r(t,e):r}i(at,"resolveOption");var ae=class{static{i(this,"FoundationElementRegistry")}constructor(t,e,n){this.type=t,this.elementDefinition=e,this.overrideDefinition=n,this.definition=Object.assign(Object.assign({},this.elementDefinition),this.overrideDefinition)}register(t,e){let n=this.definition,o=this.overrideDefinition,a=`${n.prefix||e.elementPrefix}-${n.baseName}`;e.tryDefineElement({name:a,type:this.type,baseClass:this.elementDefinition.baseClass,callback:i(c=>{let u=new Dt(at(n.template,c,n),at(n.styles,c,n));c.definePresentation(u);let l=at(n.shadowOptions,c,n);c.shadowRootMode&&(l?o.shadowOptions||(l.mode=c.shadowRootMode):l!==null&&(l={mode:c.shadowRootMode})),c.defineElement({elementOptions:at(n.elementOptions,c,n),shadowOptions:l,attributes:at(n.attributes,c,n)})},"callback")})}};function ce(r,...t){let e=ot.locate(r);t.forEach(n=>{Object.getOwnPropertyNames(n.prototype).forEach(s=>{s!=="constructor"&&Object.defineProperty(r.prototype,s,Object.getOwnPropertyDescriptor(n.prototype,s))}),ot.locate(n).forEach(s=>e.push(s))})}i(ce,"applyMixins");function ar(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}i(ar,"canUseDOM");function wn(){let r=document.querySelector('meta[property="csp-nonce"]');return r?r.getAttribute("content"):null}i(wn,"getNonce");var G;function cr(){if(typeof G=="boolean")return G;if(!ar())return G=!1,G;let r=document.createElement("style"),t=wn();t!==null&&r.setAttribute("nonce",t),document.head.appendChild(r);try{r.sheet.insertRule("foo:focus-visible {color:inherit}",0),G=!0}catch{G=!1}finally{document.head.removeChild(r)}return G}i(cr,"canUseFocusVisible");var lr;(function(r){r[r.alt=18]="alt",r[r.arrowDown=40]="arrowDown",r[r.arrowLeft=37]="arrowLeft",r[r.arrowRight=39]="arrowRight",r[r.arrowUp=38]="arrowUp",r[r.back=8]="back",r[r.backSlash=220]="backSlash",r[r.break=19]="break",r[r.capsLock=20]="capsLock",r[r.closeBracket=221]="closeBracket",r[r.colon=186]="colon",r[r.colon2=59]="colon2",r[r.comma=188]="comma",r[r.ctrl=17]="ctrl",r[r.delete=46]="delete",r[r.end=35]="end",r[r.enter=13]="enter",r[r.equals=187]="equals",r[r.equals2=61]="equals2",r[r.equals3=107]="equals3",r[r.escape=27]="escape",r[r.forwardSlash=191]="forwardSlash",r[r.function1=112]="function1",r[r.function10=121]="function10",r[r.function11=122]="function11",r[r.function12=123]="function12",r[r.function2=113]="function2",r[r.function3=114]="function3",r[r.function4=115]="function4",r[r.function5=116]="function5",r[r.function6=117]="function6",r[r.function7=118]="function7",r[r.function8=119]="function8",r[r.function9=120]="function9",r[r.home=36]="home",r[r.insert=45]="insert",r[r.menu=93]="menu",r[r.minus=189]="minus",r[r.minus2=109]="minus2",r[r.numLock=144]="numLock",r[r.numPad0=96]="numPad0",r[r.numPad1=97]="numPad1",r[r.numPad2=98]="numPad2",r[r.numPad3=99]="numPad3",r[r.numPad4=100]="numPad4",r[r.numPad5=101]="numPad5",r[r.numPad6=102]="numPad6",r[r.numPad7=103]="numPad7",r[r.numPad8=104]="numPad8",r[r.numPad9=105]="numPad9",r[r.numPadDivide=111]="numPadDivide",r[r.numPadDot=110]="numPadDot",r[r.numPadMinus=109]="numPadMinus",r[r.numPadMultiply=106]="numPadMultiply",r[r.numPadPlus=107]="numPadPlus",r[r.openBracket=219]="openBracket",r[r.pageDown=34]="pageDown",r[r.pageUp=33]="pageUp",r[r.period=190]="period",r[r.print=44]="print",r[r.quote=222]="quote",r[r.scrollLock=145]="scrollLock",r[r.shift=16]="shift",r[r.space=32]="space",r[r.tab=9]="tab",r[r.tilde=192]="tilde",r[r.windowsLeft=91]="windowsLeft",r[r.windowsOpera=219]="windowsOpera",r[r.windowsRight=92]="windowsRight"})(lr||(lr={}));var ur="Enter";var v=class{static{i(this,"ARIAGlobalStatesAndProperties")}};m([p({attribute:"aria-atomic"})],v.prototype,"ariaAtomic",void 0);m([p({attribute:"aria-busy"})],v.prototype,"ariaBusy",void 0);m([p({attribute:"aria-controls"})],v.prototype,"ariaControls",void 0);m([p({attribute:"aria-current"})],v.prototype,"ariaCurrent",void 0);m([p({attribute:"aria-describedby"})],v.prototype,"ariaDescribedby",void 0);m([p({attribute:"aria-details"})],v.prototype,"ariaDetails",void 0);m([p({attribute:"aria-disabled"})],v.prototype,"ariaDisabled",void 0);m([p({attribute:"aria-errormessage"})],v.prototype,"ariaErrormessage",void 0);m([p({attribute:"aria-flowto"})],v.prototype,"ariaFlowto",void 0);m([p({attribute:"aria-haspopup"})],v.prototype,"ariaHaspopup",void 0);m([p({attribute:"aria-hidden"})],v.prototype,"ariaHidden",void 0);m([p({attribute:"aria-invalid"})],v.prototype,"ariaInvalid",void 0);m([p({attribute:"aria-keyshortcuts"})],v.prototype,"ariaKeyshortcuts",void 0);m([p({attribute:"aria-label"})],v.prototype,"ariaLabel",void 0);m([p({attribute:"aria-labelledby"})],v.prototype,"ariaLabelledby",void 0);m([p({attribute:"aria-live"})],v.prototype,"ariaLive",void 0);m([p({attribute:"aria-owns"})],v.prototype,"ariaOwns",void 0);m([p({attribute:"aria-relevant"})],v.prototype,"ariaRelevant",void 0);m([p({attribute:"aria-roledescription"})],v.prototype,"ariaRoledescription",void 0);var dr=i((r,t)=>q`
    <button
        class="control"
        part="control"
        ?autofocus="${e=>e.autofocus}"
        ?disabled="${e=>e.disabled}"
        form="${e=>e.formId}"
        formaction="${e=>e.formaction}"
        formenctype="${e=>e.formenctype}"
        formmethod="${e=>e.formmethod}"
        formnovalidate="${e=>e.formnovalidate}"
        formtarget="${e=>e.formtarget}"
        name="${e=>e.name}"
        type="${e=>e.type}"
        value="${e=>e.value}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-pressed="${e=>e.ariaPressed}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${$("control")}
    >
        ${Ge(r,t)}
        <span class="content" part="content">
            <slot ${Ue("defaultSlottedContent")}></slot>
        </span>
        ${We(r,t)}
    </button>
`,"buttonTemplate");var hr="form-associated-proxy",fr="ElementInternals",pr=fr in window&&"setFormValue"in window[fr].prototype,mr=new WeakMap;function gr(r){let t=class extends r{static{i(this,"C")}constructor(...e){super(...e),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return pr}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){let e=this.proxy.labels,n=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),o=e?n.concat(Array.from(e)):n;return Object.freeze(o)}else return F}valueChanged(e,n){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(e,n){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(e,n){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),f.queueUpdate(()=>this.classList.toggle("disabled",this.disabled))}nameChanged(e,n){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(e,n){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),f.queueUpdate(()=>this.classList.toggle("required",this.required)),this.validate()}get elementInternals(){if(!pr)return null;let e=mr.get(this);return e||(e=this.attachInternals(),mr.set(this,e)),e}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){super.disconnectedCallback(),this.proxyEventsToBlock.forEach(e=>this.proxy.removeEventListener(e,this.stopPropagation)),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(e,n,o){this.elementInternals?this.elementInternals.setValidity(e,n,o):typeof n=="string"&&this.proxy.setCustomValidity(n)}formDisabledCallback(e){this.disabled=e}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var e;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach(n=>this.proxy.addEventListener(n,this.stopPropagation)),this.proxy.disabled=this.disabled,this.proxy.required=this.required,typeof this.name=="string"&&(this.proxy.name=this.name),typeof this.value=="string"&&(this.proxy.value=this.value),this.proxy.setAttribute("slot",hr),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name",hr)),(e=this.shadowRoot)===null||e===void 0||e.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var e;this.removeChild(this.proxy),(e=this.shadowRoot)===null||e===void 0||e.removeChild(this.proxySlot)}validate(e){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,e)}setFormValue(e,n){this.elementInternals&&this.elementInternals.setFormValue(e,n||e)}_keypressHandler(e){switch(e.key){case ur:if(this.form instanceof HTMLFormElement){let n=this.form.querySelector("[type=submit]");n?.click()}break}}stopPropagation(e){e.stopPropagation()}};return p({mode:"boolean"})(t.prototype,"disabled"),p({mode:"fromView",attribute:"value"})(t.prototype,"initialValue"),p({attribute:"current-value"})(t.prototype,"currentValue"),p(t.prototype,"name"),p({mode:"boolean"})(t.prototype,"required"),O(t.prototype,"value"),t}i(gr,"FormAssociated");var le=class extends j{static{i(this,"_Button")}},At=class extends gr(le){static{i(this,"FormAssociatedButton")}constructor(){super(...arguments),this.proxy=document.createElement("input")}};var P=class extends At{static{i(this,"Button")}constructor(){super(...arguments),this.handleClick=t=>{var e;this.disabled&&((e=this.defaultSlottedContent)===null||e===void 0?void 0:e.length)<=1&&t.stopPropagation()},this.handleSubmission=()=>{if(!this.form)return;let t=this.proxy.isConnected;t||this.attachProxy(),typeof this.form.requestSubmit=="function"?this.form.requestSubmit(this.proxy):this.proxy.click(),t||this.detachProxy()},this.handleFormReset=()=>{var t;(t=this.form)===null||t===void 0||t.reset()},this.handleUnsupportedDelegatesFocus=()=>{var t;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(!((t=this.$fastController.definition.shadowOptions)===null||t===void 0)&&t.delegatesFocus)&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(t,e){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),e==="submit"&&this.addEventListener("click",this.handleSubmission),t==="submit"&&this.removeEventListener("click",this.handleSubmission),e==="reset"&&this.addEventListener("click",this.handleFormReset),t==="reset"&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){var t;super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus();let e=Array.from((t=this.control)===null||t===void 0?void 0:t.children);e&&e.forEach(n=>{n.addEventListener("click",this.handleClick)})}disconnectedCallback(){var t;super.disconnectedCallback();let e=Array.from((t=this.control)===null||t===void 0?void 0:t.children);e&&e.forEach(n=>{n.removeEventListener("click",this.handleClick)})}};m([p({mode:"boolean"})],P.prototype,"autofocus",void 0);m([p({attribute:"form"})],P.prototype,"formId",void 0);m([p],P.prototype,"formaction",void 0);m([p],P.prototype,"formenctype",void 0);m([p],P.prototype,"formmethod",void 0);m([p({mode:"boolean"})],P.prototype,"formnovalidate",void 0);m([p],P.prototype,"formtarget",void 0);m([p],P.prototype,"type",void 0);m([O],P.prototype,"defaultSlottedContent",void 0);var et=class{static{i(this,"DelegatesARIAButton")}};m([p({attribute:"aria-expanded"})],et.prototype,"ariaExpanded",void 0);m([p({attribute:"aria-pressed"})],et.prototype,"ariaPressed",void 0);ce(et,v);ce(P,Pt,et);function ct(r){let t=r.parentElement;if(t)return t;{let e=r.getRootNode();if(e.host instanceof HTMLElement)return e.host}return null}i(ct,"composedParent");function br(r,t){let e=t;for(;e!==null;){if(e===r)return!0;e=ct(e)}return!1}i(br,"composedContains");var _=document.createElement("div");function Sn(r){return r instanceof L}i(Sn,"isFastElement");var lt=class{static{i(this,"QueuedStyleSheetTarget")}setProperty(t,e){f.queueUpdate(()=>this.target.setProperty(t,e))}removeProperty(t){f.queueUpdate(()=>this.target.removeProperty(t))}},de=class extends lt{static{i(this,"ConstructableStyleSheetTarget")}constructor(t){super();let e=new CSSStyleSheet;e[wt]=!0,this.target=e.cssRules[e.insertRule(":host{}")].style,t.$fastController.addStyles(x.create([e]))}},he=class extends lt{static{i(this,"DocumentStyleSheetTarget")}constructor(){super();let t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,t]}},fe=class extends lt{static{i(this,"HeadStyleElementStyleSheetTarget")}constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);let{sheet:t}=this.style;if(t){let e=t.insertRule(":root{}",t.cssRules.length);this.target=t.cssRules[e].style}}},Bt=class{static{i(this,"StyleElementStyleSheetTarget")}constructor(t){this.store=new Map,this.target=null;let e=t.$fastController;this.style=document.createElement("style"),e.addStyles(this.style),y.getNotifier(e).subscribe(this,"isConnected"),this.handleChange(e,"isConnected")}targetChanged(){if(this.target!==null)for(let[t,e]of this.store.entries())this.target.setProperty(t,e)}setProperty(t,e){this.store.set(t,e),f.queueUpdate(()=>{this.target!==null&&this.target.setProperty(t,e)})}removeProperty(t){this.store.delete(t),f.queueUpdate(()=>{this.target!==null&&this.target.removeProperty(t)})}handleChange(t,e){let{sheet:n}=this.style;if(n){let o=n.insertRule(":host{}",n.cssRules.length);this.target=n.cssRules[o].style}else this.target=null}};m([O],Bt.prototype,"target",void 0);var pe=class{static{i(this,"ElementStyleSheetTarget")}constructor(t){this.target=t.style}setProperty(t,e){f.queueUpdate(()=>this.target.setProperty(t,e))}removeProperty(t){f.queueUpdate(()=>this.target.removeProperty(t))}},N=class r{static{i(this,"RootStyleSheetTarget")}setProperty(t,e){r.properties[t]=e;for(let n of r.roots.values())Q.getOrCreate(r.normalizeRoot(n)).setProperty(t,e)}removeProperty(t){delete r.properties[t];for(let e of r.roots.values())Q.getOrCreate(r.normalizeRoot(e)).removeProperty(t)}static registerRoot(t){let{roots:e}=r;if(!e.has(t)){e.add(t);let n=Q.getOrCreate(this.normalizeRoot(t));for(let o in r.properties)n.setProperty(o,r.properties[o])}}static unregisterRoot(t){let{roots:e}=r;if(e.has(t)){e.delete(t);let n=Q.getOrCreate(r.normalizeRoot(t));for(let o in r.properties)n.removeProperty(o)}}static normalizeRoot(t){return t===_?document:t}};N.roots=new Set;N.properties={};var ue=new WeakMap,Cn=f.supportsAdoptedStyleSheets?de:Bt,Q=Object.freeze({getOrCreate(r){if(ue.has(r))return ue.get(r);let t;return r===_?t=new N:r instanceof Document?t=f.supportsAdoptedStyleSheets?new he:new fe:Sn(r)?t=new Cn(r):t=new pe(r),ue.set(r,t),t}});var R=class r extends z{static{i(this,"DesignTokenImpl")}constructor(t){super(),this.subscribers=new WeakMap,this._appliedTo=new Set,this.name=t.name,t.cssCustomPropertyName!==null&&(this.cssCustomProperty=`--${t.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`),this.id=r.uniqueId(),r.tokensById.set(this.id,this)}get appliedTo(){return[...this._appliedTo]}static from(t){return new r({name:typeof t=="string"?t:t.name,cssCustomPropertyName:typeof t=="string"?t:t.cssCustomPropertyName===void 0?t.name:t.cssCustomPropertyName})}static isCSSDesignToken(t){return typeof t.cssCustomProperty=="string"}static isDerivedDesignTokenValue(t){return typeof t=="function"}static getTokenById(t){return r.tokensById.get(t)}getOrCreateSubscriberSet(t=this){return this.subscribers.get(t)||this.subscribers.set(t,new Set)&&this.subscribers.get(t)}createCSS(){return this.cssVar||""}getValueFor(t){let e=k.getOrCreate(t).get(this);if(e!==void 0)return e;throw new Error(`Value could not be retrieved for token named "${this.name}". Ensure the value is set for ${t} or an ancestor of ${t}.`)}setValueFor(t,e){return this._appliedTo.add(t),e instanceof r&&(e=this.alias(e)),k.getOrCreate(t).set(this,e),this}deleteValueFor(t){return this._appliedTo.delete(t),k.existsFor(t)&&k.getOrCreate(t).delete(this),this}withDefault(t){return this.setValueFor(_,t),this}subscribe(t,e){let n=this.getOrCreateSubscriberSet(e);e&&!k.existsFor(e)&&k.getOrCreate(e),n.has(t)||n.add(t)}unsubscribe(t,e){let n=this.subscribers.get(e||this);n&&n.has(t)&&n.delete(t)}notify(t){let e=Object.freeze({token:this,target:t});this.subscribers.has(this)&&this.subscribers.get(this).forEach(n=>n.handleChange(e)),this.subscribers.has(t)&&this.subscribers.get(t).forEach(n=>n.handleChange(e))}alias(t){return e=>t.getValueFor(e)}};R.uniqueId=(()=>{let r=0;return()=>(r++,r.toString(16))})();R.tokensById=new Map;var me=class{static{i(this,"CustomPropertyReflector")}startReflection(t,e){t.subscribe(this,e),this.handleChange({token:t,target:e})}stopReflection(t,e){t.unsubscribe(this,e),this.remove(t,e)}handleChange(t){let{token:e,target:n}=t;this.add(e,n)}add(t,e){Q.getOrCreate(e).setProperty(t.cssCustomProperty,this.resolveCSSValue(k.getOrCreate(e).get(t)))}remove(t,e){Q.getOrCreate(e).removeProperty(t.cssCustomProperty)}resolveCSSValue(t){return t&&typeof t.createCSS=="function"?t.createCSS():t}},ge=class{static{i(this,"DesignTokenBindingObserver")}constructor(t,e,n){this.source=t,this.token=e,this.node=n,this.dependencies=new Set,this.observer=y.binding(t,this,!1),this.observer.handleChange=this.observer.call,this.handleChange()}disconnect(){this.observer.disconnect()}handleChange(){this.node.store.set(this.token,this.observer.observe(this.node.target,M))}},be=class{static{i(this,"Store")}constructor(){this.values=new Map}set(t,e){this.values.get(t)!==e&&(this.values.set(t,e),y.getNotifier(this).notify(t.id))}get(t){return y.track(this,t.id),this.values.get(t)}delete(t){this.values.delete(t)}all(){return this.values.entries()}},ut=new WeakMap,dt=new WeakMap,k=class r{static{i(this,"DesignTokenNode")}constructor(t){this.target=t,this.store=new be,this.children=[],this.assignedValues=new Map,this.reflecting=new Set,this.bindingObservers=new Map,this.tokenValueChangeHandler={handleChange:i((e,n)=>{let o=R.getTokenById(n);o&&(o.notify(this.target),this.updateCSSTokenReflection(e,o))},"handleChange")},ut.set(t,this),y.getNotifier(this.store).subscribe(this.tokenValueChangeHandler),t instanceof L?t.$fastController.addBehaviors([this]):t.isConnected&&this.bind()}static getOrCreate(t){return ut.get(t)||new r(t)}static existsFor(t){return ut.has(t)}static findParent(t){if(_!==t.target){let e=ct(t.target);for(;e!==null;){if(ut.has(e))return ut.get(e);e=ct(e)}return r.getOrCreate(_)}return null}static findClosestAssignedNode(t,e){let n=e;do{if(n.has(t))return n;n=n.parent?n.parent:n.target!==_?r.getOrCreate(_):null}while(n!==null);return null}get parent(){return dt.get(this)||null}updateCSSTokenReflection(t,e){if(R.isCSSDesignToken(e)){let n=this.parent,o=this.isReflecting(e);if(n){let s=n.get(e),a=t.get(e);s!==a&&!o?this.reflectToCSS(e):s===a&&o&&this.stopReflectToCSS(e)}else o||this.reflectToCSS(e)}}has(t){return this.assignedValues.has(t)}get(t){let e=this.store.get(t);if(e!==void 0)return e;let n=this.getRaw(t);if(n!==void 0)return this.hydrate(t,n),this.get(t)}getRaw(t){var e;return this.assignedValues.has(t)?this.assignedValues.get(t):(e=r.findClosestAssignedNode(t,this))===null||e===void 0?void 0:e.getRaw(t)}set(t,e){R.isDerivedDesignTokenValue(this.assignedValues.get(t))&&this.tearDownBindingObserver(t),this.assignedValues.set(t,e),R.isDerivedDesignTokenValue(e)?this.setupBindingObserver(t,e):this.store.set(t,e)}delete(t){this.assignedValues.delete(t),this.tearDownBindingObserver(t);let e=this.getRaw(t);e?this.hydrate(t,e):this.store.delete(t)}bind(){let t=r.findParent(this);t&&t.appendChild(this);for(let e of this.assignedValues.keys())e.notify(this.target)}unbind(){this.parent&&dt.get(this).removeChild(this)}appendChild(t){t.parent&&dt.get(t).removeChild(t);let e=this.children.filter(n=>t.contains(n));dt.set(t,this),this.children.push(t),e.forEach(n=>t.appendChild(n)),y.getNotifier(this.store).subscribe(t);for(let[n,o]of this.store.all())t.hydrate(n,this.bindingObservers.has(n)?this.getRaw(n):o)}removeChild(t){let e=this.children.indexOf(t);return e!==-1&&this.children.splice(e,1),y.getNotifier(this.store).unsubscribe(t),t.parent===this?dt.delete(t):!1}contains(t){return br(this.target,t.target)}reflectToCSS(t){this.isReflecting(t)||(this.reflecting.add(t),r.cssCustomPropertyReflector.startReflection(t,this.target))}stopReflectToCSS(t){this.isReflecting(t)&&(this.reflecting.delete(t),r.cssCustomPropertyReflector.stopReflection(t,this.target))}isReflecting(t){return this.reflecting.has(t)}handleChange(t,e){let n=R.getTokenById(e);n&&(this.hydrate(n,this.getRaw(n)),this.updateCSSTokenReflection(this.store,n))}hydrate(t,e){if(!this.has(t)){let n=this.bindingObservers.get(t);R.isDerivedDesignTokenValue(e)?n?n.source!==e&&(this.tearDownBindingObserver(t),this.setupBindingObserver(t,e)):this.setupBindingObserver(t,e):(n&&this.tearDownBindingObserver(t),this.store.set(t,e))}}setupBindingObserver(t,e){let n=new ge(e,t,this);return this.bindingObservers.set(t,n),n}tearDownBindingObserver(t){return this.bindingObservers.has(t)?(this.bindingObservers.get(t).disconnect(),this.bindingObservers.delete(t),!0):!1}};k.cssCustomPropertyReflector=new me;m([O],k.prototype,"children",void 0);function kn(r){return R.from(r)}i(kn,"create");var ht=Object.freeze({create:kn,notifyConnection(r){return!r.isConnected||!k.existsFor(r)?!1:(k.getOrCreate(r).bind(),!0)},notifyDisconnection(r){return r.isConnected||!k.existsFor(r)?!1:(k.getOrCreate(r).unbind(),!0)},registerRoot(r=_){N.registerRoot(r)},unregisterRoot(r=_){N.unregisterRoot(r)}});var ye=Object.freeze({definitionCallbackOnly:null,ignoreDuplicate:Symbol()}),ve=new Map,Ft=new Map,rt=null,ft=b.createInterface(r=>r.cachedCallback(t=>(rt===null&&(rt=new It(null,t)),rt))),we=Object.freeze({tagFor(r){return Ft.get(r)},responsibleFor(r){let t=r.$$designSystem$$;return t||b.findResponsibleContainer(r).get(ft)},getOrCreate(r){if(!r)return rt===null&&(rt=b.getOrCreateDOMContainer().get(ft)),rt;let t=r.$$designSystem$$;if(t)return t;let e=b.getOrCreateDOMContainer(r);if(e.has(ft,!1))return e.get(ft);{let n=new It(r,e);return e.register(W.instance(ft,n)),n}}});function Pn(r,t,e){return typeof r=="string"?{name:r,type:t,callback:e}:r}i(Pn,"extractTryDefineElementParams");var It=class{static{i(this,"DefaultDesignSystem")}constructor(t,e){this.owner=t,this.container=e,this.designTokensInitialized=!1,this.prefix="fast",this.shadowRootMode=void 0,this.disambiguate=()=>ye.definitionCallbackOnly,t!==null&&(t.$$designSystem$$=this)}withPrefix(t){return this.prefix=t,this}withShadowRootMode(t){return this.shadowRootMode=t,this}withElementDisambiguation(t){return this.disambiguate=t,this}withDesignTokenRoot(t){return this.designTokenRoot=t,this}register(...t){let e=this.container,n=[],o=this.disambiguate,s=this.shadowRootMode,a={elementPrefix:this.prefix,tryDefineElement(c,u,l){let h=Pn(c,u,l),{name:g,callback:w,baseClass:E}=h,{type:S}=h,T=g,gt=ve.get(T),jt=!0;for(;gt;){let Re=o(T,S,gt);switch(Re){case ye.ignoreDuplicate:return;case ye.definitionCallbackOnly:jt=!1,gt=void 0;break;default:T=Re,gt=ve.get(T);break}}jt&&((Ft.has(S)||S===j)&&(S=class extends S{static{i(this,"type")}}),ve.set(T,S),Ft.set(S,T),E&&Ft.set(E,T)),n.push(new xe(e,T,S,s,w,jt))}};this.designTokensInitialized||(this.designTokensInitialized=!0,this.designTokenRoot!==null&&ht.registerRoot(this.designTokenRoot)),e.registerWithContext(a,...t);for(let c of n)c.callback(c),c.willDefine&&c.definition!==null&&c.definition.define();return this}},xe=class{static{i(this,"ElementDefinitionEntry")}constructor(t,e,n,o,s,a){this.container=t,this.name=e,this.type=n,this.shadowRootMode=o,this.callback=s,this.willDefine=a,this.definition=null}definePresentation(t){_t.define(this.name,t,this.container)}defineElement(t){this.definition=new D(this.type,Object.assign(Object.assign({},t),{name:this.name}))}tagFor(t){return we.tagFor(t)}};var yr="not-allowed";var Tn=":host([hidden]){display:none}";function vr(r){return`${Tn}:host{display:${r}}`}i(vr,"display");var pt=cr()?"focus-visible":"focus";function xr(r){return we.getOrCreate(r).withPrefix("vscode")}i(xr,"provideVSCodeDesignSystem");function Sr(r){window.addEventListener("load",()=>{new MutationObserver(()=>{wr(r)}).observe(document.body,{attributes:!0,attributeFilter:["class"]}),wr(r)})}i(Sr,"initThemeChangeListener");function wr(r){let t=getComputedStyle(document.body),e=document.querySelector("body");if(e){let n=e.getAttribute("data-vscode-theme-kind");for(let[o,s]of r){let a=t.getPropertyValue(o).toString();if(n==="vscode-high-contrast")a.length===0&&s.name.includes("background")&&(a="transparent"),s.name==="button-icon-hover-background"&&(a="transparent");else if(n==="vscode-high-contrast-light"){if(a.length===0&&s.name.includes("background"))switch(s.name){case"button-primary-hover-background":a="#0F4A85";break;case"button-secondary-hover-background":a="transparent";break;case"button-icon-hover-background":a="transparent";break}}else s.name==="contrast-active-border"&&(a="transparent");s.setValueFor(e,a)}}}i(wr,"applyCurrentTheme");var Cr=new Map,kr=!1;function d(r,t){let e=ht.create(r);if(t){if(t.includes("--fake-vscode-token")){let n="id"+Math.random().toString(16).slice(2);t=`${t}-${n}`}Cr.set(t,e)}return kr||(Sr(Cr),kr=!0),e}i(d,"create");var xc=d("background","--vscode-editor-background").withDefault("#1e1e1e"),B=d("border-width").withDefault(1),Pr=d("contrast-active-border","--vscode-contrastActiveBorder").withDefault("#f38518"),wc=d("contrast-border","--vscode-contrastBorder").withDefault("#6fc3df"),Sc=d("corner-radius").withDefault(0),Tr=d("corner-radius-round").withDefault(2),Se=d("design-unit").withDefault(4),Or=d("disabled-opacity").withDefault(.4),mt=d("focus-border","--vscode-focusBorder").withDefault("#007fd4"),$r=d("font-family","--vscode-font-family").withDefault("-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol"),Cc=d("font-weight","--vscode-font-weight").withDefault("400"),Rr=d("foreground","--vscode-foreground").withDefault("#cccccc"),kc=d("input-height").withDefault("26"),Pc=d("input-min-width").withDefault("100px"),Er=d("type-ramp-base-font-size","--vscode-font-size").withDefault("13px"),Dr=d("type-ramp-base-line-height").withDefault("normal"),Tc=d("type-ramp-minus1-font-size").withDefault("11px"),Oc=d("type-ramp-minus1-line-height").withDefault("16px"),$c=d("type-ramp-minus2-font-size").withDefault("9px"),Rc=d("type-ramp-minus2-line-height").withDefault("16px"),Ec=d("type-ramp-plus1-font-size").withDefault("16px"),Dc=d("type-ramp-plus1-line-height").withDefault("24px"),_c=d("scrollbarWidth").withDefault("10px"),Ac=d("scrollbarHeight").withDefault("10px"),Bc=d("scrollbar-slider-background","--vscode-scrollbarSlider-background").withDefault("#79797966"),Fc=d("scrollbar-slider-hover-background","--vscode-scrollbarSlider-hoverBackground").withDefault("#646464b3"),Ic=d("scrollbar-slider-active-background","--vscode-scrollbarSlider-activeBackground").withDefault("#bfbfbf66"),Mc=d("badge-background","--vscode-badge-background").withDefault("#4d4d4d"),Lc=d("badge-foreground","--vscode-badge-foreground").withDefault("#ffffff"),_r=d("button-border","--vscode-button-border").withDefault("transparent"),Ce=d("button-icon-background").withDefault("transparent"),Ar=d("button-icon-corner-radius").withDefault("5px"),Br=d("button-icon-outline-offset").withDefault(0),ke=d("button-icon-hover-background","--fake-vscode-token").withDefault("rgba(90, 93, 94, 0.31)"),Fr=d("button-icon-padding").withDefault("3px"),J=d("button-primary-background","--vscode-button-background").withDefault("#0e639c"),Pe=d("button-primary-foreground","--vscode-button-foreground").withDefault("#ffffff"),Te=d("button-primary-hover-background","--vscode-button-hoverBackground").withDefault("#1177bb"),Mt=d("button-secondary-background","--vscode-button-secondaryBackground").withDefault("#3a3d41"),Ir=d("button-secondary-foreground","--vscode-button-secondaryForeground").withDefault("#ffffff"),Mr=d("button-secondary-hover-background","--vscode-button-secondaryHoverBackground").withDefault("#45494e"),Lr=d("button-padding-horizontal").withDefault("11px"),jr=d("button-padding-vertical").withDefault("4px"),jc=d("checkbox-background","--vscode-checkbox-background").withDefault("#3c3c3c"),Nc=d("checkbox-border","--vscode-checkbox-border").withDefault("#3c3c3c"),Vc=d("checkbox-corner-radius").withDefault(3),Hc=d("checkbox-foreground","--vscode-checkbox-foreground").withDefault("#f0f0f0"),qc=d("list-active-selection-background","--vscode-list-activeSelectionBackground").withDefault("#094771"),zc=d("list-active-selection-foreground","--vscode-list-activeSelectionForeground").withDefault("#ffffff"),Uc=d("list-hover-background","--vscode-list-hoverBackground").withDefault("#2a2d2e"),Wc=d("divider-background","--vscode-settings-dropdownListBorder").withDefault("#454545"),Gc=d("dropdown-background","--vscode-dropdown-background").withDefault("#3c3c3c"),Qc=d("dropdown-border","--vscode-dropdown-border").withDefault("#3c3c3c"),Jc=d("dropdown-foreground","--vscode-dropdown-foreground").withDefault("#f0f0f0"),Xc=d("dropdown-list-max-height").withDefault("200px"),Yc=d("input-background","--vscode-input-background").withDefault("#3c3c3c"),Zc=d("input-foreground","--vscode-input-foreground").withDefault("#cccccc"),Kc=d("input-placeholder-foreground","--vscode-input-placeholderForeground").withDefault("#cccccc"),tl=d("link-active-foreground","--vscode-textLink-activeForeground").withDefault("#3794ff"),el=d("link-foreground","--vscode-textLink-foreground").withDefault("#3794ff"),rl=d("progress-background","--vscode-progressBar-background").withDefault("#0e70c0"),nl=d("panel-tab-active-border","--vscode-panelTitle-activeBorder").withDefault("#e7e7e7"),ol=d("panel-tab-active-foreground","--vscode-panelTitle-activeForeground").withDefault("#e7e7e7"),il=d("panel-tab-foreground","--vscode-panelTitle-inactiveForeground").withDefault("#e7e7e799"),sl=d("panel-view-background","--vscode-panel-background").withDefault("#1e1e1e"),al=d("panel-view-border","--vscode-panel-border").withDefault("#80808059"),cl=d("tag-corner-radius").withDefault("2px");function Nr(r,t,e,n){var o=arguments.length,s=o<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,e):n,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(r,t,e,n);else for(var c=r.length-1;c>=0;c--)(a=r[c])&&(s=(o<3?a(s):o>3?a(t,e,s):a(t,e))||s);return o>3&&s&&Object.defineProperty(t,e,s),s}i(Nr,"__decorate");var On=U`
	${vr("inline-flex")} :host {
		outline: none;
		font-family: ${$r};
		font-size: ${Er};
		line-height: ${Dr};
		color: ${Pe};
		background: ${J};
		border-radius: calc(${Tr} * 1px);
		fill: currentColor;
		cursor: pointer;
	}
	.control {
		background: transparent;
		height: inherit;
		flex-grow: 1;
		box-sizing: border-box;
		display: inline-flex;
		justify-content: center;
		align-items: center;
		padding: ${jr} ${Lr};
		white-space: wrap;
		outline: none;
		text-decoration: none;
		border: calc(${B} * 1px) solid ${_r};
		color: inherit;
		border-radius: inherit;
		fill: inherit;
		cursor: inherit;
		font-family: inherit;
	}
	:host(:hover) {
		background: ${Te};
	}
	:host(:active) {
		background: ${J};
	}
	.control:${pt} {
		outline: calc(${B} * 1px) solid ${mt};
		outline-offset: calc(${B} * 2px);
	}
	.control::-moz-focus-inner {
		border: 0;
	}
	:host([disabled]) {
		opacity: ${Or};
		background: ${J};
		cursor: ${yr};
	}
	.content {
		display: flex;
	}
	.start {
		display: flex;
	}
	::slotted(svg),
	::slotted(span) {
		width: calc(${Se} * 4px);
		height: calc(${Se} * 4px);
	}
	.start {
		margin-inline-end: 8px;
	}
`,$n=U`
	:host([appearance='primary']) {
		background: ${J};
		color: ${Pe};
	}
	:host([appearance='primary']:hover) {
		background: ${Te};
	}
	:host([appearance='primary']:active) .control:active {
		background: ${J};
	}
	:host([appearance='primary']) .control:${pt} {
		outline: calc(${B} * 1px) solid ${mt};
		outline-offset: calc(${B} * 2px);
	}
	:host([appearance='primary'][disabled]) {
		background: ${J};
	}
`,Rn=U`
	:host([appearance='secondary']) {
		background: ${Mt};
		color: ${Ir};
	}
	:host([appearance='secondary']:hover) {
		background: ${Mr};
	}
	:host([appearance='secondary']:active) .control:active {
		background: ${Mt};
	}
	:host([appearance='secondary']) .control:${pt} {
		outline: calc(${B} * 1px) solid ${mt};
		outline-offset: calc(${B} * 2px);
	}
	:host([appearance='secondary'][disabled]) {
		background: ${Mt};
	}
`,En=U`
	:host([appearance='icon']) {
		background: ${Ce};
		border-radius: ${Ar};
		color: ${Rr};
	}
	:host([appearance='icon']:hover) {
		background: ${ke};
		outline: 1px dotted ${Pr};
		outline-offset: -1px;
	}
	:host([appearance='icon']) .control {
		padding: ${Fr};
		border: none;
	}
	:host([appearance='icon']:active) .control:active {
		background: ${ke};
	}
	:host([appearance='icon']) .control:${pt} {
		outline: calc(${B} * 1px) solid ${mt};
		outline-offset: ${Br};
	}
	:host([appearance='icon'][disabled]) {
		background: ${Ce};
	}
`,Vr=i((r,t)=>U`
	${On}
	${$n}
	${Rn}
	${En}
`,"buttonStyles");var Lt=class extends P{static{i(this,"Button")}connectedCallback(){if(super.connectedCallback(),!this.appearance){let t=this.getAttribute("appearance");this.appearance=t}}attributeChangedCallback(t,e,n){t==="appearance"&&n==="icon"&&(this.getAttribute("aria-label")||(this.ariaLabel="Icon Button")),t==="aria-label"&&(this.ariaLabel=n),t==="disabled"&&(this.disabled=n!==null)}};Nr([p],Lt.prototype,"appearance",void 0);var Oe=Lt.compose({baseName:"button",template:dr,styles:Vr,shadowOptions:{delegatesFocus:!0}});var Hr=document.getElementById("solutionsContainer"),zr=acquireVsCodeApi(),$e;xr().register(Oe());window.addEventListener("message",function(r){let t=r.data;switch(t.command){case"solutionsUpdated":Dn(t);break;case"navigatePreviousSolution":_n();break;case"navigateNextSolution":An();break}});function Dn(r){Bn(r),Hr&&(Hr.innerHTML=r.solutions.map((t,e)=>{let n=t.citation?`<p>
                        <span class="codicon codicon-warning" style="vertical-align: text-bottom" aria-hidden="true"></span>
                        ${t.citation.message}
                        <a href="${t.citation.url}" target="_blank">Inspect source code</a>
                      </p>`:"";return`<h3 class='solutionHeading' id="solution-${e+1}-heading">Suggestion ${e+1}</h3>
                <div class='snippetContainer' aria-labelledby="solution-${e+1}-heading" role="group">${t.htmlSnippet}</div>
                ${n}
                <vscode-button role="button" class="acceptButton" id="acceptButton${e}" appearance="secondary">Accept suggestion ${e+1}</vscode-button>`}).join("")),Fn(),Mn()}i(Dn,"handleSolutionUpdate");function _n(){let r=document.querySelectorAll(".snippetContainer pre"),t=$e-1;r[t]?.focus()}i(_n,"navigatePreviousSolution");function An(){let r=document.querySelectorAll(".snippetContainer pre"),t=($e??-1)+1;r[t]?r[t].focus():r[0]&&r[0].focus()}i(An,"navigateNextSolution");function Bn(r){let t=document.getElementById("progress-bar"),e=document.getElementById("loadingContainer");if(!(!t||!e))if(r.percentage>=100)e.innerHTML=`${r.solutions.length} Suggestions`;else{let n=e.querySelector("label");n.textContent!=="Loading suggestions:\xA0"&&(n.textContent="Loading suggestions:\xA0"),t.value=r.percentage}}i(Bn,"updateLoadingContainer");function qr(r){$e=r,zr.postMessage({command:"focusSolution",solutionIndex:r})}i(qr,"handleFocus");function Fn(){document.querySelectorAll(".snippetContainer pre").forEach((t,e)=>{t.addEventListener("focus",()=>qr(e));let n=document.getElementById(`acceptButton${e}`);n&&n.addEventListener("focus",()=>qr(e))})}i(Fn,"addFocusHandlers");function In(r){zr.postMessage({command:"acceptSolution",solutionIndex:r})}i(In,"handleClick");function Mn(){document.querySelectorAll(".acceptButton").forEach((t,e)=>{t.addEventListener("click",()=>In(e))})}i(Mn,"addClickHandlers");})();
/*! Bundled license information:

tslib/tslib.es6.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  
  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  
  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** *)
*/
//# sourceMappingURL=suggestionsPanelWebview.js.map
