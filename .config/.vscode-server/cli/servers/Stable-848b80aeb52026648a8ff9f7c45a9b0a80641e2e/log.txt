*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[06:00:33] 




[06:00:33] Extension host agent started.
[06:00:33] [<unknown>][f788b5d5][ExtensionHostConnection] New connection established.
[06:00:33] [<unknown>][21f89f3d][ManagementConnection] New connection established.
[06:00:34] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.323.0
[06:00:34] [<unknown>][f788b5d5][ExtensionHostConnection] <499> Launched Extension Host Process.
[06:00:34] ComputeTargetPlatform: linux-x64
[06:00:36] ComputeTargetPlatform: linux-x64
[06:00:48] [File Watcher] Unexpected error: inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250522T183610/exthost1/vscode.git' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/workspace)
[06:00:48] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250522T183610/exthost1/vscode.git' failed: No such file or directory
