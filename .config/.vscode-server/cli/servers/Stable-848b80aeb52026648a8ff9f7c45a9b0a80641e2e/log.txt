*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[21:34:43] 




[21:34:43] Extension host agent started.
[21:34:44] [<unknown>][72112737][ManagementConnection] Unknown reconnection token (never seen).
[21:34:55] [<unknown>][ab80c03c][ExtensionHostConnection] New connection established.
[21:34:55] [<unknown>][94947349][ManagementConnection] New connection established.
[21:34:55] [<unknown>][ab80c03c][ExtensionHostConnection] <242> Launched Extension Host Process.
[21:34:55] ComputeTargetPlatform: linux-x64
[21:34:59] ComputeTargetPlatform: linux-x64
[21:35:00] Getting Manifest... github.copilot
[21:35:00] Installing extension: github.copilot {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[21:35:00] Getting Manifest... github.copilot-chat
[21:35:03] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1959ms.
[21:35:05] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.325.0: github.copilot
[21:35:05] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.325.0
[21:35:05] Marked extension as removed github.copilot-1.323.0
[21:35:05] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5001
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[21:35:45] Error: connect ECONNREFUSED 127.0.0.1:5001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5001
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5001
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[21:35:45] Error: connect ECONNREFUSED 127.0.0.1:5001
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5001
}
